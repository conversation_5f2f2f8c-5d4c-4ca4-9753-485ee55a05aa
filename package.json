{"name": "homeprofit", "version": "2.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~54.0.7", "expo-status-bar": "~3.0.8", "react": "19.1.0", "react-native": "0.81.4", "@bugsnag/react-native": "^8.4.0", "@emotion/native": "^11.11.0", "@emotion/react": "^11.14.0", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^5.2.2", "@invertase/react-native-apple-authentication": "^2.4.1", "@jonasmerlin/react-native-markdown-display": "^1.1.0", "@kichiyaki/react-native-barcode-generator": "^0.6.7", "@notifee/react-native": "^9.1.8", "@openspacelabs/react-native-zoomable-view": "^2.4.2", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-clipboard/clipboard": "1.16.3", "@react-native-community/netinfo": "^11.4.1", "@react-native-firebase/analytics": "23.3.1", "@react-native-firebase/app": "23.3.1", "@react-native-firebase/auth": "23.3.1", "@react-native-firebase/messaging": "23.3.1", "@react-native-google-signin/google-signin": "^16.0.0", "@react-native/metro-config": "^0.81.1", "@react-navigation/native": "^7.1.17", "@react-navigation/native-stack": "^7.3.26", "@reduxjs/toolkit": "^2.9.0", "@types/crypto-js": "^4.2.2", "async-mutex": "^0.5.0", "axios": "^1.12.2", "crypto-js": "^4.2.0", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "react-hook-form": "^7.62.0", "react-native-blob-util": "^0.22.2", "react-native-device-info": "^14.0.4", "react-native-fast-image": "^8.6.3", "react-native-keyboard-manager": "^6.5.16-0", "react-native-linear-gradient": "^2.8.3", "react-native-pdf": "^6.7.7", "react-native-qrcode-svg": "^6.3.15", "react-native-safe-area-context": "^5.6.1", "react-native-screens": "^4.16.0", "react-native-share": "^12.2.0", "react-native-svg": "^15.13.0", "react-native-svg-transformer": "^1.5.1", "react-native-swiper": "^1.6.0", "react-native-version-check": "^3.5.0", "react-redux": "^9.2.0", "redux": "^5.0.1", "redux-persist": "^6.0.0", "redux-thunk": "^3.1.0", "swagger-typescript-api": "^13.2.9", "symbol-observable": "^4.0.0", "zod": "^4.1.8"}, "devDependencies": {"@types/react": "~19.1.0", "typescript": "~5.9.2", "@babel/core": "^7.28.4", "@babel/preset-env": "^7.28.3", "@babel/runtime": "^7.28.4", "@bugsnag/source-maps": "^2.3.3", "@jest/globals": "^30.1.2", "@react-native-community/cli": "^20.0.2", "@react-native-community/eslint-config": "^3.2.0", "@react-native/babel-preset": "^0.81.1", "@testing-library/react-native": "^13.3.3", "@tsconfig/react-native": "^3.0.6", "@types/jest": "^30.0.0", "@types/lodash": "^4.17.20", "@types/node": "^24.4.0", "@types/react-native-fetch-blob": "^0.10.11", "@types/react-native-version-check": "^3.4.8", "@types/react-test-renderer": "^19.1.0", "babel-jest": "^30.1.2", "babel-plugin-module-resolver": "^5.0.2", "detox": "^20.41.1", "eslint": "^9.35.0", "eslint-plugin-import": "^2.32.0", "jest": "^30.1.3", "jest-environment-jsdom": "^30.1.2", "metro-react-native-babel-preset": "^0.77.0", "metro-react-native-babel-transformer": "^0.77.0", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "prettier": "^3.6.2", "react-native-dotenv": "^3.4.11", "react-test-renderer": "19.1.1", "ts-jest": "^29.4.1", "ts-node": "^10.9.2", "typedoc": "^0.28.13"}, "private": true}