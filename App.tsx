import Bugsnag from "@bugsnag/react-native";
import { ThemeProvider } from "@emotion/react";
import { useNetInfo } from "@react-native-community/netinfo";
import React, { useEffect } from "react";
import { Platform, Text, View } from "react-native";
import KeyboardManager from "react-native-keyboard-manager";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";

import * as Error from "./src/components/Error";
import { SafeAreaProvider } from "react-native-safe-area-context";
import { ContactModalContextProvider } from "./src/context/contactModalContext";
import { FavoriteContextProvider } from "./src/context/favoriteContext";
import { FilterContextProvider } from "./src/context/filterContext";
import { setNetworkInfo } from "./src/redux/reducers/network/actions";
import store, { persistor } from "./src/redux/store";
import { Router } from "./src/routes/Router";
import theme from "./src/theme";

if (Platform.OS === "ios") {
  KeyboardManager.setEnable(true);
  KeyboardManager.setEnableDebugging(true);
  KeyboardManager.setKeyboardDistanceFromTextField(16);
  KeyboardManager.setLayoutIfNeededOnUpdate(true);
  KeyboardManager.setEnableAutoToolbar(true);
  KeyboardManager.setToolbarDoneBarButtonItemText("Gotowe");
  KeyboardManager.setToolbarManageBehaviourBy("position"); // "subviews" | "tag" | "position"
  KeyboardManager.setToolbarPreviousNextButtonEnable(true);
  KeyboardManager.setToolbarTintColor(theme.colors.purple[100]); // Only #000000 format is supported
  KeyboardManager.setToolbarBarTintColor(theme.colors.gray[1000]); // Only #000000 format is supported
  KeyboardManager.setShouldShowToolbarPlaceholder(true);
  // KeyboardManager.setOverrideKeyboardAppearance(false);
  KeyboardManager.setKeyboardAppearance("default"); // "default" | "light" | "dark"
  KeyboardManager.setShouldResignOnTouchOutside(true);
  KeyboardManager.setShouldPlayInputClicks(true);
}

Bugsnag.start();
const ErrorBoundary = Bugsnag.getPlugin("react").createErrorBoundary(React);

const ErrorView = () => (
  <View>
    <Text>Error bugsnag</Text>
  </View>
);

function App(): React.JSX.Element {
  const { isConnected } = useNetInfo();
  const { dispatch } = store;

  useEffect(() => {
    dispatch(setNetworkInfo(isConnected));
  }, [isConnected]);

  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <ThemeProvider theme={theme}>
          <FilterContextProvider>
            <FavoriteContextProvider>
              <ContactModalContextProvider>
                {!isConnected && <Error.NetworkErrorModal />}
                <SafeAreaProvider>
                  <Router />
                </SafeAreaProvider>
              </ContactModalContextProvider>
            </FavoriteContextProvider>
          </FilterContextProvider>
        </ThemeProvider>
      </PersistGate>
    </Provider>
  );
}

export default () => (
  <ErrorBoundary FallbackComponent={ErrorView}>
    <App />
  </ErrorBoundary>
);
