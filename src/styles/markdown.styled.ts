import { StyleSheet } from 'react-native';

import theme from '~/theme';

export const markdownStyles = StyleSheet.create({
  heading1: {
    fontFamily: theme.fonts.primary['600'],
    fontSize: 18,
    color: theme.colors.black[300],
    letterSpacing: 0.5,
    marginBottom: 18,
  },
  heading2: {
    fontFamily: theme.fonts.primary['500'],
    fontSize: 16,
    color: theme.colors.black[300],
    letterSpacing: 0.5,
    marginVertical: 10,
  },

  // Lists
  bullet_list: {},
  ordered_list: {},
  list_item: {
    marginBottom: 10,
    color: theme.colors.black[100],
    fontFamily: theme.fonts.primary['400'],
    letterSpacing: 0.5,
    lineHeight: 24,
    fontSize: 15,
    flexDirection: 'row',
    justifyContent: 'flex-start',
  },
  // @pseudo class, does not have a unique render rule
  bullet_list_icon: {
    marginLeft: 10,
    marginRight: 10,
  },
  // @pseudo class, does not have a unique render rule
  bullet_list_content: {
    flex: 1,
  },
  // @pseudo class, does not have a unique render rule
  ordered_list_icon: {
    marginLeft: 10,
    marginRight: 10,
  },
  // @pseudo class, does not have a unique render rule
  ordered_list_content: {
    flex: 1,
  },
  paragraph: {
    color: theme.colors.black[100],
    fontFamily: theme.fonts.primary['400'],
    letterSpacing: 0.5,
    lineHeight: 24,
    fontSize: 15,
    marginTop: 10,
    marginBottom: 10,
    flexWrap: 'wrap',
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    width: '100%',
  },
});
