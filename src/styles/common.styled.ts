import { css } from '@emotion/native';
import styled from '@emotion/native/dist/emotion-native.cjs';

import theme from '~/theme';

export const ItemListPageWrapper = css`
  flex: 1;
  display: flex;
  flex-direction: column;
`;

export const ListWrapper = styled.View`
  flex: 1;
`;
export const FilterWrapper = styled.View`
  padding: 16px;
  gap: 10px;
  background-color: ${theme.colors.white['100']};
`;
export const Content = styled.View`
  background-color: ${theme.colors.white['200']};
  flex: 1;
`;
export const ContentWhite = styled.View`
  background-color: ${theme.colors.white['100']};
  flex: 1;
`;
