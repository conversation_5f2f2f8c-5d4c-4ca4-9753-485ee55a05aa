import { RouteProp, useRoute } from '@react-navigation/native';
import { Image, Linking, StyleSheet, TouchableOpacity, View } from 'react-native';

import { Button } from '~/components/Button/Button';
import { ButtonVariant } from '~/components/Button/enum';
import { Typography } from '~/components/Typography/Typography';
import { useNavigate } from '~/hooks/useNavigate';
import { useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { RootStackParamList } from '~/routes/types';
import theme from '~/theme';
import { assets } from '~/theme/assets';

const TEXT = 'Aplikacja wymaga aktualizacji,\n prosimy o pobranie';

export const AppUpdateScreen = () => {
  const { navigation } = useNavigate();
  const { token } = useAppSelector((state) => state.user);
  const { params } = useRoute<RouteProp<RootStackParamList, RouteEnum.APP_UPDATE_SCREEN>>();

  return (
    <View style={styles.container}>
      <Image source={assets.images.splash_png} resizeMode="contain" style={styles.image} />
      <Typography fontSize={20} style={styles.text} color={theme.colors.white[100]}>
        {TEXT}
      </Typography>
      <View style={styles.button}>
        <Button
          onPress={() => Linking.openURL(params.url)}
          style={styles.update}
          variant={ButtonVariant.FOURTH}>
          Aktualizuj
        </Button>
        <TouchableOpacity
          onPress={() => {
            if (token && token.access) navigation.navigate(RouteEnum.HOME);
            else navigation.navigate(RouteEnum.LOGIN);
          }}>
          <Typography fontSize={10} style={styles.close} color={theme.colors.white[100]}>
            Zamknij
          </Typography>
        </TouchableOpacity>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.purple[100],
  },
  image: {
    width: 300,
    height: 300,
    alignSelf: 'center',
  },
  text: {
    textAlign: 'center',
  },
  button: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
    gap: 50,
    paddingBottom: 40,
  },
  update: {
    width: 220,
  },
  close: {
    textDecorationLine: 'underline',
    color: theme.colors.white[100],
  },
});
