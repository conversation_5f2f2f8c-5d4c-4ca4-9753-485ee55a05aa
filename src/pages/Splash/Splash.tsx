import { useEffect } from 'react';
import FastImage from 'react-native-fast-image';

import * as Styled from './Splash.styled';

import { useSplash } from '~/hooks/useSplash';
import { useVersionCheck } from '~/hooks/useVersionCheck';
import { addAppVersion } from '~/redux/reducers/info/appVersion/actions';
import { RouteEnum } from '~/routes/routes';
import { assets } from '~/theme/assets';

export const Splash = () => {
  const { handleTokenRefresh, dispatch, navigation, oldToken, isConnected } = useSplash();
  const { checkVersion } = useVersionCheck();

  useEffect(() => {
    if (isConnected) {
      const fn = async () => await checkVersion();
      fn().then((res) => {
        dispatch(addAppVersion(res.currentVersion));
        if (res.needUpdate && res.storeUrl) {
          navigation.navigate(RouteEnum.APP_UPDATE_SCREEN, { url: res.storeUrl });
        } else {
          setTimeout(() => {
            handleTokenRefresh(oldToken);
          }, 2000);
        }
      });
    }
  }, [isConnected]);

  return (
    <Styled.Wrapper>
      <FastImage
        testID="default-image"
        style={{ width: '100%', height: '100%' }}
        source={assets.images.splash_png}
        resizeMode={'contain'}
      />
    </Styled.Wrapper>
  );
};
