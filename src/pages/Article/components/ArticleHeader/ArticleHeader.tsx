import React, { useContext } from 'react';
import { Dimensions } from 'react-native';

import * as Styled from './ArticleHeader.styled';

import { CardFavourite } from '~/components/Cards/components/CardFavourite';
import { ImageComponent } from '~/components/ImageComponent/ImageComponent';
import { Typography } from '~/components/Typography/Typography';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { ArticleContext } from '~/pages/Article/context/ArticleContext';
import theme from '~/theme';

const deviceWidth = Dimensions.get('window').width;
export const ArticleHeader = () => {
  const context = useContext(ArticleContext);
  const { postFavorite } = useFavoritesQuery();
  const RenderFavourite = () => {
    return (
      <Styled.Favorite>
        <CardFavourite
          active={context.is_favorite}
          onPress={(isFavorite) => {
            postFavorite({
              isFavorite,
              params: {
                payload: {
                  object_id: context.id,
                  object_type: ObjectTypeEnum.Article,
                },
                type: 'Article',
              },
              event: {
                object_type: ObjectTypeEnum.Article,
                categories_id_list: context.categories || [],
                tags_id_list: context.tags || [],
              },
            });
          }}
        />
      </Styled.Favorite>
    );
  };
  return (
    <Styled.Container>
      <ImageComponent
        resizeMode={'cover'}
        noCache={true}
        height={deviceWidth > 500 ? 300 : 200}
        borderRadius={8}
        uri={context.first_image}
        onPress={() => context.actions.setModalOpen(true)}
      />
      <RenderFavourite />
      <Styled.Desc>
        <Typography fontSize={12} fontWeight="500" color={theme.colors.gray['800']}>
          {context.brand.name}
        </Typography>
        {context.publish_date ? (
          <Typography fontSize={12} fontWeight="500" color={theme.colors.gray['800']}>
            {context.publish_date}
          </Typography>
        ) : null}
      </Styled.Desc>
    </Styled.Container>
  );
};
