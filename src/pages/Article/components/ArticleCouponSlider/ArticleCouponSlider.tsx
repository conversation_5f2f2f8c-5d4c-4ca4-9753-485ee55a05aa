import { useContext } from 'react';
import { View } from 'react-native';

import { CouponCard } from '~/components/Cards/CouponCard/CouponCard';
import { Section } from '~/components/Section/Section';
import { ContentSlider } from '~/components/Slider/Slider';
import { FilterContext } from '~/context/filterContext';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useNavigate } from '~/hooks/useNavigate';
import { ArticleContext } from '~/pages/Article/context/ArticleContext';
import { RouteEnum } from '~/routes/routes';

export const ArticleCouponSlider = () => {
  const { navigateToCouponCollection, navigateToCouponDetails } = useNavigate();

  const context = useContext(ArticleContext);
  const _filterContext = useContext(FilterContext);
  const { postFavorite } = useFavoritesQuery();

  return (
    <Section
      style={{ paddingTop: 10 }}
      showBottomLine={false}
      isLoading={context.similar.coupons === null}
      title={'Powiązane Okazje'}
      counter={`[${context.counters.coupons}]`}
      onSectionHeaderPress={() => {
        _filterContext.clear();
        _filterContext.handleSimilarCategoryCollection(context.categories);
        navigateToCouponCollection({
          params: {
            category_ids: context.categories,
          },
          prevScreen: RouteEnum.ARTICLE_DETAIL,
        });
      }}>
      <ContentSlider
        data={context.similar.coupons.slice(0, 10)}
        renderItem={({ item, index }) => (
          <View style={{ width: 170, height: 250 }}>
            <CouponCard
              onFavoritePress={(objectId, isFavorite, categories, tags) => {
                postFavorite({
                  isFavorite,
                  params: {
                    payload: {
                      object_id: objectId,
                      object_type: ObjectTypeEnum.Coupon,
                    },
                    type: 'Coupon',
                  },
                  event: {
                    object_type: ObjectTypeEnum.Coupon,
                    categories_id_list: categories || [],
                    tags_id_list: tags || [],
                  },
                });
              }}
              key={index}
              data={item}
              onPress={() => navigateToCouponDetails(item.id)}
            />
          </View>
        )}
      />
    </Section>
  );
};
