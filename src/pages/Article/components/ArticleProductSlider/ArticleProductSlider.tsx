import { useContext } from 'react';
import { View } from 'react-native';

import { ProductCard } from '~/components/Cards/ProductCard/ProductCard';
import { Section } from '~/components/Section/Section';
import { ContentSlider } from '~/components/Slider/Slider';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useRedirectionUrl } from '~/events/hooks';
import { useLinking } from '~/hooks/useLinking';
import { ArticleContext } from '~/pages/Article/context/ArticleContext';
import theme from '~/theme';

export const ArticleProductSlider = () => {
  //----EVENTS----
  const _fnUrlEvent = useRedirectionUrl((link) => openUrl(link));
  //--X--EVENTS--X--
  const context = useContext(ArticleContext);
  const { openUrl } = useLinking();

  return (
    <Section
      style={{ paddingTop: 10 }}
      title={'Produkty powiązane'}
      bgColor={theme.colors.white['100']}>
      <ContentSlider
        data={context.products}
        renderItem={({ item, index }) => {
          return (
            <View style={{ width: 170 }}>
              <ProductCard
                key={index}
                data={item}
                onPress={() =>
                  _fnUrlEvent(context.brand.id, context.id, ObjectTypeEnum.Article, item.link)
                }
              />
            </View>
          );
        }}
      />
    </Section>
  );
};
