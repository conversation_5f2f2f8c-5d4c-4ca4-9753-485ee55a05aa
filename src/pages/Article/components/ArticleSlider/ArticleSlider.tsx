import { useContext } from 'react';
import { View } from 'react-native';

import { ArticleCard } from '~/components/Cards/ArticleCard/ArticleCard';
import { Section } from '~/components/Section/Section';
import { ContentSlider } from '~/components/Slider/Slider';
import { FilterContext } from '~/context/filterContext';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useNavigate } from '~/hooks/useNavigate';
import { ArticleContext } from '~/pages/Article/context/ArticleContext';
import { RouteEnum } from '~/routes/routes';
import theme from '~/theme';

export const ArticleSlider = () => {
  const { navigateToArticleCollection, navigateToArticleDetails } = useNavigate();
  const context = useContext(ArticleContext);
  const _filterContext = useContext(FilterContext);
  const { postFavorite } = useFavoritesQuery();

  return (
    <Section
      bgColor={theme.colors.white['100']}
      title="Podobne artykuły"
      counter={`[${context.counters.articles}]`}
      onSectionHeaderPress={() => {
        _filterContext.clear();
        _filterContext.handleSimilarCategoryCollection(context.categories);
        navigateToArticleCollection({
          params: {
            category_ids: context.categories,
          },
          prevScreen: RouteEnum.ARTICLE_DETAIL,
        });
      }}>
      <ContentSlider
        data={context.similar.articles.slice(0, 10)}
        renderItem={({ item, index }) => (
          <View style={{ width: 250 }}>
            <ArticleCard
              onFavoritePress={(objectId, isFavorite, categories, tags) => {
                postFavorite({
                  isFavorite,
                  params: {
                    payload: {
                      object_id: objectId,
                      object_type: ObjectTypeEnum.Article,
                    },
                    type: 'Article',
                  },
                  event: {
                    object_type: ObjectTypeEnum.Article,
                    categories_id_list: categories || [],
                    tags_id_list: tags || [],
                  },
                });
              }}
              data={item}
              key={index}
              onPress={() => navigateToArticleDetails(item.id)}
            />
          </View>
        )}
      />
    </Section>
  );
};
