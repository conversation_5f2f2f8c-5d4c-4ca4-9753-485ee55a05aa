import Markdown, { MarkdownIt } from '@jonasmerlin/react-native-markdown-display';
import React, { useContext } from 'react';
import { View } from 'react-native';

import * as Styled from './ArticleContent.styled';

import { ImageComponent } from '~/components/ImageComponent/ImageComponent';
import { ContentSlider } from '~/components/Slider/Slider';
import { Typography } from '~/components/Typography/Typography';
import { ArticleContext } from '~/pages/Article/context/ArticleContext';
import { markdownStyles } from '~/styles/markdown.styled';
import { IGallery } from '~/types/gallery';

const renderTextWithImage = (text: string, products: IGallery[], onPress: () => void) => {
  const REPLACE_STRING = '<GALERIA>';

  const splittedText = text.split(REPLACE_STRING);

  const renderImages = () => {
    return products.length > 1 ? (
      <ContentSlider
        ItemSeparatorComponent={() => <View style={{ width: 12 }} />}
        data={products}
        renderItem={({ item }) => (
          <>
            {item.image !== null ? (
              <View style={{ width: 230 }}>
                <ImageComponent onPress={onPress} height={130} uri={item.image} borderRadius={8} />
              </View>
            ) : null}
          </>
        )}
      />
    ) : (
      <Styled.ImageWrapper>
        <View style={{ width: 300 }}>
          <ImageComponent onPress={onPress} height={130} uri={products[0].image!} />
        </View>
      </Styled.ImageWrapper>
    );
  };

  // Jeśli w tekście nie ma znacznika <GALERIA> ale są dostępne zdjęcia, wyświetl je na końcu
  if (splittedText.length === 1 && products.length > 0) {
    return (
      <>
        <View style={{ paddingHorizontal: 16 }}>
          <Markdown
            style={markdownStyles}
            markdownit={MarkdownIt({ typographer: true }).disable(['image'])}>
            {text}
          </Markdown>
        </View>
        {renderImages()}
      </>
    );
  }

  return splittedText.map((part, index) => {
    if (index !== splittedText.length - 1) {
      return (
        <React.Fragment key={index}>
          <View key={index} style={{ paddingHorizontal: 16 }}>
            <Markdown
              style={markdownStyles}
              markdownit={MarkdownIt({ typographer: true }).disable(['image'])}>
              {part}
            </Markdown>
          </View>
          {renderImages()}
        </React.Fragment>
      );
    } else {
      return (
        <View key={index} style={{ paddingHorizontal: 16, paddingTop: 16 }}>
          <Markdown
            style={markdownStyles}
            markdownit={MarkdownIt({ typographer: true }).disable(['image'])}>
            {part}
          </Markdown>
        </View>
      );
    }
  });
};

export const ArticleContent = () => {
  const context = useContext(ArticleContext);
  return (
    <Styled.Container>
      <View style={{ paddingHorizontal: 16, gap: 20 }}>
        <Typography fontSize={20} fontWeight="600">
          {context.title}
        </Typography>
        {context.subtitle ? (
          <Typography fontSize={14} fontWeight="500">
            {context.subtitle}
          </Typography>
        ) : null}
      </View>

      {context.text
        ? renderTextWithImage(context.text, context.images, () =>
            context.actions.setModalOpen(true),
          )
        : null}
    </Styled.Container>
  );
};
