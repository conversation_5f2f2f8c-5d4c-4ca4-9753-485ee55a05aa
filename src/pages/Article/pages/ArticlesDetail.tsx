import { useNetInfo } from '@react-native-community/netinfo';
import { RouteProp, useRoute } from '@react-navigation/native';
import { useEffect, useMemo, useState, useRef } from 'react';
import { RefreshControl, ScrollView, View } from 'react-native';

import * as Styled from './ArticlesDetail.styled';

import { Layout } from '~/components/Layout/Layout';
import { Lightbox } from '~/components/Lightbox/Lightbox';
import { Loader } from '~/components/Loader/Loader';
import * as Lofi from '~/components/Loader/Lofi';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useDisplayCard } from '~/events/hooks/useDisplayCard';
import { useNavigate } from '~/hooks/useNavigate';
import { ArticleContent } from '~/pages/Article/components/ArticleContent/ArticleContent';
import { ArticleCouponSlider } from '~/pages/Article/components/ArticleCouponSlider/ArticleCouponSlider';
import { ArticleDiscountSlider } from '~/pages/Article/components/ArticleDiscountSlider/ArticleDiscountSlider';
import { ArticleHeader } from '~/pages/Article/components/ArticleHeader/ArticleHeader';
import { ArticleInspirationSlider } from '~/pages/Article/components/ArticleInspirationSlider/ArticleInspirationSlider';
import { ArticleProductSlider } from '~/pages/Article/components/ArticleProductSlider/ArticleProductSlider';
import { ArticleSlider } from '~/pages/Article/components/ArticleSlider/ArticleSlider';
import { ArticleContextProvider } from '~/pages/Article/context/ArticleContext';
import {
  useGetArticlesDetailsQuery,
  useGetSimilarArticlesQuery,
  useLazyGetArticlesDetailsQuery,
} from '~/redux/api/articles';
import { useGetSimilarCouponsQuery } from '~/redux/api/coupons';
import { useGetSimilarCardsQuery } from '~/redux/api/discounts';
import { useGetSimilarInspirationsQuery } from '~/redux/api/inspirations';
import { useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { RootStackParamList } from '~/routes/types';
import theme from '~/theme';
import { ParamsOptions } from '~/types/shared';

const ArticleDetailLofi = () => {
  return (
    <Lofi.Details>
      <Lofi.Container>
        <View style={{ position: 'absolute', right: 32, bottom: -32 }}>
          <Lofi.Circle border />
        </View>
      </Lofi.Container>
      <View style={{ gap: 16, paddingTop: 32, alignItems: 'flex-end' }}>
        <Lofi.Text width={'60%'} />
        <Lofi.Text width={'80%'} />
        <Lofi.Text width={'100%'} />
      </View>
      <View style={{ paddingTop: 16, gap: 16 }}>
        <Lofi.Container height={32} rx={32} />
        <Lofi.Container height={32} rx={32} />
        <Lofi.Container height={32} rx={32} />
      </View>
    </Lofi.Details>
  );
};

export const ArticlesDetail = () => {
  const { navigateBack, navigation } = useNavigate();
  const scrollViewRef = useRef<ScrollView>(null);
  const activeSections = useAppSelector((state) => state.activeSections);
  const [isModalOpen, setModalOpen] = useState(false);
  const { isConnected } = useNetInfo();

  const { params: detailParams } =
    useRoute<RouteProp<RootStackParamList, RouteEnum.ARTICLE_DETAIL>>();
  const {
    data: detail,
    isLoading: isLoadingDetail,
    error: detailError,
  } = useGetArticlesDetailsQuery({
    id: detailParams.id,
  });
  const [getArticlesDetails, { isLoading }] = useLazyGetArticlesDetailsQuery();

  let queryParams: ParamsOptions = useMemo(() => {
    return {
      category_ids: detail?.categories,
    };
  }, [detail]);

  const { data: similarArticles, isLoading: isLoadingSimilarArticles } =
    useGetSimilarArticlesQuery(queryParams);
  const { data: similarDiscounts, isLoading: isLoadingSimilarDiscounts } =
    useGetSimilarCardsQuery(queryParams);
  const { data: similarInspirations, isLoading: isLoadingSimilarInspirations } =
    useGetSimilarInspirationsQuery(queryParams);
  const { data: similarCoupons, isLoading: isLoadingSimilarCoupons } =
    useGetSimilarCouponsQuery(queryParams);

  const { handleDisplayCard } = useDisplayCard({
    type: ObjectTypeEnum.Article,
  });
  useEffect(() => {
    if (isModalOpen) {
      navigation.setOptions({ orientation: 'all' });
    } else {
      navigation.setOptions({ orientation: 'portrait' });
    }
  }, [isModalOpen]);

  // EVENTS - DISPLAY CARD
  useEffect(() => {
    if (detailParams.id === detail?.id) {
      handleDisplayCard({
        detail: detail,
        is_rec: detailParams.is_rec ? detailParams.is_rec : false,
      });
    }
    scrollViewRef.current?.scrollTo({ y: 0, animated: true });
  }, [detail]);

  const similar = similarArticles?.results.filter((item) => item.id !== detail?.id);

  if (isLoadingDetail) return <ArticleDetailLofi />;
  return (
    <Layout
      error={detailError}
      padding={0}
      onGoBack={() => navigateBack()}
      headerShown={isModalOpen ? false : true}
      footerShown={isModalOpen ? false : true}
      headerOptions={{
        label: 'Artykuły',
        leftIcon: true,
        labelStyle: {
          fontSize: 16,
          fontWeight: '600',
          color: theme.colors.black['100'],
        },
      }}>
      <ArticleContextProvider
        providerValues={{
          ...detail!,
          counters: {
            articles: similarArticles?.count ?? 0,
            discounts: similarDiscounts?.count ?? 0,
            coupons: similarCoupons?.count ?? 0,
            inspirations: similarInspirations?.count ?? 0,
          },
          similar: {
            articles: similar || [],
            discounts: similarDiscounts?.results || [],
            coupons: similarCoupons?.results || [],
            inspirations: similarInspirations?.results || [],
          },
          actions: {
            isModalOpen,
            setModalOpen,
          },
        }}>
        <Styled.Wrapper
          ref={scrollViewRef}
          refreshControl={
            <RefreshControl
              refreshing={isLoading}
              onRefresh={() => getArticlesDetails({ id: detailParams.id })}
            />
          }>
          <Styled.Container>
            <ArticleHeader />
            <ArticleContent />
          </Styled.Container>
          {/*SLIDERS*/}

          {isLoadingDetail ? (
            <Loader />
          ) : detail?.products && detail.products.length > 0 ? (
            <ArticleProductSlider />
          ) : null}

          {isLoadingSimilarArticles ? (
            <Loader />
          ) : similar && similar.length > 0 ? (
            <ArticleSlider />
          ) : null}

          {/* INSPIRATIONS */}
          {isLoadingSimilarInspirations ? (
            <Loader />
          ) : similarInspirations && similarInspirations.count > 0 ? (
            activeSections.inspiration && <ArticleInspirationSlider />
          ) : null}

          {isLoadingSimilarDiscounts ? (
            <Loader />
          ) : similarDiscounts && similarDiscounts.count > 0 ? (
            activeSections.card && <ArticleDiscountSlider />
          ) : null}

          {/* COUPONS */}
          {isLoadingSimilarCoupons ? (
            <Loader />
          ) : similarCoupons && similarCoupons.count > 0 ? (
            activeSections.coupon && <ArticleCouponSlider />
          ) : null}
        </Styled.Wrapper>
      </ArticleContextProvider>
      <>
        {isModalOpen && isConnected && detail ? (
          <Lightbox
            onClose={() => setModalOpen(false)}
            uri={detail.images.map((item) => item.image).concat(detail.first_image)}
          />
        ) : null}
      </>
    </Layout>
  );
};
