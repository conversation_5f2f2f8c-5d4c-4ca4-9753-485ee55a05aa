import { useNetInfo } from '@react-native-community/netinfo';
import { useFocusEffect } from '@react-navigation/native';
import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
// TODO: hide search button
import { View } from 'react-native';

import { ArticleCard } from '~/components/Cards/ArticleCard/ArticleCard';
import { EmptyData } from '~/components/EmptyData/EmptyData';
import { FilterLayout } from '~/components/Filters/FilterLayout';
// TODO: hide search button
import { Layout } from '~/components/Layout/Layout';
import { List } from '~/components/List/List';
import { ListItem } from '~/components/List/ListItem';
import { Search } from '~/components/Search';
import { FilterContext } from '~/context/filterContext';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useDisplayContentType, useFilterCategoriesChange } from '~/events/hooks';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useListLayout } from '~/hooks/useListLayout';
import { useNavigate } from '~/hooks/useNavigate';
import { useResetSearchWithBack } from '~/hooks/useResetSearchWithBack';
import { useGetArticlesQuery, useGetArticlesWithPaginationQuery } from '~/redux/api/articles';
import { useLazyGetCategoriesQuery } from '~/redux/api/categories';
import { useAppSelector } from '~/redux/store';
import * as Styled from '~/styles/common.styled';
import theme from '~/theme';
// TODO: hide search button
import { ParamsOptions } from '~/types/shared';

export const Article = () => {
  const { gap, screenWidth } = useListLayout();
  const { navigateToArticleCollection, navigateToArticleDetails } = useNavigate();
  const _filterContext = useContext(FilterContext);
  const { handleBackWithReset } = useResetSearchWithBack();
  const { city } = useAppSelector((state) => state.user.detail);
  const [page, setPage] = useState(1);
  const { isConnected } = useNetInfo();

  let queryParams: ParamsOptions = useMemo(() => {
    const params = _filterContext.buildFilterParams();

    return {
      ...params,
      ...(page > 1 ? { page: page } : {}),
      city_id: city,
    };
  }, [
    _filterContext.selectedCategoryFilter,
    _filterContext.selectedShopFilter,
    _filterContext.selectedSortFilter,
    _filterContext.selectedSimilarCategoryFilter,
    _filterContext.searchId,
    _filterContext.searchString,
    city,
    page,
  ]);

  /**
   * RTK Query
   */
  const { data, isFetching, refetch } = useGetArticlesWithPaginationQuery(queryParams);
  const { data: mayInterestYouData } = useGetArticlesQuery({ city_id: city });
  const [trigger] = useLazyGetCategoriesQuery();
  const { postFavorite } = useFavoritesQuery();

  /*
   * Trigger next page
   */
  let isFetchingNextPage = false;
  const onEndReached = () => {
    if (!isConnected) return;
    if (data?.next === null || isFetching || isFetchingNextPage) return;
    isFetchingNextPage = true;
    setPage(page + 1);
    setTimeout(() => {
      isFetchingNextPage = false;
    }, 500);
  };

  /*
   * Fetch categories for specific object type (Card, Coupon etc.)
   */
  useEffect(() => {
    trigger({ content_type: ObjectTypeEnum.Article }).then((res) => {
      _filterContext.setSelectedCategoryFilter(res.data);
    });
  }, []);

  /**
   * Events:
   */
  useDisplayContentType({
    type: ObjectTypeEnum.Article,
    collection: data,
  });
  // Nie mamy searcha więc ten event chyba nie jest uzywany
  // const { _fnEvent } = useSearchString();
  useFilterCategoriesChange(_filterContext.buildFilterParams().category_ids || []);

  /**
   * Reset page on focus (it triggers reset cache of rtk query and fetch new data from server)
   */
  useFocusEffect(
    useCallback(() => {
      setPage(1);
      return () => {
        setPage(1);
      };
    }, [
      _filterContext.selectedCategoryFilter,
      _filterContext.selectedShopFilter,
      _filterContext.selectedSortFilter,
      _filterContext.selectedSimilarCategoryFilter,
      _filterContext.searchId,
      _filterContext.searchString,
      city,
    ]),
  );

  useEffect(() => {
    refetch();
  }, [isConnected]);

  // ----EVENTS----
  useDisplayContentType({
    type: ObjectTypeEnum.Article,
    collection: data,
  });
  // --X--EVENTS--X--

  return (
    <Layout
      padding={0}
      onGoBack={handleBackWithReset}
      headerOptions={{
        resultCounter: data?.count,
        label: 'Artykuły',
        leftIcon: true,
        rightItem: <Search to={ObjectTypeEnum.Article} />,
        labelStyle: {
          fontSize: 16,
          fontWeight: '600',
          color: theme.colors.black['100'],
        },
      }}>
      <Styled.ListWrapper>
        <Styled.FilterWrapper>
          <FilterLayout
            screenType={ObjectTypeEnum.Article}
            setColumnsSwitch={() => {}}
            columnsSwitch={1}
            isFilter={true}
            isSwitch={false}
            desc={'Trendy i porady'}
            removeSingleFilter={(id, type) => {
              setPage(1);
              _filterContext.removeSingleFilter(id, type);
            }}
            removeAllFilters={() => {
              setPage(1);
              _filterContext.clear();
            }}
            tiles={_filterContext.tiles}
          />
        </Styled.FilterWrapper>
        <Styled.ContentWhite>
          {isFetching || (data && data.results.length > 0) ? (
            <List
              contentContainerStyle={{ gap: 10 }}
              loading={isFetching}
              onEndReached={data?.results && !isFetching ? onEndReached : () => {}}
              data={data?.results || []}
              columnsSwitch={screenWidth > 700 ? 2 : 1}
              renderItem={({ item }) => {
                return (
                  <ListItem columnsSwitch={1} gap={gap} screenWidth={screenWidth}>
                    <ArticleCard
                      size={'large'}
                      data={item}
                      onFavoritePress={(objectId, isFavorite, categories, tags) => {
                        postFavorite({
                          isFavorite,
                          params: {
                            payload: {
                              object_id: objectId,
                              object_type: ObjectTypeEnum.Article,
                            },
                            type: 'Article',
                          },
                          event: {
                            object_type: ObjectTypeEnum.Article,
                            categories_id_list: categories || [],
                            tags_id_list: tags || [],
                          },
                        });
                      }}
                      onPress={() => navigateToArticleDetails(item.id, item.is_rec)}
                    />
                  </ListItem>
                );
              }}
            />
          ) : null}

          {(!isFetching && !data) || data?.results.length === 0 ? (
            <EmptyData
              data={mayInterestYouData?.results.slice(0, 10)}
              counter={mayInterestYouData?.count}
              onSectionHeaderPress={() => {
                _filterContext.clear();
                navigateToArticleCollection();
              }}
              renderItem={({ item, index }) => (
                <View style={{ width: 250 }}>
                  <ArticleCard
                    onFavoritePress={(objectId, isFavorite, categories, tags) => {
                      postFavorite({
                        isFavorite,
                        params: {
                          payload: {
                            object_id: objectId,
                            object_type: ObjectTypeEnum.Article,
                          },
                          type: 'Article',
                        },
                        event: {
                          object_type: ObjectTypeEnum.Article,
                          categories_id_list: categories || [],
                          tags_id_list: tags || [],
                        },
                      });
                    }}
                    key={index}
                    data={item}
                    onPress={() => navigateToArticleDetails(item.id, item.is_rec)}
                  />
                </View>
              )}
            />
          ) : null}
        </Styled.ContentWhite>
      </Styled.ListWrapper>
    </Layout>
  );
};
