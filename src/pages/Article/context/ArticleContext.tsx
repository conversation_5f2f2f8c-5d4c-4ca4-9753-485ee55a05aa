import { createContext, ReactNode } from 'react';

import { IArticle, IArticleDetails } from '~/types/article';
import { ICoupon } from '~/types/coupon';
import { IDiscount } from '~/types/discount';
import { IInspiration } from '~/types/inspirations';

interface IArticleContext extends IArticleDetails {
  counters: {
    articles: number;
    coupons: number;
    discounts: number;
    inspirations: number;
  };
  similar: {
    articles: IArticle[];
    coupons: ICoupon[];
    discounts: IDiscount[];
    inspirations: IInspiration[];
  };
  actions: {
    isModalOpen: boolean;
    setModalOpen: (value: boolean) => void;
  };
}

export interface IArticleContextProvider {
  providerValues: IArticleContext;
  children: ReactNode;
}

const defaultContext: IArticleContext = {
  id: 0,
  title: '',
  brand: {
    id: 0,
    name: '',
    logo: '',
    is_favorite: false,
  },
  first_image: '',
  images: [],
  products: [],
  is_favorite: false,
  counters: {
    articles: 0,
    coupons: 0,
    discounts: 0,
    inspirations: 0,
  },
  similar: {
    articles: [],
    coupons: [],
    discounts: [],
    inspirations: [],
  },
  actions: {
    isModalOpen: false,
    setModalOpen: () => {},
  },
};

export const ArticleContext = createContext<IArticleContext>(defaultContext);

export const ArticleContextProvider = ({ providerValues, children }: IArticleContextProvider) => {
  return <ArticleContext.Provider value={providerValues}>{children}</ArticleContext.Provider>;
};
