import { createContext } from 'react';

import { IStatus } from '~/types/errors';

interface ContextProps {
  loading: boolean;
  setLoading: (loading: boolean) => void;
  status: IStatus | undefined;
  setStatus: (status: IStatus | undefined) => void;
}

interface ProviderContext {
  value: ContextProps;
  children: React.ReactNode;
}

const defaultValues: ContextProps = {
  loading: false,
  setLoading: () => {},
  status: undefined,
  setStatus: () => {},
};

export const socialLoginContext = createContext<ContextProps>(defaultValues);
export const SocialLoginProvider = ({ children, value }: ProviderContext) => {
  return <socialLoginContext.Provider value={value}>{children}</socialLoginContext.Provider>;
};
