import { useFormContext } from 'react-hook-form';

import * as Styled from './Consents.styled';

import { Checkbox } from '~/components/Checkbox/Checkbox';
import { Typography } from '~/components/Typography/Typography';
import { useNavigate } from '~/hooks/useNavigate';
import { RouteEnum } from '~/routes/routes';
import theme from '~/theme';

export const Consents = () => {
  const { navigation } = useNavigate();
  const {
    setValue,
    formState: { errors },
  } = useFormContext();

  const handleRequired = (state: boolean) => {
    setValue('consents', state);
  };

  return (
    <Styled.Wrapper>
      <Checkbox onStateChange={handleRequired}>
        <Styled.CheckboxText>
          <Typography fontSize={10} fontWeight="700" color={theme.colors.black[300]}>
            *
          </Typography>
          <Typography fontSize={10} fontWeight="500" color={theme.colors.black[300]}>
            Zapoznałem się z
          </Typography>
          <Typography
            fontSize={10}
            fontWeight="700"
            color={theme.colors.black[300]}
            style={{
              textDecorationLine: 'underline',
              textDecorationStyle: 'solid',
              textDecorationColor: theme.colors.black['100'],
            }}
            onPress={() => navigation.navigate(RouteEnum.POLICY, { type: 'terms' })}>
            regulaminem
          </Typography>
          <Typography fontSize={10} fontWeight="500" color={theme.colors.black[300]}>
            i
          </Typography>
          <Typography
            fontSize={10}
            fontWeight="700"
            color={theme.colors.black[300]}
            style={{
              textDecorationLine: 'underline',
              textDecorationStyle: 'solid',
              textDecorationColor: theme.colors.black['100'],
            }}
            onPress={() => navigation.navigate(RouteEnum.POLICY, { type: 'policy' })}>
            polityką prywatności
          </Typography>
        </Styled.CheckboxText>
      </Checkbox>
      {errors ? (
        <Typography fontSize={10} fontWeight="500" color={'red'}>
          {errors.consents?.message as string}
        </Typography>
      ) : null}
    </Styled.Wrapper>
  );
};
