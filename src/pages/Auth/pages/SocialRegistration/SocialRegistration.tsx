import { FormProvider } from 'react-hook-form';
import { View } from 'react-native';

import { Consents } from './components/Consents';
import * as Styled from './SocialRegistration.styled';

import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { Input } from '~/components/Inputs/Input/Input';
import { Loader } from '~/components/Loader/Loader';
import { Typography } from '~/components/Typography/Typography';
import { useSocialRegistration } from '~/hooks/useSocialRegistration';
import { Header } from '~/pages/Auth/components/Header';
import theme from '~/theme';

export const SocialRegistration = () => {
  const { loader, form, onSubmit } = useSocialRegistration();

  return (
    <Styled.Wrapper>
      <Header />
      <Typography
        fontSize={22}
        fontWeight="400"
        color={theme.colors.purple[100]}
        style={{ textAlign: 'center' }}>
        {'Pozosta<PERSON> jeszcze\ntylko jeden krok'}
      </Typography>
      {!loader ? (
        <FormProvider {...form}>
          <Styled.Form>
            <Input name={'name'} label={'Imię'} />
            <Input editable={false} name={'email'} label={'e-mail'} />
            <Typography
              fontSize={18}
              fontWeight="600"
              color={theme.colors.black[300]}
              style={{ textAlign: 'center' }}>
              {'Podaj swój\nnumer telefonu'}
            </Typography>
            <Input name={'phone'} label={'nr telefonu'} />
            <Consents />
            <Button
              onPress={form.handleSubmit(onSubmit, (error) => console.log(error))}
              variant={ButtonVariant.FIFTH}
              size={ButtonSize.MEDIUM}
              borderRadius={24}>
              {`Zarejestruj się`.toUpperCase()}
            </Button>
          </Styled.Form>
        </FormProvider>
      ) : (
        <View style={{ maxHeight: 200 }}>
          <Loader />
        </View>
      )}
    </Styled.Wrapper>
  );
};
