import { useFocusEffect } from '@react-navigation/native';
import { useCallback } from 'react';
import { FormProvider } from 'react-hook-form';
import { Platform, ScrollView, View } from 'react-native';

import { SocialLoginProvider } from '../../context/socialLoginContext';

import * as Styled from './Login.styled';

import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { Input } from '~/components/Inputs/Input/Input';
import { Loader } from '~/components/Loader/Loader';
import { Typography } from '~/components/Typography/Typography';
import { useLogin } from '~/hooks/useLogin';
import { usePreventBack } from '~/hooks/usePreventBack';
import { Header } from '~/pages/Auth/components/Header';
import { RegisterLink } from '~/pages/Auth/components/RegisterLink';
import { SocialAuth } from '~/pages/Auth/components/SocialAuth';
import { RouteEnum } from '~/routes/routes';
import theme from '~/theme';
import { assets } from '~/theme/assets';
import { formDefault } from '~/validation/loginForm/Default';
import { SafeAreaView } from 'react-native-safe-area-context';

export const Login = () => {
  const isIos = Platform.OS === 'ios';
  const { loading, handleLogin, form, status, navigation, route, setLoading, setStatus } =
    useLogin();
  usePreventBack();
  useFocusEffect(
    useCallback(() => {
      return () => {
        /**
         * @description
         * Reset form to default values on leave from component
         * @warning
         * On React-Native component state is not automatically reset on leave!
         */
        setLoading(false);
        setStatus(undefined);
        form.reset(formDefault);
      };
    }, []),
  );

  return (
    <SocialLoginProvider
      value={{ loading: loading, setLoading: setLoading, status: status, setStatus: setStatus }}>
      <SafeAreaView edges={['top', 'right', 'left', 'bottom']}>
        <ScrollView>
          <Styled.Wrapper style={{ gap: isIos ? 12 : 20 }}>
            <Header />
            <Typography
              fontSize={22}
              fontWeight="400"
              color={theme.colors.purple[100]}
              style={{ textAlign: 'center' }}>
              {'Profity dla\nTwojego domu!'}
            </Typography>

            {loading ? (
              <View style={{ maxHeight: 200 }}>
                <Loader />
              </View>
            ) : (
              <>
                <FormProvider {...form}>
                  <Styled.Form>
                    <Input label={'e-mail'} name={'email'} />
                    <Input
                      label={'hasło'}
                      name={'password'}
                      icon={assets.icons.eye_password_icon_svg}
                      isPassword={true}
                    />
                    {status ? (
                      <Typography
                        fontSize={12}
                        fontWeight="400"
                        color={
                          status.status === 'error' ? theme.colors.red[100] : theme.colors.gray[300]
                        }>
                        {status.message}
                      </Typography>
                    ) : null}
                  </Styled.Form>
                  <Typography
                    onPress={() => navigation.navigate(RouteEnum.PASSWORD_RESET_EMAIL)}
                    fontSize={12}
                    fontWeight="400"
                    color={theme.colors.gray[800]}
                    style={{
                      textDecorationLine: 'underline',
                      textDecorationColor: theme.colors.gray[800],
                    }}>
                    Nie pamiętam hasła
                  </Typography>
                  <Button
                    variant={ButtonVariant.FIFTH}
                    size={ButtonSize.MEDIUM}
                    onPress={form.handleSubmit(handleLogin)}>
                    {'Zaloguj się'.toUpperCase()}
                  </Button>
                </FormProvider>
                <RegisterLink />
                <SocialAuth />
                <Typography
                  onPress={() => navigation.navigate(route)}
                  fontSize={14}
                  fontWeight="600"
                  color={theme.colors.black[300]}
                  style={{
                    textDecorationLine: 'underline',
                    textDecorationColor: theme.colors.black[300],
                  }}>
                  Wchodzę jako gość
                </Typography>
              </>
            )}
          </Styled.Wrapper>
        </ScrollView>
      </SafeAreaView>
    </SocialLoginProvider>
  );
};
