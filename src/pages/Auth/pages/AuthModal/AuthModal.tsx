import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useEffect } from 'react';

import * as Styled from './AuthModal.styled';

import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { ModalHeader } from '~/components/Modals/common/ModalHeader/ModalHeader';
import * as Popup from '~/components/Popup';
import { Typography } from '~/components/Typography/Typography';
import { RegisterLink } from '~/pages/Auth/components/RegisterLink';
import { contentApi } from '~/redux/api/contentApi';
import { userLogout } from '~/redux/reducers/user/actions';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { RootStackParamList } from '~/routes/types';
import theme from '~/theme';
import { assets } from '~/theme/assets';

export const AuthModal = () => {
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const { token } = useAppSelector((state) => state.user);
  //const dispatch = useAppDispatch();

  useEffect(() => {
    if (token) {
      navigation.navigate(RouteEnum.PROFILE);
    }
  }, []);

  return (
    <Popup.DynamicHeight
      onClose={() => {
        navigation.goBack();
      }}>
      <Styled.Wrapper>
        <ModalHeader icon={assets.icons.user_svg} title={'Moje Konto'} uppercase={false} />
        <Typography
          fontSize={14}
          fontWeight="400"
          color={theme.colors.black[100]}
          style={{ textAlign: 'center' }}>
          {`Aktywuj konto\ni zyskaj jeszcze więcej.`}
        </Typography>
        <Styled.ButtonWrapper>
          <Button
            variant={ButtonVariant.FIFTH}
            size={ButtonSize.MEDIUM}
            onPress={() => {
              //dispatch(userLogout());
              navigation.replace(RouteEnum.LOGIN);
            }}>
            {'Zaloguj się'.toUpperCase()}
          </Button>
        </Styled.ButtonWrapper>
        <RegisterLink />
      </Styled.Wrapper>
    </Popup.DynamicHeight>
  );
};
