import { zodResolver } from '@hookform/resolvers/zod';
import { RouteProp, useRoute } from '@react-navigation/native';
import { useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import * as z from 'zod';

import { Header } from '../../components/Header';

import * as Styled from './PasswordReset.styled';

import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { Input } from '~/components/Inputs/Input/Input';
import { Typography } from '~/components/Typography/Typography';
import { SERVER_URL } from '~/config.api';
import { useNavigate } from '~/hooks/useNavigate';
import { RouteEnum } from '~/routes/routes';
import { RootStackParamList } from '~/routes/types';
import theme from '~/theme';
import { assets } from '~/theme/assets';
import { IStatus } from '~/types/errors';
import axios from '~/utils/axios.config';
import { passwordSchema } from '~/validation/common/passwordSchema';

type TForm = {
  password: string;
  confirmPassword: string;
};

export const formDefault: TForm = {
  password: '',
  confirmPassword: '',
};

export const formSchema = z
  .object({
    password: passwordSchema,
    confirmPassword: z.string(),
  })
  .superRefine(({ confirmPassword, password }, ctx) => {
    if (confirmPassword !== password) {
      ctx.addIssue({
        code: 'custom',
        message: 'Hasła nie są takie same',
        path: ['confirmPassword'],
      });
    }
  });

export const NewPassword = () => {
  const { navigation } = useNavigate();
  const { params } = useRoute<RouteProp<RootStackParamList, RouteEnum.NEW_PASSWORD>>();
  const [error, setError] = useState<IStatus | undefined>();

  const handlePasswordReset = async () => {
    // Trim password before sending
    const trimmedEmail = params.email.toLowerCase().trim();
    const trimmedPassword = form.getValues().password.trim();

    axios({
      method: 'POST',
      url: `${SERVER_URL}/user/me/passreset/`,
      headers: {
        Accept: 'application/json',
      },
      data: {
        email: trimmedEmail,
        verification_code: params.otc,
        new_password: trimmedPassword,
      },
    })
      .then(() => {
        navigation.navigate(RouteEnum.PASSWORD_RESET_SUMMARY);
        // event.handlePasswordReset();
      })
      .catch(() => {
        setError({
          status: 'error',
          message: 'Nie udało się zresetować hasła. Sprawdź poprawność kodu weryfikacyjnego.',
        });
      });
  };

  const form = useForm<TForm>({
    resolver: zodResolver(formSchema),
    defaultValues: formDefault,
    mode: 'onSubmit',
    reValidateMode: 'onChange',
  });

  return (
    <FormProvider {...form}>
      <Styled.Wrapper>
        <Header />
        <Input
          label={'nowe hasło'}
          icon={assets.icons.eye_password_icon_svg}
          isPassword={true}
          name={'password'}
        />
        <Input
          label={'powtórz hasło'}
          icon={assets.icons.eye_password_icon_svg}
          isPassword={true}
          name={'confirmPassword'}
        />
        <Button
          variant={ButtonVariant.FIFTH}
          size={ButtonSize.MEDIUM}
          onPress={form.handleSubmit(handlePasswordReset, (onErrors) => console.log(onErrors))}>
          {`Resetuj hasło`.toUpperCase()}
        </Button>
        {error && (
          <Typography
            fontSize={14}
            color={error.status === 'error' ? theme.colors.red[100] : theme.colors.purple[100]}
            style={{ textAlign: 'center' }}>
            {error.message}
          </Typography>
        )}
        <Typography
          onPress={() => {
            navigation.navigate(RouteEnum.LOGIN);
          }}
          fontSize={14}
          color={theme.colors.gray[100]}
          style={{ textDecorationLine: 'underline', textDecorationColor: theme.colors.gray[100] }}>
          Wróć do logowania
        </Typography>
      </Styled.Wrapper>
    </FormProvider>
  );
};
