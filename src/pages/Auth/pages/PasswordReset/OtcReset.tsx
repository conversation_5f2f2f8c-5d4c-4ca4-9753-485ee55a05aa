import { RouteProp, useRoute } from '@react-navigation/native';
import { useState } from 'react';

import { Header } from '../../components/Header';

import * as Styled from './PasswordReset.styled';

import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { InputPasswordReset } from '~/components/Inputs/Input/Input';
import { Typography } from '~/components/Typography/Typography';
import { useNavigate } from '~/hooks/useNavigate';
import { RouteEnum } from '~/routes/routes';
import { RootStackParamList } from '~/routes/types';
import theme from '~/theme';

export const OtcScreen = () => {
  const [code, setCode] = useState<string>('');

  const { navigation } = useNavigate();
  const { params } = useRoute<RouteProp<RootStackParamList, RouteEnum.PASSWORD_RESET_OTC>>();

  const handlePasswordReset = async () => {
    navigation.navigate(RouteEnum.NEW_PASSWORD, {
      email: params.email.toLowerCase(),
      otc: code,
    });
  };

  return (
    <Styled.Wrapper>
      <Header />
      <Typography
        fontSize={18}
        fontWeight="600"
        color={theme.colors.purple[100]}
        style={{ textAlign: 'center', maxWidth: 340 }}>
        Na twój adres email został wysłany kod weryfikacyjny. Wpisz go poniżej.
      </Typography>

      <InputPasswordReset
        value={code}
        onChangeText={setCode}
        key={'verification_code'}
        label={'Kod weryfikacyjny'}
        name={'verification_code'}
      />

      <Button
        variant={ButtonVariant.FIFTH}
        size={ButtonSize.MEDIUM}
        onPress={() => {
          handlePasswordReset();
        }}>
        {`Resetuj hasło`.toUpperCase()}
      </Button>
      <Typography
        onPress={() => {
          navigation.navigate(RouteEnum.LOGIN);
        }}
        fontSize={14}
        color={theme.colors.gray[100]}
        style={{ textDecorationLine: 'underline', textDecorationColor: theme.colors.gray[100] }}>
        Wróć do logowania
      </Typography>
    </Styled.Wrapper>
  );
};
