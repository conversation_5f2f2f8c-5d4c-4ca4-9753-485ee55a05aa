import { View } from 'react-native';

import { Header } from '../../components/Header';

import * as Styled from './PasswordReset.styled';

import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { Typography } from '~/components/Typography/Typography';
import { useNavigate } from '~/hooks/useNavigate';
import { RouteEnum } from '~/routes/routes';
import theme from '~/theme';

export const Summary = () => {
  const { navigation } = useNavigate();

  return (
    <Styled.Wrapper>
      <Header />
      <View>
        <Typography
          fontSize={18}
          fontWeight="600"
          color={theme.colors.purple[100]}
          style={{ textAlign: 'center', paddingBottom: 12 }}>
          Hasło zresetowano pomyślnie!
        </Typography>
        <Typography
          fontSize={16}
          fontWeight="400"
          color={theme.colors.gray[100]}
          style={{ textAlign: 'center' }}>
          Zaloguj się na konto przy uży<PERSON>u nowego hasła.
        </Typography>
      </View>
      <Button
        variant={ButtonVariant.FIFTH}
        size={ButtonSize.MEDIUM}
        onPress={() => {
          navigation.navigate(RouteEnum.LOGIN);
        }}>
        {'Zaloguj się'.toUpperCase()}
      </Button>
    </Styled.Wrapper>
  );
};
