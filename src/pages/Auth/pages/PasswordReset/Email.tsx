import { useState } from 'react';

import { Header } from '../../components/Header';

import * as Styled from './PasswordReset.styled';

import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { InputPasswordReset } from '~/components/Inputs/Input/Input';
import { Typography } from '~/components/Typography/Typography';
import { handleGetOTC as getOTC } from '~/helpers/handleGetOTC';
import { useNavigate } from '~/hooks/useNavigate';
import { RouteEnum } from '~/routes/routes';
import theme from '~/theme';
import { IStatus } from '~/types/errors';

export const EmailScreen = () => {
  const [email, setEmail] = useState<string>('');
  const [error, setError] = useState<IStatus | undefined>();
  const { navigation } = useNavigate();

  const handleGetOTC = () => {
    // Trim email before sending
    const trimmedEmail = email.toLowerCase().trim();

    getOTC(trimmedEmail, 'password_reset')
      .then(() => {
        setError(undefined);
        navigation.navigate(RouteEnum.PASSWORD_RESET_OTC, { email: trimmedEmail });
      })
      .catch(() => {
        setError({
          status: 'error',
          message: 'Nie udało się wysłać kodu weryfikacyjnego. Sprawdź poprawność adresu email.',
        });
      });
  };

  return (
    <Styled.Wrapper>
      <Header />
      <InputPasswordReset
        value={email}
        onChangeText={setEmail}
        key={'email'}
        label={'Email'}
        name={'email'}
      />
      <Button
        variant={ButtonVariant.FIFTH}
        size={ButtonSize.MEDIUM}
        onPress={() => {
          handleGetOTC();
        }}>
        {'Resetuj hasło'.toUpperCase()}
      </Button>

      {error && (
        <Typography
          fontSize={14}
          color={error.status === 'error' ? theme.colors.red[100] : theme.colors.purple[100]}
          style={{ textAlign: 'center' }}>
          {error.message}
        </Typography>
      )}
      <Typography
        onPress={() => {
          navigation.navigate(RouteEnum.LOGIN);
        }}
        fontSize={14}
        color={theme.colors.gray[100]}
        style={{ textDecorationLine: 'underline', textDecorationColor: theme.colors.gray[100] }}>
        Wróć do logowania
      </Typography>

      <Typography fontSize={16} color={theme.colors.black[100]} style={{ textAlign: 'center' }}>
        Pamiętaj - jeśli zalogowałeś się poprzez konto Google lub AppleID, nie ma możliwości zmiany
        hasła.
      </Typography>
    </Styled.Wrapper>
  );
};
