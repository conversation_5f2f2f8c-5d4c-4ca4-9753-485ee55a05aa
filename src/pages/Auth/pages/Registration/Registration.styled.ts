import styled from '@emotion/native';

import { LAYOUT_PADDING } from '~/helpers/layoutPadding';
import { form } from '~/pages/Auth/styles/common.styled';
import { flexColBetween, flexColCenter } from '~/styles/flex.styled';

export const ScrollWrapper = styled.ScrollView`
  height: 100%;
  width: 100%;
`;

export const Wrapper = styled.View`
  padding: ${`${LAYOUT_PADDING}px`};
  width: 100%;
  ${flexColCenter};
  gap: 10px;
  justify-content: space-between;
`;
export const VerificationWrapper = styled.View`
  padding: ${`${LAYOUT_PADDING}px`};
  width: 100%;
  height: 100%;
  ${flexColCenter};
  gap: 16px;
`;

export const Form = styled.View`
  ${form};
`;

export const ButtonWrapper = styled.View`
  width: 100%;
  ${flexColBetween};
  gap: 16px;
`;
