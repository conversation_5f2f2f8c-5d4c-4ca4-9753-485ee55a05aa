import { FormProvider } from 'react-hook-form';
import { Dimensions, View } from 'react-native';

import * as Styled from './Registration.styled';

import { Loader } from '~/components/Loader/Loader';
import { Typography } from '~/components/Typography/Typography';
import { useRegistration } from '~/hooks/useRegistration';
import { BackButton } from '~/pages/Auth/components/BackButton';
import { Header } from '~/pages/Auth/components/Header';
import { RegistrationForm } from '~/pages/Auth/pages/Registration/components/RegistrationForm';
import { VerificationForm } from '~/pages/Auth/pages/Registration/components/VerificationForm';
import theme from '~/theme';

const ErrorMsg = ({ msg }: { msg: string }) => (
  <Typography fontSize={14} color={theme.colors.red[100]}>
    {msg}
  </Typography>
);
export const Registration = () => {
  const { form, loader, status, verificationCode, setStatus, handleRegistration, handleSubmit } =
    useRegistration();

  return (
    <Styled.ScrollWrapper>
      <View
        style={{
          height: Dimensions.get('window').height,
          justifyContent: 'center',
        }}>
        <Styled.Wrapper>
          <Header />
          {status?.status === 'error' ? <ErrorMsg msg={status.message} /> : null}

          <FormProvider {...form}>
            {loader ? (
              <Loader />
            ) : !verificationCode ? (
              <RegistrationForm handleSubmit={handleSubmit} />
            ) : (
              <Styled.VerificationWrapper>
                <VerificationForm handleRegistration={handleRegistration} />
              </Styled.VerificationWrapper>
            )}
          </FormProvider>
          <BackButton
            onPress={() => setStatus(undefined)}
            data={form.getValues()}
            status={status}
          />
        </Styled.Wrapper>
      </View>
    </Styled.ScrollWrapper>
  );
};
