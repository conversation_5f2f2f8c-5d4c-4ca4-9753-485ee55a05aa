import * as Styled from './Status.styled';

import { Icon } from '~/components/Icon/Icon';
import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';
import { assets } from '~/theme/assets';
import { IStatus } from '~/types/errors';

export const Status = ({ status }: { status: IStatus }) => {
  return (
    <Styled.Wrapper>
      <Styled.IconWrapper>
        <Icon
          width={40}
          height={40}
          isFill={true}
          isStroke={false}
          fill={theme.colors.purple[100]}
          iconSVG={
            status.status === 'error' ? assets.icons.cross_filter_svg : assets.icons.success_svg
          }
        />
      </Styled.IconWrapper>
      <Typography
        fontSize={20}
        fontWeight="600"
        color={status.status === 'error' ? theme.colors.black[300] : theme.colors.purple[100]}
        style={{ textAlign: 'center' }}>
        {status.message}
      </Typography>
    </Styled.Wrapper>
  );
};
