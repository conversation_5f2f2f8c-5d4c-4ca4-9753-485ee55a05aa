import styled from '@emotion/native';

import { flexColCenter } from '~/styles/flex.styled';
import theme from '~/theme';

export const Wrapper = styled.View`
  ${flexColCenter};
  padding-top: 32px;
  margin-bottom: 32px;
  gap: 32px;
`;

export const IconWrapper = styled.View`
  ${flexColCenter};
  padding: 24px;
  border-width: 6px;
  border-radius: 100px;
  border-color: ${theme.colors.purple[100]};
`;
