import { useEffect, useState } from 'react';
import { useFormContext } from 'react-hook-form';

import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { Input } from '~/components/Inputs/Input/Input';
import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';

interface IVerificationForm {
  handleRegistration: (data: any) => void;
}

export const VerificationForm = ({ handleRegistration }: IVerificationForm) => {
  const { watch, resetField } = useFormContext();
  const [error, setError] = useState<string>('');
  const watchAllFields = watch();

  useEffect(() => {
    /**
     * @description
     * @function
     * UseEffect for remove Code object from form state if code is empty
     */

    if (watchAllFields.verification_code?.length === 0) {
      resetField('verification_code');
    } else {
      setError('');
    }
  }, [watchAllFields.verification_code]);

  const handleCheckCode = () => {
    if (watchAllFields.verification_code?.length === 0 || !watchAllFields.verification_code) {
      setError('Wpisz kod lub wybierz opcje "Pomiń weryfikacje"');
    } else {
      setError('');

      // Trim verification code before sending
      const trimmedFields = {
        ...watchAllFields,
        verification_code: watchAllFields.verification_code.trim(),
      };

      handleRegistration(trimmedFields);
    }
  };

  return (
    <>
      <Typography
        fontSize={20}
        fontWeight="700"
        color={theme.colors.purple[100]}
        style={{ textAlign: 'center' }}>
        {'Na podany przez Ciebie adres email wysłaliśmy kod weryfikacyjny'}
        {/*{JSON.stringify(watchAllFields)}*/}
      </Typography>
      <Input label={'Kod weryfikacyjny'} name={'verification_code'} customError={error} />
      <Button variant={ButtonVariant.FIFTH} size={ButtonSize.SMALL} onPress={handleCheckCode}>
        {'Zweryfikuj adres e-mail'.toUpperCase()}
      </Button>
      {watchAllFields.verification_code?.length > 0 ? null : (
        <Button
          variant={ButtonVariant.SECONDARY}
          size={ButtonSize.SMALL}
          onPress={() => handleRegistration(watchAllFields)}>
          {'Pomiń weryfikacje'.toUpperCase()}
        </Button>
      )}
    </>
  );
};
