import { useFormContext } from 'react-hook-form';

import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { Input } from '~/components/Inputs/Input/Input';
import { SocialAuth } from '~/pages/Auth/components/SocialAuth';
import { CheckboxSection } from '~/pages/Auth/pages/Registration/components/CheckboxSection';
import * as Styled from '~/pages/Auth/pages/Registration/Registration.styled';
import { assets } from '~/theme/assets';

interface Props {
  handleSubmit: (data: any) => void;
}

export const RegistrationForm = ({ handleSubmit }: Props) => {
  const form = useFormContext();

  return (
    <>
      <Styled.Form>
        <Input label="Imię" name="name" />
        <Input label="e-mail" name="email" />
        <Input keyboardType="numeric" label="telefon" name="phone" maxLength={12} />
        <Input
          label="hasło"
          icon={assets.icons.eye_password_icon_svg}
          isPassword={true}
          name="password"
        />
        <Input
          label="powtórz hasło"
          icon={assets.icons.eye_password_icon_svg}
          isPassword={true}
          name="confirmPassword"
        />
        <CheckboxSection />
      </Styled.Form>
      <Styled.ButtonWrapper>
        <Button
          variant={ButtonVariant.FIFTH}
          size={ButtonSize.MEDIUM}
          onPress={form.handleSubmit(handleSubmit, (onErrors) => console.log(onErrors))}>
          {'Zarejestruj się'.toUpperCase()}
        </Button>
        <SocialAuth />
      </Styled.ButtonWrapper>
    </>
  );
};
