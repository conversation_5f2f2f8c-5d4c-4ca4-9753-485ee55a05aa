import { useFormContext } from 'react-hook-form';

import * as Styled from './CheckboxSection.styled';

import { Checkbox } from '~/components/Checkbox/Checkbox';
import { Typography } from '~/components/Typography/Typography';
import { useNavigate } from '~/hooks/useNavigate';
import { useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import theme from '~/theme';
import { IConsents } from '~/validation/registrationForm/types';

export const CheckboxSection = () => {
  const privacyConsents = useAppSelector((state) => state.privacy.consents);
  const {
    setValue,
    watch,
    clearErrors,
    formState: { errors },
  } = useFormContext();
  const watchAllFields = watch();
  const { navigation } = useNavigate();

  const handleRequired = (state: boolean) => {
    let consentsArray = watchAllFields.consents;
    if (state) {
      consentsArray.push(...privacyConsents.filter((consent) => consent.is_required));
    } else {
      consentsArray = consentsArray.filter(
        (obj: IConsents) =>
          !privacyConsents
            .filter((consent) => consent.is_required)
            .map((consent) => consent.code_name)
            .includes(obj.code_name),
      );
    }
    setValue('consents', consentsArray);
    clearErrors('consents');
  };

  // TODO
  // const handleOptional = (state: boolean) => {
  //   let consentsArray = watchAllFields.consents;
  //   if (state) {
  //     consentsArray.push(...privacyConsents.filter((consent) => !consent.is_required));
  //   } else {
  //     consentsArray = consentsArray.filter(
  //       (obj: IConsents) =>
  //         !privacyConsents
  //           .filter((consent) => !consent.is_required)
  //           .map((consent) => consent.code_name)
  //           .includes(obj.code_name),
  //     );
  //   }
  //   setValue('consents', consentsArray);
  // };

  return (
    <Styled.CheckboxWrapper>
      <Checkbox onStateChange={handleRequired}>
        <Styled.CheckboxText>
          <Typography fontSize={10} fontWeight="700" color={theme.colors.black[300]}>
            *
          </Typography>
          <Typography fontSize={10} fontWeight="500" color={theme.colors.black[300]}>
            Zapoznałem się z
          </Typography>
          <Typography
            fontSize={10}
            fontWeight="700"
            color={theme.colors.black[300]}
            style={{
              textDecorationLine: 'underline',
              textDecorationStyle: 'solid',
              textDecorationColor: theme.colors.black['100'],
            }}
            onPress={() => navigation.navigate(RouteEnum.POLICY, { type: 'terms' })}>
            regulaminem
          </Typography>
          <Typography fontSize={10} fontWeight="500" color={theme.colors.black[300]}>
            i
          </Typography>
          <Typography
            fontSize={10}
            fontWeight="700"
            color={theme.colors.black[300]}
            style={{
              textDecorationLine: 'underline',
              textDecorationStyle: 'solid',
              textDecorationColor: theme.colors.black['100'],
            }}
            onPress={() => navigation.navigate(RouteEnum.POLICY, { type: 'policy' })}>
            polityką prywatności
          </Typography>
        </Styled.CheckboxText>
      </Checkbox>
      {/*// TODO <Checkbox onStateChange={handleOptional}>
        <Typography fontSize={10} fontWeight={500} color={theme.colors.black[300]}>
          Wyrażam zgodę na udostępnianie danych w celach marketingowych
        </Typography>
      </Checkbox> */}
      {errors ? (
        <Typography fontSize={10} fontWeight="500" color={'red'}>
          {errors.consents?.message as string}
        </Typography>
      ) : null}
    </Styled.CheckboxWrapper>
  );
};
