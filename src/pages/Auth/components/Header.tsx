import * as Styled from './Header.styled';

import { Icon } from '~/components/Icon/Icon';
import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';
import { assets } from '~/theme/assets';

export const Header = () => {
  return (
    <Styled.Header>
      <Icon iconSVG={assets.icons.logo_svg} width={80} height={80} />
      <Typography fontSize={28} fontWeight="600" color={theme.colors.black[300]}>
        HomeProfit
      </Typography>
    </Styled.Header>
  );
};
