import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { Typography } from '~/components/Typography/Typography';
import { useLogin } from '~/hooks/useLogin';
import { useNavigate } from '~/hooks/useNavigate';
import { IStatus } from '~/pages/Auth/pages/Registration/types';
import { RouteEnum } from '~/routes/routes';
import theme from '~/theme';
import { TForm } from '~/validation/registrationForm/types';

export const BackButton = ({
  status,
  data,
  onPress,
}: {
  status: IStatus | undefined;
  data: TForm | null;
  onPress: () => void;
}) => {
  const { navigation } = useNavigate();
  const { handleLogin } = useLogin();

  if (status && status.status === 'success')
    return (
      <Button
        variant={ButtonVariant.FIFTH}
        size={ButtonSize.MEDIUM}
        onPress={() => {
          if (data) handleLogin(data);
          else navigation.navigate(RouteEnum.LOGIN);
        }}>
        {'Zaloguj się'.toUpperCase()}
      </Button>
    );
  else if (status && status.status === 'error') {
    return (
      <Typography
        onPress={() => {
          navigation.navigate(RouteEnum.LOGIN);
          onPress();
        }}
        fontSize={14}
        color={theme.colors.gray[100]}
        style={{ textDecorationLine: 'underline', textDecorationColor: theme.colors.gray[100] }}>
        Wróć do logowania
      </Typography>
    );
  } else {
    return (
      <Typography
        onPress={() => {
          navigation.navigate(RouteEnum.LOGIN);
          onPress();
        }}
        fontSize={14}
        color={theme.colors.gray[100]}
        style={{ textDecorationLine: 'underline', textDecorationColor: theme.colors.gray[100] }}>
        Wróć do logowania
      </Typography>
    );
  }
};
