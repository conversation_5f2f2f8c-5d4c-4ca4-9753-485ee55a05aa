import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

import * as Styled from './RegisterLink.styled';

import { Typography } from '~/components/Typography/Typography';
import { contentApi } from '~/redux/api/contentApi';
import { userLogout } from '~/redux/reducers/user/actions';
import { useAppDispatch } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { RootStackParamList } from '~/routes/types';
import theme from '~/theme';

export const RegisterLink = () => {
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  // const dispatch = useAppDispatch();
  return (
    <Styled.Wrapper>
      <Typography fontSize={14} fontWeight="400" color={theme.colors.black[300]}>
        Nie masz konta?
      </Typography>
      <Typography
        onPress={() => {
          // dispatch(userLogout());
          navigation.replace(RouteEnum.REGISTRATION);
        }}
        fontSize={14}
        fontWeight="600"
        color={theme.colors.black[300]}
        style={{ textDecorationLine: 'underline', textDecorationColor: theme.colors.black[300] }}>
        Zarejestruj się
      </Typography>
    </Styled.Wrapper>
  );
};
