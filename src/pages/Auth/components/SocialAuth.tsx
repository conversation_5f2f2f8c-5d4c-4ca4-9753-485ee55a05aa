//TODO
import { Platform } from 'react-native';
import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { Separator } from '~/components/Separator/Separator';
import { useAppleLogin } from '~/hooks/useAppleLogin';
import { useGoogleLogin } from '~/hooks/useGoogleLogin';
import { assets } from '~/theme/assets';

export const SocialAuth = () => {
  const { googleLogin } = useGoogleLogin();
  const { appleLogin } = useAppleLogin();

  return (
    <>
      <Separator />
      <Button
        onPress={googleLogin}
        size={ButtonSize.MEDIUM}
        variant={ButtonVariant.SECONDARY}
        icon={assets.icons.google_svg}
        iconPosition={'right'}>
        {'Dołącz z google'.toUpperCase()}
      </Button>
      {Platform.OS === 'ios' && (
        <Button
          onPress={appleLogin}
          variant={ButtonVariant.SECONDARY}
          size={ButtonSize.MEDIUM}
          icon={assets.icons.apple_logo_svg}
          iconPosition={'right'}>
          {'Dołącz z Apple Id'.toUpperCase()}
        </Button>
      )}
    </>
  );
};
