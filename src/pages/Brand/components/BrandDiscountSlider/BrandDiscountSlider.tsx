import { useContext } from 'react';
import { View } from 'react-native';

import { brandContext } from '../../context/brandContext';

import { DiscountCard } from '~/components/Cards/DiscountCard/DiscountCard';
import { Section } from '~/components/Section/Section';
import { ContentSlider } from '~/components/Slider/Slider';
import { FilterContext } from '~/context/filterContext';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useNavigate } from '~/hooks/useNavigate';
import { RouteEnum } from '~/routes/routes';

export const BrandDiscountSlider = () => {
  const context = useContext(brandContext);
  const _filterContext = useContext(FilterContext);
  const { navigateToDiscountDetails, navigateToDiscountCollection } = useNavigate();
  const { postFavorite } = useFavoritesQuery();

  return (
    <Section
      isLoading={context.similar.discounts.length === 0}
      title={`Rabaty\n${context.name}`}
      counter={`[${context.counters.discounts}]`}
      onSectionHeaderPress={() => {
        _filterContext.clear();
        _filterContext.handleSimilarCategoryCollection(context.categories);
        _filterContext.handleFilterByBrandId(context.id);
        navigateToDiscountCollection({
          params: {
            category_ids: context.categories,
          },
          prevScreen: RouteEnum.DISCOUNT_DETAIL,
        });
      }}>
      <ContentSlider
        data={context.similar.discounts.slice(0, 10)}
        renderItem={({ item, index }) => (
          <View style={{ width: 170 }}>
            <DiscountCard
              onFavoritePress={(objectId, isFavorite, categories, tags) => {
                postFavorite({
                  isFavorite,
                  params: {
                    payload: {
                      object_id: objectId,
                      object_type: ObjectTypeEnum.Card,
                    },
                    type: 'Card',
                  },
                  event: {
                    object_type: ObjectTypeEnum.Card,
                    categories_id_list: categories || [],
                    tags_id_list: tags || [],
                  },
                });
              }}
              key={index}
              data={item}
              onPress={() => navigateToDiscountDetails(item.id)}
            />
          </View>
        )}
      />
    </Section>
  );
};
