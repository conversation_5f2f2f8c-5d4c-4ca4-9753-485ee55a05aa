import styled from '@emotion/native';
import { Dimensions } from 'react-native';

import { flexColCenter } from '~/styles/flex.styled';
import theme from '~/theme';
import { shadow } from '~/theme/shadows';

interface IWrapper {
  bgColor?: string;
}

const deviceWidth = Dimensions.get('window').width;
export const Wrapper = styled.View`
  height: ${deviceWidth > 500 ? '300px' : '230px'};
  gap: 40px;
`;
export const Container = styled.View`
  flex: 1;
  width: 100%;
  height: 160px;
  position: relative;
`;

export const ImageWrapper = styled.View<IWrapper>`
  background-color: ${({ bgColor }) => (bgColor ? bgColor : theme.colors.purple[100])};
  border-radius: 6px;
  gap: 14px;
  flex: 1;
`;

export const TitleWrapper = styled.View``;

export const BrandWrapper = styled.TouchableOpacity`
  ${flexColCenter};
  flex: 1;
  border-radius: 100px;
  position: absolute;
  right: 16px;
  bottom: -20%;
  z-index: 100;
  width: 120px;
  height: 120px;
  ${shadow.gray[200]}
`;

export const HeartIcon = styled.View`
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 100;
`;
