import React, { useContext } from 'react';

import * as Styled from './BrandHeader.styled';

import { CardFavourite } from '~/components/Cards/components/CardFavourite';
import { ImageComponent } from '~/components/ImageComponent/ImageComponent';
import { Typography } from '~/components/Typography/Typography';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { brandContext } from '~/pages/Brand/context/brandContext';

export const BrandHeader = () => {
  const context = useContext(brandContext);
  const { postFavorite } = useFavoritesQuery();

  return (
    <Styled.Wrapper>
      <Styled.Container>
        <Styled.ImageWrapper bgColor={context.bg_color}>
          <Styled.BrandWrapper>
            <ImageComponent
              borderRadius={16}
              uri={context.logo}
              width={86}
              height={86}
              resizeMode={'contain'}
              style={{ aspectRatio: 1 }}
              bgColor={context.bg_color}
            />
          </Styled.BrandWrapper>
          <Styled.HeartIcon>
            <CardFavourite
              active={context.is_favorite}
              onPress={(isFavorite) => {
                postFavorite({
                  isFavorite,
                  params: {
                    payload: {
                      object_id: context.id,
                      object_type: ObjectTypeEnum.Brand,
                    },
                    type: 'Brand',
                  },
                  event: {
                    object_type: ObjectTypeEnum.Brand,
                    categories_id_list: context.categories || [],
                    tags_id_list: context.tags || [],
                  },
                });
              }}
            />
          </Styled.HeartIcon>
        </Styled.ImageWrapper>
      </Styled.Container>
      <Styled.TitleWrapper>
        {context.name ? (
          <Typography fontSize={24} fontWeight="600">
            {context.name.toUpperCase()}
          </Typography>
        ) : null}
      </Styled.TitleWrapper>
    </Styled.Wrapper>
  );
};
