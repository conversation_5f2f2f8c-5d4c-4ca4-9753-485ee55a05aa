import React, { useContext, useState } from 'react';

import * as Styled from './BrandDescription.styled';

import { Icon } from '~/components/Icon/Icon';
import { Typography } from '~/components/Typography/Typography';
import { IUseLayoutAnimationProps, useLayoutAnimation } from '~/hooks/useLayoutAnimation';
import { brandContext } from '~/pages/Brand/context/brandContext';
import { assets } from '~/theme/assets';

export const BrandDescription = () => {
  const context = useContext(brandContext);
  const [isExpanded, setIsExpanded] = useState(false);
  const layoutAnimOptions: IUseLayoutAnimationProps = {
    type: 'linear',
    property: 'opacity',
    delay: 0,
    duration: 100,
  };
  const { animate } = useLayoutAnimation(layoutAnimOptions);
  const handlePress = () => {
    animate();
    setIsExpanded(!isExpanded);
  };
  return (
    <Styled.Container onPress={handlePress}>
      {context.description ? (
        <>
          <Styled.AbsoluteWrapper>
            {isExpanded ? (
              <Typography fontSize={14} fontWeight="400">
                {context.description}
              </Typography>
            ) : (
              <Typography fontSize={14} fontWeight="400">
                {context.description.slice(0, 200)}
              </Typography>
            )}
            <Styled.Gradient
              isExpanded={isExpanded}
              colors={
                isExpanded
                  ? ['transparent', 'rgba(255, 255, 255, 0)']
                  : ['rgba(255, 255, 255, 0)', 'rgba(255, 255, 255, 1)']
              }
            />
          </Styled.AbsoluteWrapper>
          <Styled.IconWrapper onPress={handlePress}>
            <Icon
              iconSVG={assets.icons.arrow_down_svg}
              width={24}
              height={24}
              rotation={isExpanded ? 180 : 0}
            />
          </Styled.IconWrapper>
        </>
      ) : null}
    </Styled.Container>
  );
};
