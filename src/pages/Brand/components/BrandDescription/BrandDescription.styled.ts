import styled from '@emotion/native';
import LinearGradient from 'react-native-linear-gradient';

export const Container = styled.TouchableOpacity`
  width: 100%;
  padding: 0 10px 0 10px;
`;

export const AbsoluteWrapper = styled.View`
  position: relative;
`;

export const IconWrapper = styled.TouchableOpacity`
  width: 100%;
  display: flex;
  align-items: center;
  padding: 5px;
`;

export const Gradient = styled(LinearGradient)<{ isExpanded: boolean }>`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: ${({ isExpanded }) => (isExpanded ? '0' : '100px')};
  justify-content: flex-end;
  align-items: center;
`;
