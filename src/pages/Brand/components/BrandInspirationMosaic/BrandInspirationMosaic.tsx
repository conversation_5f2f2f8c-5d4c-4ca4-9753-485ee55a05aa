import React, { useContext } from 'react';
import { View } from 'react-native';

import { Inspiration } from '~/components/Inspiration/Inspiration';
import { Section } from '~/components/Section/Section';
import { FilterContext } from '~/context/filterContext';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useNavigate } from '~/hooks/useNavigate';
import { brandContext } from '~/pages/Brand/context/brandContext';
import { RouteEnum } from '~/routes/routes';
import theme from '~/theme';

export const BrandInspirationMosaic = () => {
  const context = useContext(brandContext);
  const _filterContext = useContext(FilterContext);
  const { navigateToInspirationDetails, navigateToInspirationCollection } = useNavigate();
  const { postFavorite } = useFavoritesQuery();
  return (
    <Section
      style={{ paddingTop: 20 }}
      showBottomLine={false}
      bgColor={theme.colors.white['100']}
      title={`Inspiracje\n${context.name}`}
      counter={`[${context.counters.inspirations}]`}
      onSectionHeaderPress={() => {
        _filterContext.clear();
        _filterContext.handleSimilarCategoryCollection(context.categories);
        _filterContext.handleFilterByBrandId(context.id);
        navigateToInspirationCollection({
          params: {
            category_ids: context.categories,
          },
          prevScreen: RouteEnum.BRAND_DETAIL,
        });
      }}>
      <View style={{ padding: 16 }}>
        <Inspiration
          onFavoritePress={(objectId, isFavorite, categories, tags) => {
            postFavorite({
              isFavorite,
              params: {
                payload: {
                  object_id: objectId,
                  object_type: ObjectTypeEnum.Inspiration,
                },
                type: 'Inspiration',
              },
              event: {
                object_type: ObjectTypeEnum.Inspiration,
                categories_id_list: categories || [],
                tags_id_list: tags || [],
              },
            });
          }}
          data={context.similar.inspirations.slice(0, 10)}
          onPress={(id) => navigateToInspirationDetails(id)}
        />
      </View>
    </Section>
  );
};
