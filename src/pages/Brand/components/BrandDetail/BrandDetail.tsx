import Bugsnag from '@bugsnag/react-native';
import { useContext, useEffect } from 'react';

import * as Styled from './BrandDetail.styled';

import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { DetailSection } from '~/components/DetailSection/DetailSection';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { ShopTypeEnum } from '~/enums/shared';
import { useRedirectionUrl } from '~/events/hooks';
import { useLinking } from '~/hooks/useLinking';
import { useNavigate } from '~/hooks/useNavigate';
import { brandContext } from '~/pages/Brand/context/brandContext';
import { getBrandAddresses } from '~/redux/reducers/content/brands/thunks';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import theme from '~/theme';

export const BrandDetail = () => {
  const { openUrl } = useLinking();
  const { navigation } = useNavigate();
  const { addresses } = useAppSelector((state) => state.brands);
  const dispatch = useAppDispatch();
  const context = useContext(brandContext);

  const handleFindShop = () => {
    dispatch(getBrandAddresses({ id: context.id }))
      .then(() => {
        navigation.navigate(RouteEnum.FIND_SHOP_MODAL_SCREEN, {
          screenType: ObjectTypeEnum.Brand,
          shopId: context.id,
        });
      })
      .catch((err) => {
        Bugsnag.notify(new Error(err));
      });
  };

  useEffect(() => {
    if (context.id) {
      dispatch(getBrandAddresses({ id: context.id }));
    }
  }, [context.id]);

  //----EVENTS----
  const _fnUrlEvent = useRedirectionUrl(() => openUrl(context.site));
  // --X--EVENTS--X--
  return (
    <Styled.Container>
      {addresses.length > 0 ? (
        context.shop_type !== ShopTypeEnum.ONLINE ? (
          <DetailSection leftText={'Sklepy: '}>
            <Styled.ButtonWrapper>
              <Button
                variant={ButtonVariant.FOURTH}
                typographyStyles={{
                  fontSize: 12,
                  fontWeight: '400',
                  color: theme.colors.purple['100'],
                }}
                size={ButtonSize.SMALL}
                onPress={handleFindShop}>
                {`znajdź sklep`.toUpperCase()}
              </Button>
            </Styled.ButtonWrapper>
          </DetailSection>
        ) : null
      ) : null}
      <DetailSection leftText="Strona internetowa: " bottomBorder={true}>
        <Styled.ButtonWrapper>
          <Button
            variant={ButtonVariant.FOURTH}
            typographyStyles={{
              fontSize: 12,
              fontWeight: '400',
              color: theme.colors.purple['100'],
            }}
            size={ButtonSize.SMALL}
            onPress={() => _fnUrlEvent(context.id, context.id, ObjectTypeEnum.Brand, context.site)}>
            {`${context.site_btn_text !== null ? context.site_btn_text : 'WWW'}`}
          </Button>
        </Styled.ButtonWrapper>
      </DetailSection>
    </Styled.Container>
  );
};
