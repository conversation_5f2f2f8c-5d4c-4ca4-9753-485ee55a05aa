import React, { useContext } from 'react';
import { View } from 'react-native';

import { NewspaperCard } from '~/components/Cards/NewspaperCard/NewspaperCard';
import { Section } from '~/components/Section/Section';
import { ContentSlider } from '~/components/Slider/Slider';
import { FilterContext } from '~/context/filterContext';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useNavigate } from '~/hooks/useNavigate';
import { brandContext } from '~/pages/Brand/context/brandContext';
import { RouteEnum } from '~/routes/routes';

export const BrandNewspaperSlider = () => {
  const context = useContext(brandContext);
  const { navigateToNewspaperCollection, navigateToNewspaperDetails } = useNavigate();
  const _filterContext = useContext(FilterContext);
  const { postFavorite } = useFavoritesQuery();

  return (
    <Section
      showBottomLine={false}
      isLoading={context.similar.newspapers.length === 0}
      title={`Gazetki\n${context.name}`}
      counter={`[${context.counters.newspapers}]`}
      onSectionHeaderPress={() => {
        _filterContext.clear();
        _filterContext.handleSimilarCategoryCollection(context.categories);
        _filterContext.handleFilterByBrandId(context.id);
        navigateToNewspaperCollection({
          params: {
            category_ids: context.categories,
          },
          prevScreen: RouteEnum.BRAND_DETAIL,
        });
      }}>
      <ContentSlider
        data={context.similar.newspapers.slice(0, 10)}
        renderItem={({ item, index }) => (
          <View style={{ width: 175, height: 350 }}>
            <NewspaperCard
              key={index}
              data={item}
              onFavoritePress={(objectId, isFavorite, categories, tags) => {
                postFavorite({
                  isFavorite,
                  params: {
                    payload: {
                      object_id: objectId,
                      object_type: ObjectTypeEnum.Leaflet,
                    },
                    type: 'Leaflet',
                  },
                  event: {
                    object_type: ObjectTypeEnum.Leaflet,
                    categories_id_list: categories || [],
                    tags_id_list: tags || [],
                  },
                });
              }}
              onPress={() => navigateToNewspaperDetails(item.id)}
            />
          </View>
        )}
      />
    </Section>
  );
};
