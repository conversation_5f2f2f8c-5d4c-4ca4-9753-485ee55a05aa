import { createContext, ReactNode } from 'react';

import { IAddFavoriteMethod } from '~/hooks/useFavorites';
import { IBrandDetail } from '~/types/brands';
import { ICoupon } from '~/types/coupon';
import { IDiscount } from '~/types/discount';
import { IInspiration } from '~/types/inspirations';
import { INewspaper } from '~/types/newspaper';

export interface IBrandDetailContext extends IBrandDetail {
  similar: {
    discounts: IDiscount[];
    coupons: ICoupon[];
    newspapers: INewspaper[];
    inspirations: IInspiration[];
  };
  counters: {
    discounts: number;
    coupons: number;
    newspapers: number;
    inspirations: number;
  };
}

export interface IBrandContextProvider {
  providerValues: IBrandDetailContext;
  children: ReactNode;
}

const defaultContext: IBrandDetailContext = {
  id: 0,
  name: '',
  logo: '',
  description: '',
  site: '',
  categories: [],
  is_favorite: false,
  shop_type: null,
  site_btn_text: null,
  similar: {
    discounts: [],
    coupons: [],
    newspapers: [],
    inspirations: [],
  },
  counters: {
    discounts: 0,
    coupons: 0,
    newspapers: 0,
    inspirations: 0,
  },
};

export const brandContext = createContext<IBrandDetailContext>(defaultContext);

export const BrandContextProvider = ({ providerValues, children }: IBrandContextProvider) => {
  return <brandContext.Provider value={providerValues}>{children}</brandContext.Provider>;
};
