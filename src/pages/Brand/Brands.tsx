import { useNetInfo } from '@react-native-community/netinfo';
import { useFocusEffect } from '@react-navigation/native';
import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
// TODO: hide search button
import { RefreshControl, View } from 'react-native';

import { BrandCard } from '~/components/Cards/BrandCard/BrandCard';
import { DiscountCardLofi } from '~/components/Cards/DiscountCard/DiscountCard';
import { EmptyData } from '~/components/EmptyData/EmptyData';
import { FilterLayout } from '~/components/Filters/FilterLayout';
// TODO: hide search button
import { Layout } from '~/components/Layout/Layout';
import { List } from '~/components/List/List';
import { ListItem } from '~/components/List/ListItem';
import { Search } from '~/components/Search';
import { FilterContext } from '~/context/filterContext';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useDisplayContentType, useFilterCategoriesChange } from '~/events/hooks';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useListLayout } from '~/hooks/useListLayout';
import { useNavigate } from '~/hooks/useNavigate';
import { useResetSearchWithBack } from '~/hooks/useResetSearchWithBack';
import { useGetBrandsQuery, useGetBrandsWithPaginationQuery } from '~/redux/api/brands';
import { useLazyGetCategoriesQuery } from '~/redux/api/categories';
import { useAppSelector } from '~/redux/store';
import * as Styled from '~/styles/common.styled';
import theme from '~/theme';
// TODO: hide search button
import { ParamsOptions } from '~/types/shared';

const ListEmptyComponent = () => {
  return (
    <>
      <View style={{ height: 230, flexDirection: 'row' }}>
        <DiscountCardLofi />
        <DiscountCardLofi />
      </View>
      <View style={{ height: 230, flexDirection: 'row' }}>
        <DiscountCardLofi />
        <DiscountCardLofi />
      </View>
    </>
  );
};
export const Brands = () => {
  const { navigateToBrandCollection, navigateToBrandDetails } = useNavigate();
  const { columnsSwitch, setColumnsSwitch, screenWidth, gap } = useListLayout();
  const _filterContext = useContext(FilterContext);
  const { handleBackWithReset } = useResetSearchWithBack();
  const { city } = useAppSelector((state) => state.user.detail);
  const [page, setPage] = useState(1);
  const { isConnected } = useNetInfo();

  let queryParams: ParamsOptions = useMemo(() => {
    const params = _filterContext.buildFilterParams();

    return {
      ...params,
      ...(page > 1 ? { page: page } : {}),
      city_id: city,
    };
  }, [
    _filterContext.selectedCategoryFilter,
    _filterContext.selectedShopFilter,
    _filterContext.selectedSortFilter,
    _filterContext.selectedSimilarCategoryFilter,
    _filterContext.searchId,
    _filterContext.searchString,
    city,
    page,
  ]);

  /**
   * RTK Query
   */
  const { data, isFetching, refetch } = useGetBrandsWithPaginationQuery(queryParams);
  const { data: mayInterestYouData } = useGetBrandsQuery({ city_id: city });
  const [trigger] = useLazyGetCategoriesQuery();
  const { postFavorite } = useFavoritesQuery();

  /*
   * Trigger next page
   */
  let isFetchingNextPage = false;
  const onEndReached = () => {
    if (!isConnected) return;
    if (data?.next === null || isFetching || isFetchingNextPage) return;
    isFetchingNextPage = true;
    setPage(page + 1);
    setTimeout(() => {
      isFetchingNextPage = false;
    }, 500);
  };

  /*
   * Fetch categories for specific object type (Card, Coupon etc.)
   */
  useEffect(() => {
    trigger({ content_type: ObjectTypeEnum.Brand }).then((res) => {
      _filterContext.setSelectedCategoryFilter(res.data);
    });
  }, []);

  /**
   * Events:
   */
  useDisplayContentType({
    type: ObjectTypeEnum.Brand,
    collection: data,
  });

  useFilterCategoriesChange(_filterContext.buildFilterParams().category_ids || []);

  /**
   * Reset page on focus (it triggers reset cache of rtk query and fetch new data from server)
   */
  useFocusEffect(
    useCallback(() => {
      setPage(1);
      return () => {
        setPage(1);
      };
    }, [
      _filterContext.selectedCategoryFilter,
      _filterContext.selectedShopFilter,
      _filterContext.selectedSortFilter,
      _filterContext.selectedSimilarCategoryFilter,
      _filterContext.searchId,
      _filterContext.searchString,
      city,
    ]),
  );

  useEffect(() => {
    refetch();
  }, [isConnected]);

  return (
    <Layout
      padding={0}
      onGoBack={handleBackWithReset}
      headerOptions={{
        resultCounter: data?.count,
        label: 'Strefa marek',
        leftIcon: true,
        rightItem: <Search to={ObjectTypeEnum.Brand} />,
        labelStyle: {
          fontSize: 16,
          fontWeight: '600',
          color: theme.colors.black['100'],
        },
      }}>
      <Styled.ListWrapper>
        <Styled.FilterWrapper>
          <FilterLayout
            removeSingleFilter={(id, type) => {
              setPage(1);
              _filterContext.removeSingleFilter(id, type);
            }}
            removeAllFilters={() => {
              setPage(1);
              _filterContext.clear();
            }}
            tiles={_filterContext.tiles}
            screenType={ObjectTypeEnum.Brand}
            setColumnsSwitch={setColumnsSwitch}
            columnsSwitch={columnsSwitch}
            singleSwitch={true}
          />
        </Styled.FilterWrapper>
        <Styled.ContentWhite>
          {isFetching || (data && data.results.length > 0) ? (
            <List
              contentContainerStyle={{ gap: 10 }}
              loading={isFetching}
              data={data?.results || []}
              columnsSwitch={columnsSwitch}
              onEndReached={data?.results && !isFetching ? onEndReached : () => {}}
              ListEmptyComponent={isFetching ? ListEmptyComponent : undefined}
              refreshControl={
                <RefreshControl
                  refreshing={isFetching}
                  onRefresh={() => {
                    setPage(1);
                  }}
                />
              }
              renderItem={({ item }) => {
                return (
                  <ListItem columnsSwitch={columnsSwitch} gap={gap} screenWidth={screenWidth}>
                    <BrandCard
                      data={item}
                      onFavoritePress={(objectId, isFavorite, categories, tags) => {
                        postFavorite({
                          isFavorite,
                          params: {
                            payload: {
                              object_id: objectId,
                              object_type: ObjectTypeEnum.Brand,
                            },
                            type: 'Brand',
                          },
                          event: {
                            object_type: ObjectTypeEnum.Brand,
                            categories_id_list: categories || [],
                            tags_id_list: tags || [],
                          },
                        });
                      }}
                      onPress={() => navigateToBrandDetails(item.id, item.is_rec)}
                    />
                  </ListItem>
                );
              }}
            />
          ) : null}

          {(!isFetching && !data) || data?.results.length === 0 ? (
            <EmptyData
              data={mayInterestYouData?.results.slice(0, 10)}
              counter={mayInterestYouData?.count}
              onSectionHeaderPress={() => {
                _filterContext.clear();
                navigateToBrandCollection();
              }}
              renderItem={({ item }) => {
                return (
                  <View style={{ width: 170 }}>
                    <BrandCard
                      data={item}
                      onFavoritePress={(objectId, isFavorite, categories, tags) => {
                        postFavorite({
                          isFavorite,
                          params: {
                            payload: {
                              object_id: objectId,
                              object_type: ObjectTypeEnum.Brand,
                            },
                            type: 'Brand',
                          },
                          event: {
                            object_type: ObjectTypeEnum.Brand,
                            categories_id_list: categories || [],
                            tags_id_list: tags || [],
                          },
                        });
                      }}
                      onPress={() => navigateToBrandDetails(item.id, item.is_rec)}
                    />
                  </View>
                );
              }}
            />
          ) : null}
        </Styled.ContentWhite>
      </Styled.ListWrapper>
    </Layout>
  );
};
