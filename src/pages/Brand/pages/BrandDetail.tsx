import { RouteProp, useRoute } from '@react-navigation/native';
import { useEffect, useMemo, useRef } from 'react';
import { ScrollView, View, RefreshControl } from 'react-native';

import { BrandCouponSlider } from '../components/BrandCouponSlider/BrandCouponSlider';
import { BrandInspirationMosaic } from '../components/BrandInspirationMosaic/BrandInspirationMosaic';
import { BrandNewspaperSlider } from '../components/BrandNewspaperSlider/BrandNewspaperSlider';

import * as Styled from './BrandDetail.styled';

import { Layout } from '~/components/Layout/Layout';
import { Loader } from '~/components/Loader/Loader';
import * as Lofi from '~/components/Loader/Lofi';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useDisplayCard, useInteraction } from '~/events/hooks';
import { useNavigate } from '~/hooks/useNavigate';
import { BrandDescription } from '~/pages/Brand/components/BrandDescription/BrandDescription';
import { BrandDetail as Detail } from '~/pages/Brand/components/BrandDetail/BrandDetail';
import { BrandDiscountSlider } from '~/pages/Brand/components/BrandDiscountSlider/BrandDiscountSlider';
import { BrandHeader } from '~/pages/Brand/components/BrandHeader/BrandHeader';
import { BrandContextProvider } from '~/pages/Brand/context/brandContext';
import { useGetBrandsDetailsQuery, useLazyGetBrandsDetailsQuery } from '~/redux/api/brands';
import { useGetCouponsQuery, useGetSimilarCouponsQuery } from '~/redux/api/coupons';
import { useGetCardsQuery, useGetSimilarCardsQuery } from '~/redux/api/discounts';
import { useGetInspirationsQuery, useGetSimilarInspirationsQuery } from '~/redux/api/inspirations';
import { useGetLeafletsQuery, useGetSimilarLeafletsQuery } from '~/redux/api/newspapers';
import { useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { RootStackParamList } from '~/routes/types';
import theme from '~/theme';
import { ParamsOptions } from '~/types/shared';

const BrandDetailLofi = () => {
  return (
    <Lofi.Details>
      <Lofi.Container>
        <View style={{ position: 'absolute', right: 32, bottom: -32 }}>
          <Lofi.Circle border />
        </View>
      </Lofi.Container>
      <View style={{ gap: 16, paddingTop: 32, alignItems: 'flex-end' }}>
        <Lofi.Text width={'60%'} />
        <Lofi.Text width={'80%'} />
        <Lofi.Text width={'100%'} />
      </View>
      <View style={{ paddingTop: 16, gap: 16 }}>
        <Lofi.Container height={32} rx={32} />
        <Lofi.Container height={32} rx={32} />
        <Lofi.Container height={32} rx={32} />
      </View>
    </Lofi.Details>
  );
};

export const BrandDetail = () => {
  const { navigateBack } = useNavigate();
  const scrollViewRef = useRef<ScrollView>(null);
  const activeSections = useAppSelector((state) => state.activeSections);

  const { params: detailParams } =
    useRoute<RouteProp<RootStackParamList, RouteEnum.BRAND_DETAIL>>();
  const {
    data: detail,
    isLoading: isLoadingDetail,
    error: detailError,
  } = useGetBrandsDetailsQuery({
    id: detailParams.id,
  });
  const [getBrandsDetails, { isLoading }] = useLazyGetBrandsDetailsQuery();

  let queryParams: ParamsOptions = useMemo(() => {
    return {
      category_ids: detail?.categories,
      brand_id: detail?.id,
    };
  }, [detail]);

  //SIMILARS
  const { data: similarDiscounts, isLoading: isLoadingSimilarDiscounts } =
    useGetSimilarCardsQuery(queryParams);
  const { data: similarCoupons, isLoading: isLoadingSimilarCoupons } =
    useGetSimilarCouponsQuery(queryParams);
  const { data: similarNewspapers, isLoading: isLoadingSimilarNewspapers } =
    useGetSimilarLeafletsQuery(queryParams);
  const { data: similarInspirations, isLoading: isLoadingSImilarInspirations } =
    useGetSimilarInspirationsQuery(queryParams);

  // ----EVENTS---- display card
  const { handleDisplayCard } = useDisplayCard({
    type: ObjectTypeEnum.Card,
  });

  useEffect(() => {
    if (detailParams.id === detail?.id) {
      handleDisplayCard({
        detail: detail,
        is_rec: detailParams.is_rec ? detailParams.is_rec : false,
      });
    }
    scrollViewRef.current?.scrollTo({ y: 0, animated: true });
  }, [detail]);

  useInteraction({
    object_type: ObjectTypeEnum.Brand,
    object_id: detailParams.id,
  });
  // --X--EVENTS--X--

  if (isLoadingDetail) return <BrandDetailLofi />;
  return (
    <Layout
      error={detailError}
      padding={0}
      onGoBack={() => navigateBack()}
      headerOptions={{
        label: 'profil marki',
        leftIcon: true,
        labelStyle: {
          fontSize: 16,
          fontWeight: '600',
          color: theme.colors.black['100'],
        },
      }}>
      <BrandContextProvider
        providerValues={{
          ...detail!,
          counters: {
            coupons: similarCoupons?.count ?? 0,
            discounts: similarDiscounts?.count ?? 0,
            inspirations: similarInspirations?.count ?? 0,
            newspapers: similarNewspapers?.count ?? 0,
          },
          similar: {
            coupons: similarCoupons?.results || [],
            discounts: similarDiscounts?.results || [],
            inspirations: similarInspirations?.results || [],
            newspapers: similarNewspapers?.results || [],
          },
        }}>
        <Styled.Wrapper
            ref={scrollViewRef}
          refreshControl={
            <RefreshControl
              refreshing={isLoading}
              onRefresh={() => getBrandsDetails({ id: detailParams.id })}
            />
          }>
          <Styled.Container>
            <BrandHeader />
            <Detail />
            <BrandDescription />
          </Styled.Container>
          {/*----SLIDERS----*/}
          <View>
            <Styled.SliderWrapper>
              {isLoadingSimilarDiscounts ? (
                <Loader />
              ) : similarDiscounts && similarDiscounts.count > 0 ? (
                activeSections.card && <BrandDiscountSlider />
              ) : null}
            </Styled.SliderWrapper>
            <Styled.SliderWrapper>
              {isLoadingSimilarCoupons ? (
                <Loader />
              ) : similarCoupons && similarCoupons.count > 0 ? (
                activeSections.coupon && <BrandCouponSlider />
              ) : null}
            </Styled.SliderWrapper>
            <Styled.SliderWrapper>
              {isLoadingSimilarNewspapers ? (
                <Loader />
              ) : similarNewspapers && similarNewspapers.count > 0 ? (
                activeSections.leaflet && <BrandNewspaperSlider />
              ) : null}
            </Styled.SliderWrapper>
            {isLoadingSImilarInspirations ? (
              <Loader />
            ) : similarInspirations && similarInspirations.count > 0 ? (
              activeSections.inspiration && <BrandInspirationMosaic />
            ) : null}
          </View>
        </Styled.Wrapper>
      </BrandContextProvider>
    </Layout>
  );
};
