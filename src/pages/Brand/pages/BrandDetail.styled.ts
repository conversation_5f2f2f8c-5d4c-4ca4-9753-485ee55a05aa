import styled from '@emotion/native';

import { ItemListPageWrapper } from '~/styles/common.styled';
import theme from '~/theme';

export const ListWrapper = styled.View`
  ${ItemListPageWrapper}
`;
export const SliderWrapper = styled.View`
  padding-top: 16px;
  background-color: ${theme.colors.white['200']};
`;
export const Wrapper = styled.ScrollView`
  flex-direction: column;
  gap: 15px;
`;

export const Container = styled.View`
  gap: 30px;
  padding: 16px;
`;
