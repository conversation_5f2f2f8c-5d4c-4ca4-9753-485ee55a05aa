import { useEffect } from 'react';
import { View } from 'react-native';
import Pdf from 'react-native-pdf';

import * as Styled from '../Newspaper.styled';

import { Layout } from '~/components/Layout/Layout';
import { Loader } from '~/components/Loader/Loader';
import * as Lofi from '~/components/Loader/Lofi';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useDisplayCard } from '~/events/hooks/useDisplayCard';
import { useInteraction } from '~/events/hooks/useInteraction';
import { useNavigate } from '~/hooks/useNavigate';
import { useNewspapers } from '~/hooks/useNewspapers';
import { NewspapersHeader } from '~/pages/Newspaper/components/NewspapersHeader';
import { NewspapersNavigation } from '~/pages/Newspaper/components/NewspapersNavigation';
import { NewspapersSwitcher } from '~/pages/Newspaper/components/NewspapersSwitcher';
import { NewspaperContextProvider } from '~/pages/Newspaper/context/newspaperContext';
import { useGetLeafletsDetailsQuery } from '~/redux/api/newspapers';
import theme from '~/theme';

const LeafletDetailLofi = () => {
  return (
    <Lofi.Details>
      <Lofi.Container height={60}>
        <View style={{ position: 'absolute', right: 32, bottom: -32 }}>
          <Lofi.Circle width={60} height={60} border />
        </View>
      </Lofi.Container>

      <View style={{ flex: 1, gap: 16, marginTop: 40, flexDirection: 'row', alignItems: 'center' }}>
        <Lofi.Circle height={30} width={30} />
        <View style={{ flex: 1, height: '100%', paddingTop: 16, gap: 16 }}>
          <Lofi.Container height={'100%'} rx={8} />
        </View>
        <Lofi.Circle height={30} width={30} />
      </View>
    </Lofi.Details>
  );
};
export const NewspaperDetail = () => {
  const { navigateBack } = useNavigate();
  const {
    detailParams,
    activePage,
    source,
    numberPages,
    setNumberPages,
    setActivePage,
    setSource,
    pdfRef,
  } = useNewspapers();

  const { handleDisplayCard } = useDisplayCard({
    type: ObjectTypeEnum.Leaflet,
  });

  const {
    data: detail,
    isLoading: isLoadingDetail,
    error: detailError,
  } = useGetLeafletsDetailsQuery({ id: detailParams.id });

  /**
   * EVENTS:
   */
  useEffect(() => {
    if (detailParams.id === detail?.id) {
      handleDisplayCard({
        detail: detail,
        is_rec: detailParams.is_rec ? detailParams.is_rec : false,
      });
    }
  }, [detail]);

  useInteraction({
    object_type: ObjectTypeEnum.Leaflet,
    object_id: detailParams.id,
  });

  useEffect(() => {
    if (detail?.file) {
      setSource({ uri: detail?.file!, cache: false });
    }
  }, [detail]);

  if (isLoadingDetail) return <LeafletDetailLofi />;
  return (
    <Layout
      error={detailError}
      onGoBack={() => navigateBack()}
      padding={0}
      footerShown={false}
      headerOptions={{
        label: 'Gazetki',
        leftIcon: true,
        labelStyle: {
          fontSize: 16,
          fontWeight: '600',
          color: theme.colors.black['100'],
        },
      }}>
      <NewspaperContextProvider
        providerValues={{
          ...detail!,
        }}>
        <Styled.Wrapper>
          <Styled.HeaderWrapper>
            <NewspapersHeader />
          </Styled.HeaderWrapper>
          <Styled.Content>
            <Styled.PdfWrapper>
              <Pdf
                ref={pdfRef}
                trustAllCerts={false}
                source={{ uri: source?.uri, cache: true }}
                enablePaging={true}
                onPageChanged={(page: number) => {
                  setActivePage(page);
                }}
                enableAntialiasing={false}
                renderActivityIndicator={() => <Loader />}
                onLoadComplete={(numberOfPages: number) => {
                  setNumberPages(numberOfPages);
                }}
                horizontal={true}
                enableAnnotationRendering={true}
                enableRTL={false}
                onError={() => {}}
                onPressLink={(uri) => {
                  console.log(`Link pressed: ${uri}`);
                }}
                style={{
                  width: '100%',
                  height: '100%',
                  overflow: 'hidden',
                  backgroundColor: theme.colors.white[200],
                }}
              />
              <NewspapersNavigation
                pdfRef={pdfRef}
                activePage={activePage}
                numberOfPages={numberPages}
              />
            </Styled.PdfWrapper>
            <NewspapersSwitcher
              pdfRef={pdfRef}
              activePage={activePage}
              numberOfPages={numberPages}
            />
          </Styled.Content>
        </Styled.Wrapper>
      </NewspaperContextProvider>
    </Layout>
  );
};
