import { useEffect, useRef, useState } from 'react';
import { TextInput } from 'react-native';

import { INewspapersSwitcher } from '~/pages/Newspaper/types';

export const usePageSwitcher = ({ activePage, pdfRef, numberOfPages }: INewspapersSwitcher) => {
  const input = useRef<TextInput>(null);
  const [focus, setFocus] = useState(false);
  const [value, setValue] = useState<string>(activePage.toString());

  const handlePageChange = () => {
    if (input.current) {
      input.current.focus();
    }
  };

  const handleFocusOut = () => {
    setFocus(false);
    const numberValue = parseInt(value, 10);

    if (!isNaN(numberValue)) {
      if (numberValue > numberOfPages) {
        pdfRef.current?.setPage(numberOfPages);
        setValue(numberOfPages.toString());
      } else {
        if (numberValue < 1) {
          pdfRef.current?.setPage(1);
          setValue('1');
        } else {
          pdfRef.current?.setPage(numberValue);
          setValue(numberValue.toString());
        }
      }
    } else {
      pdfRef.current?.setPage(1);
      setValue('1');
    }
  };

  useEffect(() => {
    if (!input?.current?.isFocused()) {
      setValue(activePage.toString());
    }
  }, [activePage]);

  return {
    input,
    focus,
    setFocus,
    handleFocusOut,
    handlePageChange,
    value,
    setValue,
  };
};
