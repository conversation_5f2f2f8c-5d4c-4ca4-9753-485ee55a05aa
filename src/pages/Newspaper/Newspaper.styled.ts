import styled from '@emotion/native';

import { LAYOUT_PADDING } from '~/helpers/layoutPadding';
import { ItemListPageWrapper } from '~/styles/common.styled';
import { flexColCenter } from '~/styles/flex.styled';
import theme from '~/theme';

export const ListWrapper = styled.View`
  ${ItemListPageWrapper}
`;

export const Wrapper = styled.View`
  ${flexColCenter};
  gap: 12px;
  width: 100%;
  background-color: ${theme.colors.white[200]};
  flex: 1;
`;

export const HeaderWrapper = styled.View`
  width: 100%;
  padding: ${`${LAYOUT_PADDING}px`};
`;

export const PdfWrapper = styled.View`
  position: relative;
  background-color: ${theme.colors.white[100]};
  flex: 1;
`;

export const Content = styled.View`
  gap: 16px;
  width: 100%;
  flex: 12;
`;
