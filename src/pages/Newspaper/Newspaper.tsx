import { useNetInfo } from '@react-native-community/netinfo';
import { useFocusEffect } from '@react-navigation/native';
import { useCallback, useContext, useEffect, useMemo } from 'react';
import { RefreshControl, TouchableOpacity, View } from 'react-native';

import { NewspaperCard, NewspaperCardLofi } from '~/components/Cards/NewspaperCard/NewspaperCard';
import { EmptyData } from '~/components/EmptyData/EmptyData';
import { FilterLayout } from '~/components/Filters/FilterLayout';
import { Icon } from '~/components/Icon/Icon';
import { Layout } from '~/components/Layout/Layout';
import { List } from '~/components/List/List';
import { ListItem } from '~/components/List/ListItem';
import { FilterContext } from '~/context/filterContext';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useDisplayContentType, useFilterCategoriesChange } from '~/events/hooks';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useListLayout } from '~/hooks/useListLayout';
import { useNavigate } from '~/hooks/useNavigate';
import { usePagination } from '~/hooks/usePagination';
import { useLazyGetCategoriesQuery } from '~/redux/api/categories';
import { useGetLeafletsQuery, useGetLeafletsWithPaginationQuery } from '~/redux/api/newspapers';
import { useAppSelector } from '~/redux/store';
import * as Styled from '~/styles/common.styled';
import theme from '~/theme';
import { assets } from '~/theme/assets';
import { ParamsOptions } from '~/types/shared';
import { Search } from '~/components/Search';
import { useResetSearchWithBack } from '~/hooks/useResetSearchWithBack';

const ListEmptyComponent = () => {
  return (
    <>
      <View style={{ height: 350, flexDirection: 'row' }}>
        <NewspaperCardLofi />
        <NewspaperCardLofi />
      </View>
      <View style={{ height: 350, flexDirection: 'row' }}>
        <NewspaperCardLofi />
        <NewspaperCardLofi />
      </View>
    </>
  );
};
export const Newspaper = () => {
  const { columnsSwitch, gap, screenWidth, setColumnsSwitch } = useListLayout();

  const { page, setPage, onEndReached } = usePagination();
  const _filterContext = useContext(FilterContext);

  const { isConnected } = useNetInfo();
  const { city } = useAppSelector((state) => state.user.detail);

  const { navigateToNewspaperDetails, navigateToNewspaperCollection } = useNavigate();

  const { handleBackWithReset } = useResetSearchWithBack();

  /**
   * Query Params:
   */
  let queryParams: ParamsOptions = useMemo(() => {
    const params = _filterContext.buildFilterParams();

    return {
      ...params,
      ...(page > 1 ? { page: page } : {}),
      city_id: city,
    };
  }, [
    _filterContext.selectedCategoryFilter,
    _filterContext.selectedShopFilter,
    _filterContext.selectedSortFilter,
    _filterContext.selectedSimilarCategoryFilter,
    _filterContext.searchId,
    _filterContext.searchString,
    city,
    page,
  ]);

  /**
   * RTK Query:
   */
  const { data, isFetching, error, refetch } = useGetLeafletsWithPaginationQuery(queryParams);
  const { data: mayInterestYouData } = useGetLeafletsQuery({ city_id: city });
  const [trigger] = useLazyGetCategoriesQuery();
  const { postFavorite } = useFavoritesQuery();

  /*
   * Fetch categories for specific object type (Card, Coupon etc.)
   */
  useEffect(() => {
    trigger({ content_type: ObjectTypeEnum.Leaflet }).then((res) => {
      _filterContext.setSelectedCategoryFilter(res.data);
    });
  }, []);

  /**
   * Reset page on focus (it triggers reset cache of rtk query and fetch new data from server)
   */
  useFocusEffect(
    useCallback(() => {
      setPage(1);
      return () => {
        setPage(1);
      };
    }, [
      _filterContext.selectedCategoryFilter,
      _filterContext.selectedShopFilter,
      _filterContext.selectedSortFilter,
      _filterContext.selectedSimilarCategoryFilter,
      _filterContext.searchId,
      _filterContext.searchString,
      city,
    ]),
  );

  useEffect(() => {
    refetch();
  }, [isConnected]);

  /**
   * Events:
   */
  useDisplayContentType({
    type: ObjectTypeEnum.Leaflet,
    collection: data,
  });
  useFilterCategoriesChange(_filterContext.buildFilterParams().category_ids || []);

  return (
    <Layout
      error={error}
      padding={0}
      onGoBack={handleBackWithReset}
      headerOptions={{
        resultCounter: data?.count,
        label: 'Gazetki',
        leftIcon: true,
        rightItem: <Search to={ObjectTypeEnum.Leaflet} />,
        labelStyle: {
          fontSize: 16,
          fontWeight: '600',
          color: theme.colors.black['100'],
        },
      }}>
      <Styled.ListWrapper>
        <Styled.FilterWrapper>
          <FilterLayout
            removeSingleFilter={(id, type) => {
              setPage(1);
              _filterContext.removeSingleFilter(id, type);
            }}
            removeAllFilters={() => {
              setPage(1);
              _filterContext.clear();
            }}
            tiles={_filterContext.tiles}
            screenType={ObjectTypeEnum.Leaflet}
            setColumnsSwitch={setColumnsSwitch}
            columnsSwitch={columnsSwitch}
          />
        </Styled.FilterWrapper>
        <Styled.Content>
          {isFetching || (data && data.results.length > 0) ? (
            <List
              contentContainerStyle={{ gap: columnsSwitch > 1 ? 10 : 0 }}
              loading={isFetching}
              data={data?.results || []}
              columnsSwitch={columnsSwitch}
              ListEmptyComponent={isFetching ? ListEmptyComponent : undefined}
              onEndReached={
                data?.results && !isFetching
                  ? () => {
                      onEndReached(isFetching, data?.next);
                    }
                  : () => {}
              }
              refreshControl={
                <RefreshControl
                  refreshing={isFetching}
                  onRefresh={() => {
                    setPage(1);
                  }}
                />
              }
              renderItem={({ item, index }) => {
                return (
                  <ListItem columnsSwitch={columnsSwitch} gap={gap} screenWidth={screenWidth}>
                    <NewspaperCard
                      data={item}
                      key={index}
                      size={columnsSwitch > 1 ? 'small' : 'large'}
                      onPress={() => navigateToNewspaperDetails(item.id, item.is_rec)}
                      onFavoritePress={(objectId, isFavorite, categories, tags) => {
                        postFavorite({
                          isFavorite,
                          params: {
                            payload: {
                              object_id: objectId,
                              object_type: ObjectTypeEnum.Leaflet,
                            },
                            type: 'Leaflet',
                          },
                          event: {
                            object_type: ObjectTypeEnum.Leaflet,
                            categories_id_list: categories || [],
                            tags_id_list: tags || [],
                          },
                        });
                      }}
                    />
                  </ListItem>
                );
              }}
            />
          ) : null}
          {/**EMPTY DATA:  */}
          {(!isFetching && !data) || data?.results.length === 0 ? (
            <EmptyData
              flex={8}
              data={mayInterestYouData?.results.slice(0, 10)}
              counter={mayInterestYouData?.count}
              onSectionHeaderPress={() => {
                _filterContext.clear();
                navigateToNewspaperCollection();
              }}
              renderItem={({ item, index }) => (
                <View style={{ width: 175, height: 350 }}>
                  <NewspaperCard
                    data={item}
                    key={index}
                    onPress={() => navigateToNewspaperDetails(item.id, item.is_rec)}
                    onFavoritePress={(objectId, isFavorite, categories, tags) =>
                      postFavorite({
                        isFavorite,
                        params: {
                          payload: {
                            object_id: objectId,
                            object_type: ObjectTypeEnum.Leaflet,
                          },
                          type: 'Leaflet',
                        },
                        event: {
                          object_type: ObjectTypeEnum.Leaflet,
                          categories_id_list: categories || [],
                          tags_id_list: tags || [],
                        },
                      })
                    }
                  />
                </View>
              )}
            />
          ) : null}
        </Styled.Content>
      </Styled.ListWrapper>
    </Layout>
  );
};
