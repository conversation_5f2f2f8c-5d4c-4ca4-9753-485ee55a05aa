import styled from '@emotion/native';

import { flexColCenter } from '~/styles/flex.styled';
import theme from '~/theme';
import { shadow } from '~/theme/shadows';

export const Wrapper = styled.View`
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  gap: 16px;
  position: relative;
  background-color: ${theme.colors.white[100]};
  border-radius: 8px;
  padding: 8px;
  height: 64px;
`;
export const Description = styled.View`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  width: 55%;
`;

export const IconWrapper = styled.View`
  height: 100%;
`;

export const Icon = styled.View`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 4px;
  border-radius: 100px;
  border: 1px solid ${theme.colors.gray[300]};
`;

export const Brand = styled.TouchableOpacity`
  width: 70px;
  height: 70px;
  border-radius: 70px;
  overflow: hidden;
  background-color: ${theme.colors.white[100]};
  margin: 0 5px 10px 5px;
  ${flexColCenter};
  ${shadow.gray[100]};
  position: absolute;
  right: 16px;
  top: 8px;
`;
