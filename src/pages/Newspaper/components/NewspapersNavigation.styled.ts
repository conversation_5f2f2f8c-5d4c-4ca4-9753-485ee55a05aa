import styled, { css } from '@emotion/native';

import { flexColCenter, flexRowBetween } from '~/styles/flex.styled';
import theme from '~/theme';
import { shadow } from '~/theme/shadows';

const Switch = css`
  background-color: ${theme.colors.white[100]};
  width: 32px;
  height: 32px;
  border-radius: 32px;
  ${flexColCenter};
  ${shadow.gray[100]};
`;

export const Wrapper = styled.View`
  ${flexRowBetween};
  padding: 0 16px;
  background-color: transparent;
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
`;

export const SwitchLeft = styled.TouchableOpacity`
  position: absolute;
  top: 48%;
  left: 10px;
  ${Switch};
`;

export const SwitchRight = styled.TouchableOpacity`
  position: absolute;
  top: 48%;
  right: 10px;
  ${Switch};
`;
