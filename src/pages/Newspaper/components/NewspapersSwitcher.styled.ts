import styled from '@emotion/native';

import { flexRowCenter } from '~/styles/flex.styled';
import theme from '~/theme';

export const Wrapper = styled.View`
  background-color: ${theme.colors.white[100]};
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 12px;
`;
export const Counter = styled.View`
  border-radius: 16px;
  border-width: 1px;
  border-color: ${theme.colors.gray[800]};
  overflow: hidden;
  width: 72px;
`;

export const CounterText = styled.TouchableOpacity`
  ${flexRowCenter};
`;

export const CounterInput = styled.TextInput`
  color: ${theme.colors.black[300]};
  padding: 0;
  text-align: center;
`;
