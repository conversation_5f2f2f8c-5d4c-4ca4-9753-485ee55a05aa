import * as Styled from './NewspapersNavigation.styled';

import { Icon } from '~/components/Icon/Icon';
import { INewspapersSwitcher } from '~/pages/Newspaper/types';
import theme from '~/theme';
import { assets } from '~/theme/assets';

export const NewspapersNavigation = ({
  pdfRef,
  numberOfPages,
  activePage,
}: INewspapersSwitcher) => {
  const handleNextPage = () => {
    if (activePage < numberOfPages) {
      pdfRef.current?.setPage(activePage + 1);
    }
  };

  const handlePreviousPage = () => {
    if (activePage > 1) {
      pdfRef.current?.setPage(activePage - 1);
    }
  };

  return (
    <>
      <Styled.SwitchLeft onPress={handlePreviousPage}>
        <Icon
          iconSVG={assets.icons.arrow_left_svg}
          width={24}
          height={24}
          fill={activePage > 1 ? theme.colors.purple[100] : theme.colors.gray[800]}
        />
      </Styled.SwitchLeft>
      <Styled.SwitchRight onPress={handleNextPage}>
        <Icon
          iconSVG={assets.icons.arrow_right_svg}
          width={24}
          height={24}
          fill={activePage < numberOfPages ? theme.colors.purple[100] : theme.colors.gray[800]}
        />
      </Styled.SwitchRight>
    </>
  );
};
