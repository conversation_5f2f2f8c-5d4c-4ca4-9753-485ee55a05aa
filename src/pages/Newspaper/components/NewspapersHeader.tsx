import { useContext } from 'react';

import * as Styled from './NewspaperHeader.styled';

import { CardFavourite } from '~/components/Cards/components/CardFavourite';
import { ImageComponent } from '~/components/ImageComponent/ImageComponent';
import { Typography } from '~/components/Typography/Typography';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { convertEndDate, convertStartDate } from '~/helpers/convertDate';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useNavigate } from '~/hooks/useNavigate';
import { newspaperContext } from '~/pages/Newspaper/context/newspaperContext';
import { RouteEnum } from '~/routes/routes';

export const NewspapersHeader = () => {
  const { navigation } = useNavigate();
  const context = useContext(newspaperContext);
  const { postFavorite } = useFavoritesQuery();

  const startDate = convertStartDate(context.valid_from);
  const endDate = context.valid_to ? ' - ' + convertEndDate(context.valid_to) : '- DO ODWOŁANIA';
  return (
    <Styled.Wrapper>
      <CardFavourite
        active={context.is_favorite}
        onPress={(isFavorite) => {
          postFavorite({
            isFavorite,
            params: {
              payload: {
                object_id: context.id,
                object_type: ObjectTypeEnum.Leaflet,
              },
              type: 'Leaflet',
            },
            event: {
              object_type: ObjectTypeEnum.Leaflet,
              categories_id_list: context.categories || [],
              tags_id_list: context.tags || [],
            },
          });
        }}
      />
      <Styled.Description>
        <Typography numberOfLines={1} fontSize={16} fontWeight="600" style={{ width: '100%' }}>
          {context.title}
        </Typography>
        <Typography fontSize={12} fontWeight="400">
          {`${startDate} ${endDate}`}
        </Typography>
      </Styled.Description>
      {context.brand && context.brand.logo ? (
        <Styled.Brand
          onPress={() =>
            navigation.navigate(RouteEnum.BRAND_DETAIL, {
              id: context.brand.id,
              is_rec: context.brand.is_rec,
            })
          }>
          <ImageComponent uri={context.brand.logo} width={44} height={44} borderRadius={4} />
        </Styled.Brand>
      ) : null}
    </Styled.Wrapper>
  );
};
