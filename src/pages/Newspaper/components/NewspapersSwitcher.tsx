import * as Styled from './NewspapersSwitcher.styled';

import { Typography } from '~/components/Typography/Typography';
import { usePageSwitcher } from '~/pages/Newspaper/hooks/usePageSwitcher';
import { INewspapersSwitcher } from '~/pages/Newspaper/types.d';
import theme from '~/theme';

export const NewspapersSwitcher = ({ activePage, pdfRef, numberOfPages }: INewspapersSwitcher) => {
  const { handlePageChange, handleFocusOut, focus, setFocus, input, value, setValue } =
    usePageSwitcher({
      activePage,
      pdfRef,
      numberOfPages,
    });

  return (
    <Styled.Wrapper>
      <Styled.Counter>
        {!focus ? (
          <Styled.CounterText onPress={handlePageChange}>
            <Typography
              fontSize={14}
              fontWeight="600"
              style={{ textAlign: 'center' }}
              color={theme.colors.black[300]}>
              {activePage.toString()}
            </Typography>
            <Typography
              fontSize={14}
              fontWeight="600"
              style={{ textAlign: 'center' }}
              color={theme.colors.gray[800]}>
              {` / ${numberOfPages}`}
            </Typography>
          </Styled.CounterText>
        ) : null}

        <Styled.CounterInput
          style={{ opacity: focus ? 1 : 0, height: focus ? 24 : 1 }}
          ref={input}
          keyboardType={'numeric'}
          defaultValue={value}
          onChangeText={(text) => setValue(text)}
          onFocus={() => setFocus(true)}
          onBlur={() => handleFocusOut()}
        />
      </Styled.Counter>
    </Styled.Wrapper>
  );
};
