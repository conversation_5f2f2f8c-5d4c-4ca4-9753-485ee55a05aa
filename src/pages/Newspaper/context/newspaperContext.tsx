import { createContext, ReactNode } from 'react';

import { INewspaperDetail } from '~/types/newspaper';

export interface INewspaperContextProvider {
  providerValues: INewspaperDetail;
  children: ReactNode;
}

const defaultContext: INewspaperDetail = {
  id: 0,
  title: '',
  is_favorite: false,
  brand: {
    id: 0,
    name: '',
    logo: '',
    is_favorite: false,
  },
  image: '',
  valid_from: '',
  valid_to: '',
  categories: [],
  file: '',
};

export const newspaperContext = createContext<INewspaperDetail>(defaultContext);

export const NewspaperContextProvider = ({
  providerValues,
  children,
}: INewspaperContextProvider) => {
  return <newspaperContext.Provider value={providerValues}>{children}</newspaperContext.Provider>;
};
