import Markdown, { MarkdownIt } from '@jonasmerlin/react-native-markdown-display';
import React from 'react';

import * as Styled from './Policy.styled';

import { Layout } from '~/components/Layout/Layout';
import { Loader } from '~/components/Loader/Loader';
import { LanguageEnum } from '~/enums/legalEnum';
import { useNavigate } from '~/hooks/useNavigate';
import { usePolicy } from '~/hooks/usePolicy';
import { markdownStyles } from '~/styles/markdown.styled';
import theme from '~/theme';
import { ILegalContentVersion } from '~/types/privacy';

export const Policy = () => {
  const { navigateBack } = useNavigate();
  const { legal, params } = usePolicy();

  return (
    <Layout
      onGoBack={() => navigateBack()}
      footerShown={false}
      headerOptions={{
        label: params.type === 'terms' ? 'Regulamin' : 'Polityka prywatności',
        leftIcon: true,
        labelStyle: {
          fontSize: 16,
          fontWeight: '600',
          color: theme.colors.black['100'],
        },
      }}>
      {legal !== null ? (
        <Styled.Wrapper>
          <Markdown
            style={markdownStyles}
            markdownit={MarkdownIt({ typographer: true }).disable(['image'])}>
            {
              legal?.versions?.find(
                (item: ILegalContentVersion) => item.language === LanguageEnum.Pl,
              )?.content
            }
          </Markdown>
        </Styled.Wrapper>
      ) : (
        <Loader />
      )}
    </Layout>
  );
};
