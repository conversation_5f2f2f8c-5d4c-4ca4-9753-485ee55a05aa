import { toNumber } from 'lodash';
import { useEffect, useRef, useState } from 'react';
import { FlatList, Image, NativeScrollEvent, NativeSyntheticEvent } from 'react-native';

import * as Styled from './Onboarding.styled';

import { Layout } from '~/components/Layout/Layout';
import { Loader } from '~/components/Loader/Loader';
import { Typography } from '~/components/Typography/Typography';
import { useNavigate } from '~/hooks/useNavigate';
import { IOnboardingStep } from '~/pages/Onboarding/types';
import { getOnboarding } from '~/redux/reducers/info/onboarding/thunks';
import { userOnboardingChange } from '~/redux/reducers/user/actions';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import theme from '~/theme';
import { IOnboarding } from '~/types/onboarding';
import { Control, Stepper } from '~/components/Onboarding';

export const Onboarding = () => {
  const flatListRef = useRef<FlatList>(null);
  const dispatch = useAppDispatch();
  const { navigation } = useNavigate();
  const { loading } = useAppSelector((state) => state.onboarding);
  const [step, setStep] = useState(0);
  const [data, setData] = useState<Array<IOnboardingStep>>();

  const handleConvert = (response: IOnboarding) => {
    const obj = Object.keys(response).reduce((acc: any, key: string) => {
      const [id, property] = key.split('_screen_');
      if (!acc[toNumber(id) - 1]) {
        acc[toNumber(id) - 1] = { id: Number(id) };
      }
      acc[toNumber(id) - 1][property] = response[key as keyof IOnboarding];
      return acc;
    }, []);
    return obj.filter(
      (object: IOnboardingStep) =>
        object.image !== null && object.text.length > 0 && object.title.length > 0,
    );
  };

  useEffect(() => {
    dispatch(getOnboarding()).then((res) => {
      setData(handleConvert(res));
    });
  }, []);

  const handleNextStep = () => {
    if (data && step < data.length - 1) {
      setStep(step + 1);
      flatListRef.current?.scrollToIndex({
        animated: true,
        index: step + 1,
      });
    } else {
      dispatch(userOnboardingChange(false));
      navigation.navigate(RouteEnum.HOME);
    }
  };

  const handleCurrentVisibleIndex = (e: NativeSyntheticEvent<NativeScrollEvent>) => {
    const index = Math.round(e.nativeEvent.contentOffset.x / 300);
    setStep(index);
  };

  return (
    <Layout footerShown={false} headerShown={false}>
      <Styled.Wrapper>
        {loading ? (
          <Loader />
        ) : (
          <>
            {data ? (
              <Styled.ListWrapper>
                <FlatList
                  ref={flatListRef}
                  data={data}
                  pagingEnabled={true}
                  horizontal={true}
                  snapToAlignment={'center'}
                  showsHorizontalScrollIndicator={false}
                  onMomentumScrollEnd={handleCurrentVisibleIndex}
                  keyExtractor={(item, index) => index.toString()}
                  renderItem={({ item, index }) => (
                    <Styled.Item key={index}>
                      <Image source={{ uri: item.image }} style={{ width: 320, height: 320 }} />
                      <Styled.Desc>
                        <Typography
                          fontSize={20}
                          fontWeight="600"
                          style={{ textAlign: 'center', padding: 0 }}
                          color={theme.colors.purple['100']}>
                          {item.title}
                        </Typography>
                        <Typography
                          fontSize={14}
                          fontWeight="300"
                          color={theme.colors.black[300]}
                          style={{ textAlign: 'center', padding: 0 }}>
                          {item.text}
                        </Typography>
                      </Styled.Desc>
                    </Styled.Item>
                  )}
                />
                <Stepper active={step} data={data} />
              </Styled.ListWrapper>
            ) : null}
          </>
        )}
        <Control step={step} onPress={handleNextStep} length={data?.length} />
      </Styled.Wrapper>
    </Layout>
  );
};
