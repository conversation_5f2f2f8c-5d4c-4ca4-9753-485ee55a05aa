import styled from '@emotion/native';

import { flexColBetween, flexColCenter } from '~/styles/flex.styled';

export const Wrapper = styled.View`
  ${flexColBetween};
  height: 100%;
`;

export const Desc = styled.View`
  ${flexColCenter};
  gap: 8px;
`;

export const ListWrapper = styled.View`
  ${flexColBetween};
  flex: 1;
  width: 320px;
`;

export const Item = styled.View`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  display: flex;
  flex: 20;
  gap: 16px;
  width: 320px;
`;
