import { useFocusEffect } from '@react-navigation/native';
import { FC, ReactElement, useCallback, useContext, useRef } from 'react';
import { RefreshControl, ScrollView, Text, View } from 'react-native';

import * as Styled from './Home.styled';

import { Icon } from '~/components/Icon/Icon';
import { Layout } from '~/components/Layout/Layout';
import { FilterContext } from '~/context/filterContext';
import { HomePageScrollContextProvider } from '~/context/homePageScrollContext';
import { useHome } from '~/hooks/useHome';
import { useNavigate } from '~/hooks/useNavigate';
import { useNotifications } from '~/hooks/useNotifications';
import { usePopularSearch } from '~/hooks/usePopularSearch';
import { usePreventBack } from '~/hooks/usePreventBack';
import { usePushNotifications } from '~/hooks/usePushNotifications';
import { Articles } from '~/pages/Home/components/Articles';
import { Banners } from '~/pages/Home/components/Banners';
import { Brands } from '~/pages/Home/components/Brands';
import { Coupons } from '~/pages/Home/components/Coupons';
import { Discounts } from '~/pages/Home/components/Discounts';
import { Inspirations } from '~/pages/Home/components/Inspirations';
import { Newspapers } from '~/pages/Home/components/Newspapers';
import { useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import theme from '~/theme';
import { assets } from '~/theme/assets';
import { getFontSize } from '~/theme/size';

export const Home: FC = (): ReactElement => {
  const { navigation } = useNavigate();
  const { refreshing, detail, handleRefresh } = useHome();
  const activeSections = useAppSelector((state) => state.activeSections);

  const ref = useRef<ScrollView>(null);
  useNotifications();
  usePopularSearch();
  usePreventBack();
  usePushNotifications();

  const _filterContext = useContext(FilterContext);

  /**
   * Clear filter context on focus
   */
  useFocusEffect(
    useCallback(() => {
      _filterContext.clear();
    }, []),
  );

  return (
    <HomePageScrollContextProvider providerValue={{ ref: ref }}>
      <Layout
        padding={0}
        isLoading={false}
        homeScreen={true}
        refreshControl={
          <RefreshControl
            progressViewOffset={50}
            refreshing={refreshing}
            onRefresh={handleRefresh}
          />
        }
        headerOptions={{
          homeScreen: true,
          label: 'homeprofit',
          labelStyle: {
            fontSize: 16,
            fontWeight: '600',
            color: theme.colors.black['100'],
          },
          rightItem: (
            <Styled.Header onPress={() => navigation.navigate(RouteEnum.LOCALIZATION)}>
              <Text
                style={{
                  fontSize: getFontSize(14),
                  fontWeight: '400',
                  color: theme.colors.gray[800],
                }}>{`${detail?.city ? detail.city_name : 'Cała Polska'}`}</Text>
              <Icon iconSVG={assets.icons.map_pin_svg} width={22} height={22} />
            </Styled.Header>
          ),
        }}>
        <View>
          <Banners />
          <Styled.Section>
            {activeSections.card && <Discounts />}
            {activeSections.brand && <Brands />}
            {activeSections.coupon && <Coupons />}
            {activeSections.leaflet && <Newspapers />}
          </Styled.Section>
          {activeSections.article && <Articles />}
          {activeSections.inspiration && <Inspirations />}
        </View>
      </Layout>
    </HomePageScrollContextProvider>
  );
};
