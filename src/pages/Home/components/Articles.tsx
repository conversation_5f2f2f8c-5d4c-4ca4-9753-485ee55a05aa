import { memo } from 'react';
import { View } from 'react-native';

import { ArticleCard, ArticleCardLofi } from '~/components/Cards/ArticleCard/ArticleCard';
import { Section } from '~/components/Section/Section';
import { ContentSlider } from '~/components/Slider/Slider';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useNavigate } from '~/hooks/useNavigate';
import { useGetArticlesQuery } from '~/redux/api/articles';
import { useAppSelector } from '~/redux/store';
import theme from '~/theme';

const ListEmptyComponent = () => {
  return (
    <View style={{ height: 240, flexDirection: 'row' }}>
      <ArticleCardLofi width={235} />
      <ArticleCardLofi width={235} />
      <ArticleCardLofi width={235} />
      <ArticleCardLofi width={235} />
      <ArticleCardLofi width={235} />
    </View>
  );
};

const MemoArticle = memo(ArticleCard);
export const Articles = () => {
  const { navigateToArticleCollection, navigateToArticleDetails } = useNavigate();
  const { city } = useAppSelector((state) => state.user.detail);
  const { token } = useAppSelector((state) => state.user);
  const { data } = useGetArticlesQuery({ city_id: city, token: token?.access });
  const { postFavorite } = useFavoritesQuery();

  return (
    <Section
      style={{ paddingTop: 20 }}
      bgColor={theme.colors.white['100']}
      counter="więcej"
      title={'Artykuły'}
      onSectionHeaderPress={() => navigateToArticleCollection()}>
      <ContentSlider
        ListEmptyComponent={ListEmptyComponent}
        containerStyle={{ gap: 20, paddingBottom: 10 }}
        data={data?.results.slice(0, 10)}
        renderItem={({ item, index }) => (
          <View style={{ width: 235, height: 240 }}>
            <MemoArticle
              onFavoritePress={(objectId, isFavorite, categories, tags) => {
                postFavorite({
                  isFavorite,
                  params: {
                    payload: {
                      object_id: objectId,
                      object_type: ObjectTypeEnum.Article,
                    },
                    type: 'Article',
                  },
                  event: {
                    object_type: ObjectTypeEnum.Article,
                    categories_id_list: categories || [],
                    tags_id_list: tags || [],
                  },
                });
              }}
              data={item}
              key={index}
              onPress={() => navigateToArticleDetails(item.id, item.is_rec)}
            />
          </View>
        )}
      />
    </Section>
  );
};
