import { memo } from 'react';
import { View } from 'react-native';

import { NewspaperCard, NewspaperCardLofi } from '~/components/Cards/NewspaperCard/NewspaperCard';
import { MoreNewspaper } from '~/components/MoreTile/MoreNewspaper';
import { Section } from '~/components/Section/Section';
import { ContentSlider } from '~/components/Slider/Slider';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useNavigate } from '~/hooks/useNavigate';
import { useGetLeafletsQuery } from '~/redux/api/newspapers';
import { useAppSelector } from '~/redux/store';

const ListEmptyComponent = () => {
  return (
    <View style={{ height: 350, gap: 0, flexDirection: 'row' }}>
      <NewspaperCardLofi />
      <NewspaperCardLofi />
      <NewspaperCardLofi />
      <NewspaperCardLofi />
      <NewspaperCardLofi />
    </View>
  );
};

const MemoNewspapers = memo(NewspaperCard);

export const Newspapers = () => {
  const { navigateToNewspaperDetails, navigateToNewspaperCollection } = useNavigate();
  const { postFavorite } = useFavoritesQuery();
  const { city } = useAppSelector((state) => state.user.detail);
  const { token } = useAppSelector((state) => state.user);
  const { data } = useGetLeafletsQuery({ city_id: city, token: token?.access });

  return (
    <Section
      style={{ paddingTop: 15 }}
      showBottomLine={false}
      title="Gazetki"
      counter="więcej"
      onSectionHeaderPress={() => navigateToNewspaperCollection()}>
      <ContentSlider
        containerStyle={{ gap: 15 }}
        data={data?.results.slice(0, 10)}
        ListEmptyComponent={ListEmptyComponent}
        renderItem={({ item, index }) => (
          <View style={{ width: 170, height: 350 }}>
            <MemoNewspapers
              data={item}
              key={index}
              onPress={() => navigateToNewspaperDetails(item.id, item.is_rec)}
              onFavoritePress={(objectId, isFavorite, categories, tags) => {
                postFavorite({
                  isFavorite,
                  params: {
                    payload: {
                      object_id: objectId,
                      object_type: ObjectTypeEnum.Leaflet,
                    },
                    type: 'Leaflet',
                  },
                  event: {
                    object_type: ObjectTypeEnum.Leaflet,
                    categories_id_list: categories || [],
                    tags_id_list: tags || [],
                  },
                });
              }}
            />
          </View>
        )}
        ListFooterComponent={() => {
          if (!data) return <></>;
          else return <MoreNewspaper onPress={() => navigateToNewspaperCollection()} />;
        }}
      />
    </Section>
  );
};
