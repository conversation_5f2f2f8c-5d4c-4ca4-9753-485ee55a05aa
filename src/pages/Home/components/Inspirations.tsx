import { Inspiration } from '~/components/Inspiration/Inspiration';
import { Section } from '~/components/Section/Section';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { LAYOUT_PADDING } from '~/helpers/layoutPadding';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useNavigate } from '~/hooks/useNavigate';
import * as Styled from '~/pages/Home/components/Brands.styled';
import { useGetInspirationsQuery } from '~/redux/api/inspirations';
import { useAppSelector } from '~/redux/store';
import theme from '~/theme';

export const Inspirations = () => {
  const { city } = useAppSelector((state) => state.user.detail);
  const { token } = useAppSelector((state) => state.user);
  const { navigateToInspirationDetails, navigateToInspirationCollection } = useNavigate();

  const { postFavorite } = useFavoritesQuery();
  const { data } = useGetInspirationsQuery({ city_id: city, token: token?.access });
  return (
    <Section
      style={{ paddingTop: 16, paddingBottom: 16 }}
      bgColor={theme.colors.white['100']}
      showBottomLine={false}
      title="Inspiracje"
      counter="więcej"
      onSectionHeaderPress={() => navigateToInspirationCollection()}>
      <Styled.Wrapper style={{ paddingHorizontal: LAYOUT_PADDING, paddingTop: 15 }}>
        <Inspiration
          onFavoritePress={(objectId, isFavorite, categories, tags) =>
            postFavorite({
              isFavorite,
              params: {
                payload: {
                  object_id: objectId,
                  object_type: ObjectTypeEnum.Inspiration,
                },
                type: 'Inspiration',
              },
              event: {
                object_type: ObjectTypeEnum.Inspiration,
                categories_id_list: categories || [],
                tags_id_list: tags || [],
              },
            })
          }
          data={data?.results}
          onPress={(id, is_rec) => {
            navigateToInspirationDetails(id, is_rec);
          }}
        />
      </Styled.Wrapper>
    </Section>
  );
};
