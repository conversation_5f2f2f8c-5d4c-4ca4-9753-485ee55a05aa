import styled from '@emotion/native';

import { flexColCenter } from '~/styles/flex.styled';
import { shadow } from '~/theme/shadows';

export const Wrapper = styled.View`
  flex: 1;
`;
export const ImageWrapper = styled.TouchableOpacity`
  width: 100px;
  height: 100px;
  border-radius: 100px;
  margin: 0 5px 10px 5px;
  ${flexColCenter};
  ${shadow.gray[100]};
`;

export const Image = styled.Image`
  border-radius: 100px;
  width: 100px;
  height: 100px;
`;
