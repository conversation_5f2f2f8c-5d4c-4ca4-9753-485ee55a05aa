import { useFocusEffect } from '@react-navigation/native';
import { memo, useCallback, useEffect } from 'react';
import { View } from 'react-native';

import { DiscountCard, DiscountCardLofi } from '~/components/Cards/DiscountCard/DiscountCard';
import { MoreDiscount } from '~/components/MoreTile/MoreDiscount';
import { Section } from '~/components/Section/Section';
import { ContentSlider } from '~/components/Slider/Slider';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useNavigate } from '~/hooks/useNavigate';
import { useGetCardsQuery } from '~/redux/api/discounts';
import { useAppSelector } from '~/redux/store';

const ListEmptyComponent = () => {
  return (
    <View style={{ height: 230, flexDirection: 'row' }}>
      <DiscountCardLofi />
      <DiscountCardLofi />
      <DiscountCardLofi />
      <DiscountCardLofi />
      <DiscountCardLofi />
    </View>
  );
};

const MemoDiscount = memo(DiscountCard);
export const Discounts = () => {
  const { city } = useAppSelector((state) => state.user.detail);
  const { token } = useAppSelector((state) => state.user);
  const { data } = useGetCardsQuery({ city_id: city, token: token?.access });
  const { navigateToDiscountCollection, navigateToDiscountDetails } = useNavigate();
  const { postFavorite } = useFavoritesQuery();

  return (
    <Section
      title={'Rabaty\nHomeProfit'}
      counter="więcej"
      onSectionHeaderPress={() => navigateToDiscountCollection()}>
      <ContentSlider
        data={data?.results.slice(0, 10)}
        ListEmptyComponent={ListEmptyComponent}
        renderItem={({ item, index }) => (
          <View style={{ width: 170 }}>
            <MemoDiscount
              onFavoritePress={(objectId, isFavorite, categories, tags) => {
                postFavorite({
                  isFavorite,
                  params: {
                    payload: {
                      object_id: objectId,
                      object_type: ObjectTypeEnum.Card,
                    },
                    type: 'Card',
                  },
                  event: {
                    object_type: ObjectTypeEnum.Card,
                    categories_id_list: categories || [],
                    tags_id_list: tags || [],
                  },
                });
              }}
              key={index}
              data={item}
              onPress={() => navigateToDiscountDetails(item.id, item.is_rec)}
            />
          </View>
        )}
        ListFooterComponent={() => {
          if (!data) return <></>;
          else return <MoreDiscount onPress={() => navigateToDiscountCollection()} />;
        }}
      />
    </Section>
  );
};
