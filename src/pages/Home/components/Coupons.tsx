import { memo } from 'react';
import { View } from 'react-native';

import { CouponCard, CouponCardLofi } from '~/components/Cards/CouponCard/CouponCard';
import { MoreCoupon } from '~/components/MoreTile/MoreCoupon';
import { Section } from '~/components/Section/Section';
import { ContentSlider } from '~/components/Slider/Slider';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useCoupons } from '~/hooks/useCoupons';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useGetCouponsQuery } from '~/redux/api/coupons';
import { useAppSelector } from '~/redux/store';

const ListEmptyComponent = () => {
  return (
    <View style={{ height: 230, flexDirection: 'row' }}>
      <CouponCardLofi />
      <CouponCardLofi />
      <CouponCardLofi />
      <CouponCardLofi />
      <CouponCardLofi />
    </View>
  );
};

const MemoCoupons = memo(CouponCard);

export const Coupons = () => {
  const { navigateToCouponCollection, navigateToCouponDetails } = useCoupons();
  const { city } = useAppSelector((state) => state.user.detail);
  const { token } = useAppSelector((state) => state.user);
  const { data } = useGetCouponsQuery({ city_id: city, token: token?.access });
  const { postFavorite } = useFavoritesQuery();

  return (
    <Section
      style={{ paddingTop: 15 }}
      title="Okazje"
      counter="więcej"
      onSectionHeaderPress={() => navigateToCouponCollection()}>
      <ContentSlider
        data={data?.results.slice(0, 10)}
        ListEmptyComponent={ListEmptyComponent}
        renderItem={({ item, index }) => (
          <View style={{ width: 170, height: 250 }}>
            <MemoCoupons
              onFavoritePress={(objectId, isFavorite, categories, tags) => {
                postFavorite({
                  isFavorite,
                  params: {
                    payload: {
                      object_id: objectId,
                      object_type: ObjectTypeEnum.Coupon,
                    },
                    type: 'Coupon',
                  },
                  event: {
                    object_type: ObjectTypeEnum.Coupon,
                    categories_id_list: categories || [],
                    tags_id_list: tags || [],
                  },
                });
              }}
              key={index}
              data={item}
              onPress={() => navigateToCouponDetails(item.id, item.is_rec)}
            />
          </View>
        )}
        ListFooterComponent={() => {
          if (!data) return <></>;
          else return <MoreCoupon onPress={() => navigateToCouponCollection()} />;
        }}
      />
    </Section>
  );
};
