import { View } from 'react-native';

import { InfiniteSlider } from '~/components/InfiniteSlider/InfiniteSlider';
import { useGetBannersQuery } from '~/redux/api/banners';
import theme from '~/theme';

export const Banners = () => {
  const { data } = useGetBannersQuery();
  if (!data)
    return (
      <View style={{ height: 230, width: '100%', backgroundColor: theme.colors.gray['300'] }} />
    );
  return <InfiniteSlider data={data} />;
};
