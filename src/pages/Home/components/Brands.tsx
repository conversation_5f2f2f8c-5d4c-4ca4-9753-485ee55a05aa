import { View } from 'react-native';

import * as Styled from './Brands.styled';

import { BrandCardLofi } from '~/components/Cards/BrandCard/BrandCard';
import { ImageComponent } from '~/components/ImageComponent/ImageComponent';
import { MoreBrand } from '~/components/MoreTile/MoreBrand';
import { Section } from '~/components/Section/Section';
import { ContentSlider } from '~/components/Slider/Slider';
// import { useBrands } from '~/hooks/useBrands';
import { useNavigate } from '~/hooks/useNavigate';
import { useGetBrandsQuery } from '~/redux/api/brands';
import { useAppSelector } from '~/redux/store';

const ListEmptyComponent = () => {
  return (
    <View style={{ flexDirection: 'row' }}>
      <BrandCardLofi />
      <BrandCardLofi />
      <BrandCardLofi />
      <BrandCardLofi />
      <BrandCardLofi />
    </View>
  );
};
export const Brands = () => {
  const { navigateToBrandCollection, navigateToBrandDetails } = useNavigate();
  const { city } = useAppSelector((state) => state.user.detail);
  const { token } = useAppSelector((state) => state.user);
  const { data } = useGetBrandsQuery({ city_id: city, token: token?.access });

  return (
    <Section
      style={{ paddingTop: 15 }}
      title="Strefa Marek"
      counter="więcej"
      onSectionHeaderPress={() => navigateToBrandCollection()}>
      <ContentSlider
        ListEmptyComponent={ListEmptyComponent}
        data={data?.results.slice(0, 10)}
        renderItem={({ item, index }) => (
          <Styled.ImageWrapper
            key={index}
            onPress={() => navigateToBrandDetails(item.id, item.is_rec)}>
            <ImageComponent
              width={76}
              height={76}
              bgColor={item.bg_color}
              borderRadius={16}
              uri={item.logo}
              resizeMode={'contain'}
              style={{ aspectRatio: 1 }}
            />
          </Styled.ImageWrapper>
        )}
        ListFooterComponent={() => {
          if (!data) return <></>;
          return <MoreBrand onPress={() => navigateToBrandCollection()} />;
        }}
      />
    </Section>
  );
};
