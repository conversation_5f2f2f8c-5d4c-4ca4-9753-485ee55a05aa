import { View } from 'react-native';

import { CornerBox } from '~/components/CornerBox/CornerBox';
import { Partner } from '~/pages/Profile/components/Partner';
import { Profile } from '~/pages/Profile/components/Profile';
import { IHeader } from '~/pages/Profile/types.d';

export const Header = ({ profile, partner }: IHeader) => {
  return (
    <View>
      <CornerBox>
        <Profile {...profile} />
      </CornerBox>
      <Partner premium={partner} />
    </View>
  );
};
