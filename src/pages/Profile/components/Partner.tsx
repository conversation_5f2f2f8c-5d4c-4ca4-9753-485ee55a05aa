import React from 'react';

import * as Styled from './Partner.styled';

import { BrandIcon } from '~/components/BrandIcon/BrandIcon';
import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { Icon } from '~/components/Icon/Icon';
import { Typography } from '~/components/Typography/Typography';
import { useNavigate } from '~/hooks/useNavigate';
import { RouteEnum } from '~/routes/routes';
import { assets } from '~/theme/assets';
import { IPremium } from '~/types/user';

export interface IPartner {
  premium: IPremium | null;
}

export const Partner = ({ premium }: IPartner) => {
  const { navigation } = useNavigate();

  if (premium)
    return (
      <Styled.Wrapper>
        <Typography fontSize={14} fontWeight="500">
          {`Twój Partner: `}
        </Typography>
        {premium.partner_logo && premium.partner_logo.length > 0 ? (
          <BrandIcon
            size={'large'}
            src={premium.partner_logo}
            isActive={true}
            resizeMode={'contain'}
          />
        ) : (
          <Styled.Image>
            <Styled.ImageContainer>
              <Icon iconSVG={assets.icons.hp_logo_svg} />
            </Styled.ImageContainer>
          </Styled.Image>
        )}
      </Styled.Wrapper>
    );
  else
    return (
      <Styled.WrapperButton>
        <Button
          onPress={() => navigation.navigate(RouteEnum.PREMIUM_MODAL)}
          variant={ButtonVariant.FIFTH}
          size={ButtonSize.MEDIUM}
          typographyStyles={{ fontWeight: '600', fontSize: 12, color: '#ffffff' }}
          borderRadius={32}>
          {'Aktywuj\npremium'.toUpperCase()}
        </Button>
      </Styled.WrapperButton>
    );
};
