import styled from '@emotion/native';

import { flexColCenter } from '~/styles/flex.styled';
import { shadow } from '~/theme/shadows';

export const Wrapper = styled.View`
  position: absolute;
  right: 24px;
  top: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
  flex: 1;
`;

export const WrapperButton = styled.View`
  position: absolute;
  right: 16px;
  height: 100%;
  width: 130px;
  ${flexColCenter};
`;

export const Image = styled.View`
  ${flexColCenter};
  width: 120px;
  height: 120px;
  ${shadow.gray['100']}
  border-radius: 100px;
  overflow: hidden;
`;

export const ImageContainer = styled.View`
  ${flexColCenter};
  width: 86px;
  height: 86px;
`;
