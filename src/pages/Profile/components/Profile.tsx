import * as Styled from './Profile.styled';

import { Typography } from '~/components/Typography/Typography';
import { useNavigate } from '~/hooks/useNavigate';
import { IProfile } from '~/pages/Profile/types.d';
import { RouteEnum } from '~/routes/routes';

export const Profile = ({ name, status, validTo }: IProfile) => {
  const { navigation } = useNavigate();

  return (
    <Styled.Wrapper>
      <Typography fontSize={22} fontWeight="600">
        {`Witaj,\n${name}`}
      </Typography>
      <Styled.WrapperStatus hitSlop={32} onPress={() => navigation.navigate(RouteEnum.SETTINGS)}>
        <Typography fontSize={12} fontWeight="400" style={{ textDecorationLine: 'underline' }}>
          {`Twój dostęp: `}
        </Typography>
        <Typography fontSize={12} fontWeight="600" style={{ textDecorationLine: 'underline' }}>
          {`${status}`.toUpperCase()}
        </Typography>
      </Styled.WrapperStatus>
      {validTo ? (
        <Typography fontSize={12} fontWeight="400">
          {`Ważny do: ${validTo.split('-').reverse().join('.')}`}
        </Typography>
      ) : null}
    </Styled.Wrapper>
  );
};
