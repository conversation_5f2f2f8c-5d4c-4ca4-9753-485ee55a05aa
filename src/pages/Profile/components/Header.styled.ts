import styled from '@emotion/native';

import theme from '~/theme';

export const Wrapper = styled.View`
  overflow: hidden;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  background-color: ${theme.colors.purple[200]};
  position: relative;
`;

export const Corner = styled.View`
  position: absolute;
  z-index: 0;
  top: -25px;
  left: -25px;
  width: 50px;
  height: 50px;
  background-color: ${theme.colors.white[100]};
  transform: rotate(45deg);
`;
