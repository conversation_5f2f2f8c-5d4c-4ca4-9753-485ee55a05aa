import { BookmarkEnum } from '~/enums/bookmarkEnum';
import { IFavoritesCollection } from '~/redux/reducers/content/favorites/types';

export interface IHandleIfEmpty {
  key: keyof IFavoritesCollection;
  collection: IFavoritesCollection;
  active: null | 'mie' | 'ins' | 'urz';
}

export const handleIfEmpty = ({ key, collection, active }: IHandleIfEmpty) => {
  let empty = true;
  if (collection[key] !== undefined) {
    if (collection[key].results !== undefined && collection[key].results !== null) {
      if (collection[key].results.length > 0) {
        collection[key].results.map((item) => {
          if (Object.keys(item.object).length > 0) {
            if (item.bookmark === active || active === BookmarkEnum.All) {
              empty = false;
            }
          }
        });
      }
    }
  }
  return empty;
};
