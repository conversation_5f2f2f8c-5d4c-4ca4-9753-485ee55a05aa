import { useContext, useEffect, useState } from 'react';

import { favoritesContext } from '~/pages/Profile/components/favorites/context/favoritesContext';
import { useGetFavoritesQuery } from '~/redux/api/favorites';
import { getFavoritesAll } from '~/redux/reducers/content/favorites/thunks';
import { IFavoritesCollection } from '~/redux/reducers/content/favorites/types';
import { useAppDispatch, useAppSelector } from '~/redux/store';

export const useSectionList = () => {
  const dispatch = useAppDispatch();
  const context = useContext(favoritesContext);
  const { collection, loading, download } = useAppSelector((state) => state.favorites);
  const [empty, setEmpty] = useState<boolean>(false);

  const { data } = useGetFavoritesQuery({});

  const handleEmptyCollection = () => {
    for (const key in collection) {
      const results = collection[key as keyof IFavoritesCollection].results;
      if (results.length > 0) {
        const hasMatchingBookmark = results.some((item: any) => item.bookmark === context.active);
        if (hasMatchingBookmark) {
          return false;
        }
      }
    }
    return true;
  };

  useEffect(() => {
    if (!download) {
      dispatch(getFavoritesAll());
    }
  }, []);

  useEffect(() => {
    setEmpty(handleEmptyCollection());
  }, [collection, context.active]);

  return {
    empty,
    loading,
  };
};
