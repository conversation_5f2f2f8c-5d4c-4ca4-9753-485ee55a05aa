import { FlatList, View } from 'react-native';

import * as Styled from './Bookmarks.styled';

import { BookmarkItem } from '~/pages/Profile/components/favorites/components/BookmarkItem';
import { bookmarks } from '~/utils/constants/bookmarks';

export const Bookmarks = () => {
  return (
    <Styled.Wrapper>
      <FlatList
        data={bookmarks}
        horizontal={true}
        ItemSeparatorComponent={() => <View style={{ width: 10 }} />}
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item, index) => index.toString()}
        renderItem={({ item }) => {
          return <BookmarkItem code={item.code} name={item.name} />;
        }}
      />
    </Styled.Wrapper>
  );
};
