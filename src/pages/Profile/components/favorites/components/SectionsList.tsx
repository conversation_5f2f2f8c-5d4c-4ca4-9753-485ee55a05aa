import { useContext, useEffect, useState } from 'react';
import { View } from 'react-native';

import { favoritesContext } from '../context/favoritesContext';
import { InspirationSection } from '../sections/InspirationSection';

import * as Styled from './SectionsList.styled';

import { Empty } from '~/pages/Profile/components/favorites/components/Empty';
import { ArticleSection } from '~/pages/Profile/components/favorites/sections/ArticleSection';
import { BrandSection } from '~/pages/Profile/components/favorites/sections/BrandSection';
import { CouponSection } from '~/pages/Profile/components/favorites/sections/CouponSection';
import { DiscountSection } from '~/pages/Profile/components/favorites/sections/DiscountSection';
import { NewspaperSection } from '~/pages/Profile/components/favorites/sections/NewspaperSection';
import { useGetFavoritesQuery } from '~/redux/api/favorites';
import { useAppSelector } from '~/redux/store';

export const SectionsList = () => {
  const { active } = useContext(favoritesContext);
  const [empty, setEmpty] = useState<boolean>(false);
  const activeSections = useAppSelector((state) => state.activeSections);

  const { data } = useGetFavoritesQuery({
    bookmark: active,
  });

  useEffect(() => {
    setEmpty(data && data.length > 0 ? false : true);
  }, [data]);

  return (
    <Styled.Wrapper>
      {empty ? (
        <Empty />
      ) : (
        <View style={{ paddingBottom: 96 }}>
          {activeSections.card && <DiscountSection />}
          {activeSections.brand && <BrandSection />}
          {activeSections.coupon && <CouponSection />}
          {activeSections.leaflet && <NewspaperSection />}
          {activeSections.article && <ArticleSection />}
          {activeSections.inspiration && <InspirationSection />}
        </View>
      )}
    </Styled.Wrapper>
  );
};
