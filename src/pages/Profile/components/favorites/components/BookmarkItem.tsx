import { useContext } from 'react';

import * as Styled from './BookmarkItem.styled';

import { Typography } from '~/components/Typography/Typography';
import { favoritesContext } from '~/pages/Profile/components/favorites/context/favoritesContext';
import { IBookmarkItem } from '~/pages/Profile/components/favorites/types';
import theme from '~/theme';

export const BookmarkItem = ({ name, code }: IBookmarkItem) => {
  const context = useContext(favoritesContext);

  return (
    <Styled.Wrapper
      onPress={() => {
        context.setActive(code);
      }}
      active={context.active === code}>
      <Typography
        fontSize={16}
        fontWeight="500"
        style={{ textAlign: 'center' }}
        color={context.active === code ? theme.colors.purple[100] : theme.colors.gray[400]}>
        {name.toUpperCase()}
      </Typography>
    </Styled.Wrapper>
  );
};
