import FastImage from 'react-native-fast-image';

import * as Styled from './Empty.styled';

import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';
import { assets } from '~/theme/assets';

export const Empty = () => {
  return (
    <Styled.Empty>
      <Typography
        fontSize={16}
        fontWeight="600"
        color={theme.colors.black[300]}
        style={{ textAlign: 'center' }}>
        {`Brak elementów\n dodanych do ulubionych`}
      </Typography>
      <Styled.Image>
        <FastImage
          testID="default-image"
          style={{ width: '100%', height: '100%' }}
          source={assets.images.favorite_empty_png}
          resizeMode={'contain'}
        />
      </Styled.Image>
    </Styled.Empty>
  );
};
