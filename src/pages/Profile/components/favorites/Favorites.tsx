import { useState } from 'react';

import * as Styled from './Favorites.styled';

import { BookmarkEnum } from '~/enums/bookmarkEnum';
import { Bookmarks } from '~/pages/Profile/components/favorites/components/Bookmarks';
import { SectionsList } from '~/pages/Profile/components/favorites/components/SectionsList';
import { FavoritesContextProvider } from '~/pages/Profile/components/favorites/context/favoritesContext';

export const Favorites = () => {
  const [active, setActive] = useState<typeof BookmarkEnum | null>(BookmarkEnum.All);

  return (
    <Styled.Wrapper>
      <FavoritesContextProvider
        providerValues={{
          active,
          setActive,
        }}>
        <Bookmarks />
        <SectionsList />
      </FavoritesContextProvider>
    </Styled.Wrapper>
  );
};
