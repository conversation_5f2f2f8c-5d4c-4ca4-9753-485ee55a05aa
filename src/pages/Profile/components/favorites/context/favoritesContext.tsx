import { createContext, ReactNode } from 'react';

import { BookmarkEnum } from '~/enums/bookmarkEnum';

interface IFavoritesContext {
  active: typeof BookmarkEnum | null;
  setActive: (active: typeof BookmarkEnum) => void;
}

interface IFavoritesContextProvider {
  providerValues: IFavoritesContext;
  children: ReactNode;
}

const defaultContext: IFavoritesContext = {
  active: BookmarkEnum.All,
  setActive: () => {},
};

export const favoritesContext = createContext<IFavoritesContext>(defaultContext);

export const FavoritesContextProvider = ({
  providerValues,
  children,
}: IFavoritesContextProvider) => {
  return <favoritesContext.Provider value={providerValues}>{children}</favoritesContext.Provider>;
};
