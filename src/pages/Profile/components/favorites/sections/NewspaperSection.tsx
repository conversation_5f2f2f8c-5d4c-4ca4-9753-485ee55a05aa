import { useContext } from 'react';
import { View } from 'react-native';

import { NewspaperCard } from '~/components/Cards/NewspaperCard/NewspaperCard';
import { Section } from '~/components/Section/Section';
import { ContentSlider } from '~/components/Slider/Slider';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { validate } from '~/helpers/convertDate';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useNavigate } from '~/hooks/useNavigate';
import { favoritesContext } from '~/pages/Profile/components/favorites/context/favoritesContext';
import { useGetFavoritesQuery } from '~/redux/api/favorites';
import { RouteEnum } from '~/routes/routes';
import { INewspaper } from '~/types/newspaper';

export const NewspaperSection = () => {
  const context = useContext(favoritesContext);
  const { navigation } = useNavigate();

  const { postFavorite } = useFavoritesQuery();

  const { data, isSuccess } = useGetFavoritesQuery({
    object_type: ObjectTypeEnum.Leaflet,
    bookmark: context.active,
  });

  if (isSuccess && data.length === 0) return null;
  return (
    <Section title={'Moje gazetki'}>
      <ContentSlider
        data={data}
        renderItem={({ item }) => {
          const isActive = validate((item.object as INewspaper).valid_to);
          return (
            <View style={{ width: 175, height: 350 }}>
              <NewspaperCard
                isActive={isActive}
                key={`newspaper-${context.active}`}
                data={item.object as INewspaper}
                onPress={() =>
                  navigation.navigate(RouteEnum.NEWSPAPER_DETAIL, { id: item.object.id })
                }
                onFavoritePress={(objectId, isFavorite, categories, tags) => {
                  postFavorite({
                    isFavorite,
                    params: {
                      payload: {
                        object_id: objectId,
                        object_type: ObjectTypeEnum.Leaflet,
                      },
                      type: 'Leaflet',
                    },
                    event: {
                      object_type: ObjectTypeEnum.Leaflet,
                      categories_id_list: categories || [],
                      tags_id_list: tags || [],
                    },
                  });
                }}
              />
            </View>
          );
        }}
      />
    </Section>
  );
};
