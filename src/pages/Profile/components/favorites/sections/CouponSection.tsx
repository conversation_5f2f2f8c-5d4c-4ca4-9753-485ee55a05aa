import { useContext } from 'react';
import { View } from 'react-native';

import { CouponCard } from '~/components/Cards/CouponCard/CouponCard';
import { Section } from '~/components/Section/Section';
import { ContentSlider } from '~/components/Slider/Slider';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { validate } from '~/helpers/convertDate';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useNavigate } from '~/hooks/useNavigate';
import { favoritesContext } from '~/pages/Profile/components/favorites/context/favoritesContext';
import { useGetFavoritesQuery } from '~/redux/api/favorites';
import { RouteEnum } from '~/routes/routes';
import { ICoupon } from '~/types/coupon';

export const CouponSection = () => {
  const context = useContext(favoritesContext);
  const { navigation } = useNavigate();

  const { postFavorite } = useFavoritesQuery();

  const { data, isSuccess } = useGetFavoritesQuery({
    object_type: ObjectTypeEnum.Coupon,
    bookmark: context.active,
  });

  if (isSuccess && data.length === 0) return null;
  return (
    <Section title={'Moje okazje'}>
      <ContentSlider
        key={`coupon-${context.active}`}
        data={data}
        renderItem={({ item, index }) => {
          return (
            <View style={{ width: 170, height: 250 }}>
              <CouponCard
                isActive={validate((item.object as ICoupon).end_date)}
                onFavoritePress={(objectId, isFavorite, categories, tags) => {
                  postFavorite({
                    isFavorite,
                    params: {
                      payload: {
                        object_id: objectId,
                        object_type: ObjectTypeEnum.Coupon,
                      },
                      type: 'Coupon',
                    },
                    event: {
                      object_type: ObjectTypeEnum.Coupon,
                      categories_id_list: categories || [],
                      tags_id_list: tags || [],
                    },
                  });
                }}
                key={index}
                data={item.object as ICoupon}
                onPress={() => navigation.navigate(RouteEnum.COUPON_DETAIL, { id: item.object.id })}
              />
            </View>
          );
        }}
      />
    </Section>
  );
};
