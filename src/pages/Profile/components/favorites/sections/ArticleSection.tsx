import { useContext } from 'react';
import { View } from 'react-native';

import { ArticleCard, ArticleCardLofi } from '~/components/Cards/ArticleCard/ArticleCard';
import { Section } from '~/components/Section/Section';
import { ContentSlider } from '~/components/Slider/Slider';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useNavigate } from '~/hooks/useNavigate';
import { favoritesContext } from '~/pages/Profile/components/favorites/context/favoritesContext';
import { useGetFavoritesQuery } from '~/redux/api/favorites';
import { RouteEnum } from '~/routes/routes';
import { IArticle } from '~/types/article';

const ListEmptyComponent = () => {
  return (
    <View style={{ height: 240, flexDirection: 'row' }}>
      <ArticleCardLofi width={235} />
      <ArticleCardLofi width={235} />
      <ArticleCardLofi width={235} />
      <ArticleCardLofi width={235} />
      <ArticleCardLofi width={235} />
    </View>
  );
};

export const ArticleSection = () => {
  const context = useContext(favoritesContext);
  const { navigation } = useNavigate();

  const { postFavorite } = useFavoritesQuery();

  const { data, isSuccess } = useGetFavoritesQuery({
    object_type: ObjectTypeEnum.Article,
    bookmark: context.active,
  });

  if (isSuccess && data.length === 0) return null;
  else
    return (
      <Section title={'Moje artykuły'}>
        <ContentSlider
          key={`article-${context.active}`}
          data={data}
          ListEmptyComponent={ListEmptyComponent}
          renderItem={({ item, index }) => {
            return (
              <View style={{ width: 250 }}>
                <ArticleCard
                  onFavoritePress={(objectId, isFavorite, categories, tags) => {
                    postFavorite({
                      isFavorite,
                      params: {
                        payload: {
                          object_id: objectId,
                          object_type: ObjectTypeEnum.Article,
                        },
                        type: 'Article',
                      },
                      event: {
                        object_type: ObjectTypeEnum.Article,
                        categories_id_list: categories || [],
                        tags_id_list: tags || [],
                      },
                    });
                  }}
                  data={item.object as IArticle}
                  key={index}
                  onPress={() =>
                    navigation.navigate(RouteEnum.ARTICLE_DETAIL, { id: item.object.id })
                  }
                />
              </View>
            );
          }}
        />
      </Section>
    );
};
