import { useContext } from 'react';
import { View } from 'react-native';

import * as Styled from './section.styled';

import { BrandCardLofi } from '~/components/Cards/BrandCard/BrandCard';
import { DiscountCardLofi } from '~/components/Cards/DiscountCard/DiscountCard';
import { ImageComponent } from '~/components/ImageComponent/ImageComponent';
import { Section } from '~/components/Section/Section';
import { ContentSlider } from '~/components/Slider/Slider';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useNavigate } from '~/hooks/useNavigate';
import { favoritesContext } from '~/pages/Profile/components/favorites/context/favoritesContext';
import { useGetFavoritesQuery } from '~/redux/api/favorites';
import { RouteEnum } from '~/routes/routes';
import { IBaseBrand } from '~/types/brands';

const ListEmptyComponent = () => {
  return (
    <View style={{ flexDirection: 'row' }}>
      <BrandCardLofi />
      <BrandCardLofi />
      <BrandCardLofi />
      <BrandCardLofi />
      <BrandCardLofi />
    </View>
  );
};

export const BrandSection = () => {
  const context = useContext(favoritesContext);
  const { navigation } = useNavigate();

  const { data, isSuccess } = useGetFavoritesQuery({
    object_type: ObjectTypeEnum.Brand,
    bookmark: context.active,
  });

  if (isSuccess && data.length === 0) return null;
  return (
    <Section title={'Moje Marki'}>
      <ContentSlider
        key={`brand-${context.active}`}
        data={data}
        ListEmptyComponent={ListEmptyComponent}
        renderItem={({ item, index }) => {
          return (
            <Styled.ImageWrapper
              key={index}
              onPress={() =>
                navigation.navigate(RouteEnum.BRAND_DETAIL, { id: (item.object as IBaseBrand).id })
              }>
              <ImageComponent
                bgColor={(item.object as IBaseBrand).bg_color}
                uri={(item.object as IBaseBrand).logo}
                width={76}
                height={76}
                borderRadius={16}
                resizeMode={'contain'}
                style={{ aspectRatio: 1 }}
              />
            </Styled.ImageWrapper>
          );
        }}
      />
    </Section>
  );
};
