import { useContext } from 'react';
import { View } from 'react-native';

import { DiscountCard, DiscountCardLofi } from '~/components/Cards/DiscountCard/DiscountCard';
import { Section } from '~/components/Section/Section';
import { ContentSlider } from '~/components/Slider/Slider';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { validate } from '~/helpers/convertDate';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useNavigate } from '~/hooks/useNavigate';
import { favoritesContext } from '~/pages/Profile/components/favorites/context/favoritesContext';
import { useGetFavoritesQuery } from '~/redux/api/favorites';
import { RouteEnum } from '~/routes/routes';
import { IDiscount } from '~/types/discount';

const ListEmptyComponent = () => {
  return (
    <View style={{ height: 230, flexDirection: 'row' }}>
      <DiscountCardLofi />
      <DiscountCardLofi />
      <DiscountCardLofi />
      <DiscountCardLofi />
      <DiscountCardLofi />
    </View>
  );
};

export const DiscountSection = () => {
  const context = useContext(favoritesContext);
  const { navigation } = useNavigate();

  const { postFavorite } = useFavoritesQuery();

  const { data, isSuccess } = useGetFavoritesQuery({
    object_type: ObjectTypeEnum.Card,
    bookmark: context.active,
  });

  if (isSuccess && data.length === 0) return null;
  return (
    <Section title={'Moje rabaty\nHomeProfit'}>
      <ContentSlider
        key={`discount-${context.active}`}
        data={data}
        ListEmptyComponent={ListEmptyComponent}
        renderItem={({ item, index }) => {
          return (
            <View style={{ width: 170 }}>
              <DiscountCard
                isActive={validate((item.object as IDiscount).valid_to)}
                onFavoritePress={(objectId, isFavorite, categories, tags) => {
                  postFavorite({
                    isFavorite,
                    params: {
                      payload: {
                        object_id: objectId,
                        object_type: ObjectTypeEnum.Card,
                      },
                      type: 'Card',
                    },
                    event: {
                      object_type: ObjectTypeEnum.Card,
                      categories_id_list: categories || [],
                      tags_id_list: tags || [],
                    },
                  });
                }}
                key={index}
                data={item.object as IDiscount}
                onPress={() =>
                  navigation.navigate(RouteEnum.DISCOUNT_DETAIL, { id: item.object.id })
                }
              />
            </View>
          );
        }}
      />
    </Section>
  );
};
