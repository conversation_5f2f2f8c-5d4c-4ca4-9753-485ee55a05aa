import { useContext } from 'react';
import { View } from 'react-native';

import { InspirationImage } from '~/components/Inspiration/components/InspirationImage/InspirationImage';
import { Section } from '~/components/Section/Section';
import { ContentSlider } from '~/components/Slider/Slider';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useNavigate } from '~/hooks/useNavigate';
import { favoritesContext } from '~/pages/Profile/components/favorites/context/favoritesContext';
import { useGetFavoritesQuery } from '~/redux/api/favorites';
import { IInspiration } from '~/types/inspirations';

export const InspirationSection = () => {
  const context = useContext(favoritesContext);
  const { navigateToInspirationDetails } = useNavigate();

  const { postFavorite } = useFavoritesQuery();

  const { data, isSuccess } = useGetFavoritesQuery({
    object_type: ObjectTypeEnum.Inspiration,
    bookmark: context.active,
  });

  if (isSuccess && data.length === 0) return null;

  return (
    <Section showBottomLine={false} title={'Moje Inspiracje'}>
      <ContentSlider
        data={data}
        renderItem={({ item, index }) => (
          <View style={{ width: 170, height: 132 }}>
            <InspirationImage
              onFavoritePress={(objectId, isFavorite, categories, tags) =>
                postFavorite({
                  isFavorite,
                  params: {
                    payload: {
                      object_id: objectId,
                      object_type: ObjectTypeEnum.Inspiration,
                    },
                    type: 'Inspiration',
                  },
                  event: {
                    object_type: ObjectTypeEnum.Inspiration,
                    categories_id_list: categories || [],
                    tags_id_list: tags || [],
                  },
                })
              }
              key={index}
              data={item.object as IInspiration}
              onPress={() => navigateToInspirationDetails(item.object.id)}
            />
          </View>
        )}
      />
    </Section>
  );
};
