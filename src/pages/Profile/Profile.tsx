import { FC, ReactElement, useEffect } from 'react';
import { TouchableOpacity } from 'react-native';

import * as Styled from './Profile.styled';

import { Icon } from '~/components/Icon/Icon';
import { Layout } from '~/components/Layout/Layout';
import { Icon as NotifyIcon } from '~/components/Notification/Icon/Icon';
import { useNavigate } from '~/hooks/useNavigate';
import { Favorites } from '~/pages/Profile/components/favorites/Favorites';
import { Header } from '~/pages/Profile/components/Header';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { assets } from '~/theme/assets';
import { getPremium } from '~/redux/reducers/user/thunks';

/**
 * Profile page
 * @constructor
 * @return {React.FC}
 * @description Profile page
 * W zależności od tego, czy dla komponentu "Header", przekażemy pole partner, wyświetlimy czy nie, wyrenderujemy komponent "Partner" lub przycisk "Aktywuj premium"
 */

export const Profile: FC = (): ReactElement => {
  const { navigation, navigateBack } = useNavigate();
  const dispatch = useAppDispatch();
  const { detail, premium, token } = useAppSelector((state) => state.user);

  useEffect(() => {
    if (token && token.access) {
      dispatch(getPremium({ token: token.access }));
    }
  }, [token, dispatch]);

  return (
    <Layout
      padding={0}
      onGoBack={() => navigateBack()}
      headerOptions={{
        leftIcon: (
          <>
            <Icon iconSVG={assets.icons.logo_nav_my_profit_svg} width={138} height={52} />
          </>
        ),
        rightItem: (
          <>
            <TouchableOpacity onPress={() => navigation.navigate(RouteEnum.SETTINGS)} hitSlop={16}>
              <Icon iconSVG={assets.icons.settings_svg} width={24} height={24} />
            </TouchableOpacity>
            <NotifyIcon />
          </>
        ),
      }}>
      <Styled.Container>
        <Styled.HeaderWrapper>
          <Header
            profile={{
              name: detail?.first_name!,
              validTo: premium?.valid_to,
              status: premium ? 'premium' : 'otwarty',
            }}
            partner={premium}
          />
        </Styled.HeaderWrapper>
        <Styled.FavoritesWrapper>
          <Favorites />
        </Styled.FavoritesWrapper>
      </Styled.Container>
    </Layout>
  );
};
