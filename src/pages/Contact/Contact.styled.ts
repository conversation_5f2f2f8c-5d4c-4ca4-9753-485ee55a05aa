import styled from '@emotion/native';

import { LAYOUT_PADDING } from '~/helpers/layoutPadding';
import { flexColBetween } from '~/styles/flex.styled';
import { shadow } from '~/theme/shadows';
import { WIDE_SCREEN } from '~/utils/constants/shared';

export const Wrapper = styled.View`
  ${flexColBetween};
  flex: 1;
  gap: 12px;
  height: 100%;
  padding: 0 ${`${LAYOUT_PADDING}px`};
`;
export const ContactContentWrapper = styled.View`
  margin-top: 30px;
`;

export const ButtonWrapper = styled.View`
  width: 150px;
  margin: 8px auto;
`;

export const ContactInfo = styled.View`
  border-radius: 8px;
  ${shadow.gray[200]};
  display: flex;
  flex-direction: column;
  padding: 16px;
  gap: 16px;
  margin-bottom: 16px;
  width: 90%;
  max-width: ${`${WIDE_SCREEN}px`};
`;
