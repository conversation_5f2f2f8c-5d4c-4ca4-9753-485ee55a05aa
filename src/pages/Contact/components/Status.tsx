import React from 'react';

import * as Styled from './Status.styled';

import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';
import { IStatus } from '~/types/errors';

export const Status = ({ status }: { status: IStatus | undefined }) => {
  return (
    <>
      {status ? (
        <Styled.Wrapper>
          <Typography fontSize={16} fontWeight="600" color={theme.colors.white[100]}>
            {status.message}
          </Typography>
        </Styled.Wrapper>
      ) : null}
    </>
  );
};
