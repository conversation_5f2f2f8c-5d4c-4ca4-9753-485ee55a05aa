import styled from '@emotion/native';

import { flexColCenter } from '~/styles/flex.styled';
import theme from '~/theme';

export interface IWrapper {
  borderBottom?: boolean;
}

export const Wrapper = styled.View<IWrapper>`
  width: 80%;
  align-self: center;
  ${flexColCenter};
  border-bottom-width: ${({ borderBottom }) => (borderBottom ? '1px' : '0')};
  padding-bottom: 8px;
  border-color: ${theme.colors.gray[300]};
`;
export const Row = styled.TouchableOpacity`
  display: flex;
  flex-direction: row;
  gap: 8px;
  justify-content: flex-start;
`;
