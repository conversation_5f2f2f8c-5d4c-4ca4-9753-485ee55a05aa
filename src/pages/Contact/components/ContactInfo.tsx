import * as Styled from './ContactInfo.styled';

import { Icon } from '~/components/Icon/Icon';
import { Typography } from '~/components/Typography/Typography';
import { IContactInfo } from '~/pages/Contact/types';
import theme from '~/theme';

export const ContactInfo = ({
  onPress,
  icon,
  text,
  subText,
  borderBottom = false,
}: IContactInfo) => {
  return (
    <Styled.Wrapper borderBottom={borderBottom}>
      <Styled.Row onPress={onPress}>
        <Icon iconSVG={icon} width={24} height={24} />
        <Typography fontSize={16} fontWeight="600" style={{ textDecorationLine: 'underline' }}>
          {text}
        </Typography>
      </Styled.Row>

      {subText ? (
        <Styled.Row>
          <Typography fontSize={14} fontWeight="400" color={theme.colors.gray[400]}>
            {subText}
          </Typography>
        </Styled.Row>
      ) : null}
    </Styled.Wrapper>
  );
};
