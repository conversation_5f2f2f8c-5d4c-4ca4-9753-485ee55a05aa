import React, { useState } from 'react';
import { FormProvider } from 'react-hook-form';
import { View } from 'react-native';

import * as Styled from './Contact.styled';

import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { Input } from '~/components/Inputs/Input/Input';
import { Select } from '~/components/Inputs/Select/Select';
import { Loader } from '~/components/Loader/Loader';
import { ModalHeader } from '~/components/Modals/common/ModalHeader/ModalHeader';
import * as Popup from '~/components/Popup';
import { Typography } from '~/components/Typography/Typography';
import { useContactAction } from '~/events/hooks/useContactAction';
import { useContact } from '~/hooks/useContact';
import { useLinking } from '~/hooks/useLinking';
import { ContactInfo } from '~/pages/Contact/components/ContactInfo';
import { Status } from '~/pages/Contact/components/Status';
import { assets } from '~/theme/assets';

export const Contact = () => {
  const { navigation, form, status, info, loading, topics, button, onSubmit } = useContact();
  const [modalHeight] = useState<number>(0.84);
  const { openPhone, openEmail } = useLinking();

  //----EVENTS----
  useContactAction();
  //--X--EVENTS--X--
  return (
    <Popup.StaticHeight
      heightPercentage={status ? 0.6 : modalHeight}
      onClose={() => navigation.goBack()}>
      <Styled.Wrapper>
        <ModalHeader
          icon={assets.icons.contact_svg}
          width={30}
          height={30}
          title={'Kontakt'}
          uppercase={false}
        />
        {loading ? (
          <Loader />
        ) : (
          <>
            {!status ? (
              <>
                <View style={{ marginTop: 30 }}>
                  <Typography fontSize={14} fontWeight="600">
                    Napisz wiadomość
                  </Typography>
                </View>

                <FormProvider {...form}>
                  <Input name={'email'} label={'e-mail'} />
                  <Select name={'topic'} placeholder={'Temat'} options={topics} />
                  <Input name={'message'} label={'Treść'} multiline={true} />
                  <Styled.ButtonWrapper>
                    {button ? (
                      <Button
                        onPress={form.handleSubmit(onSubmit)}
                        variant={ButtonVariant.FIFTH}
                        size={ButtonSize.SMALL}
                        borderRadius={24}>
                        {`Wyślij`.toUpperCase()}
                      </Button>
                    ) : null}
                  </Styled.ButtonWrapper>
                </FormProvider>
              </>
            ) : null}
            <Status status={status} />
            <View style={{ marginBottom: 10 }}>
              <Typography fontSize={14} fontWeight="600">
                lub skontaktuj się z nami
              </Typography>
            </View>

            {info?.phone && info.hours && info?.email ? (
              <Styled.ContactInfo>
                <ContactInfo
                  onPress={() => openPhone(info?.phone)}
                  icon={assets.icons.phone_icon_svg}
                  text={info?.phone}
                  subText={info?.hours}
                  borderBottom={true}
                />
                <ContactInfo
                  onPress={() => openEmail(info?.email)}
                  icon={assets.icons.email_svg}
                  text={info?.email}
                />
              </Styled.ContactInfo>
            ) : null}
          </>
        )}
      </Styled.Wrapper>
    </Popup.StaticHeight>
  );
};
