import { RouteProp, useRoute } from '@react-navigation/native';
import { View } from 'react-native';

import { DiscountModal } from '~/components/Modals/DiscountModal/DiscountModal';
import { useUseDiscount } from '~/events/hooks/useUseDiscount';
import { useDiscountActions } from '~/hooks/useDiscountActions';
import { RouteEnum } from '~/routes/routes';
import { RootStackParamList } from '~/routes/types';

export const DiscountModalScreen = () => {
  const { params } = useRoute<RouteProp<RootStackParamList, RouteEnum.DISCOUNT_MODAL_SCREEN>>();
  const { handleCloseModal } = useDiscountActions();
  //----EVENTS----
  useUseDiscount({
    shop_id: params.data.brand_id,
    discount_id: params.data.discount_id,
    card_id: params.data.card_id,
  });

  //--X--EVENTS--X--
  return (
    <View style={{ height: '100%' }}>
      <DiscountModal
        isOpen={true}
        onClose={() => handleCloseModal()}
        data={params.data}
        code={params.code}
      />
    </View>
  );
};
