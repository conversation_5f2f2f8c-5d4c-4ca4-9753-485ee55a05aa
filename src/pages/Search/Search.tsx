import { RouteProp, useRoute } from '@react-navigation/native';
import { useContext, useEffect } from 'react';
import { View } from 'react-native';

import * as Styled from './Search.styled';

import { SearchModal } from '~/components/Modals/SearchModal/SearchModal';
import { ContentSlider } from '~/components/Slider/Slider';
import { FilterContext } from '~/context/filterContext';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useFilters } from '~/hooks/useFilters';
import { useSearch } from '~/hooks/useSearch';
import { useGetArticlesQuery } from '~/redux/api/articles';
import { useGetBrandsQuery } from '~/redux/api/brands';
import { useGetCouponsQuery } from '~/redux/api/coupons';
import { useGetCardsQuery } from '~/redux/api/discounts';
import { useGetInspirationsQuery } from '~/redux/api/inspirations';
import { useGetLeafletsQuery } from '~/redux/api/newspapers';
import { useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { RootStackParamList } from '~/routes/types';
import { IArticle } from '~/types/article';
import { IBrandDetail } from '~/types/brands';
import { ICoupon } from '~/types/coupon';
import { IDiscount } from '~/types/discount';
import { IInspiration } from '~/types/inspirations';
import { INewspaper } from '~/types/newspaper';

export type ISearchItem = IDiscount | ICoupon | IArticle | INewspaper | IBrandDetail | IInspiration;
export const Search = () => {
  const { params } = useRoute<RouteProp<RootStackParamList, RouteEnum.SEARCH>>();
  const { data, value: globalValue } = useAppSelector((state) => state.search);
  const { userSearches } = useAppSelector((state) => state.userSearches);
  const { removeAllFilters } = useFilters();
  const _filterContext = useContext(FilterContext);

  const {
    searchResult,
    renderContent,
    categoryName,
    onSubmit,
    onChange,
    // dynamicSelector,
    onNavigation,
  } = useSearch();
  // const { mayInterestYou } = useAppSelector(dynamicSelector(params.screenType));

  useEffect(() => {
    _filterContext.clearSearch();
    return () => {
      removeAllFilters();
    };
  }, []);

  const queryMap = {
    [ObjectTypeEnum.Card]: useGetCardsQuery,
    [ObjectTypeEnum.Coupon]: useGetCouponsQuery,
    [ObjectTypeEnum.Brand]: useGetBrandsQuery,
    [ObjectTypeEnum.Article]: useGetArticlesQuery,
    [ObjectTypeEnum.Leaflet]: useGetLeafletsQuery,
    [ObjectTypeEnum.Inspiration]: useGetInspirationsQuery,
  };

  const useQuery = queryMap[params.screenType];
  const { data: itemsData } = useQuery({});

  return (
    <Styled.ScrollView>
      <Styled.Container>
        <SearchModal
          onNavigate={() => onNavigation(params.screenType, {}, RouteEnum.SEARCH)}
          value={globalValue}
          searchResult={searchResult}
          onSubmit={(value) => onSubmit(value, params.screenType)}
          onValueChange={(value) => onChange(value, params.screenType)}
          category={categoryName(params.screenType)}
          placeholder={'Wyszukaj rabat'}
          popularSearches={[...userSearches.slice().reverse(), ...data].slice(0, 5)}
          mayInterestChildren={
            <ContentSlider
              itemWidth={170}
              ItemSeparatorComponent={() => <View style={{ width: 12 }} />}
              data={itemsData?.results as ISearchItem[]}
              renderItem={({ item, index }) => (
                <View style={{ width: 170 }}>{renderContent(params.screenType, item, index)}</View>
              )}
            />
          }
        />
      </Styled.Container>
    </Styled.ScrollView>
  );
};
