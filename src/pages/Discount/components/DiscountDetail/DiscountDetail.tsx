import Bugsnag from '@bugsnag/react-native';
import React, { FC, useContext, useEffect } from 'react';

import * as Styled from './DiscountDetail.styled';
import { Text } from 'react-native';
import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { DetailSection } from '~/components/DetailSection/DetailSection';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { ShopTypeEnumShow } from '~/enums/shared';
import { useNavigate } from '~/hooks/useNavigate';
import { discountContext } from '~/pages/Discount/context/discountContext';
import { getDiscountAddresses } from '~/redux/reducers/content/discounts/thunks';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import theme from '~/theme';

const formatDate = (dateString: string, includeYear = false) => {
  const date = new Date(dateString);
  const dayMonth = `${date.getDate().toString().padStart(2, '0')}.${(date.getMonth() + 1)
    .toString()
    .padStart(2, '0')}`;
  return includeYear ? `${dayMonth}.${date.getFullYear().toString().slice(-2)}` : dayMonth;
};
export const DiscountDetail: FC = () => {
  const { navigation } = useNavigate();
  const dispatch = useAppDispatch();
  const { addresses } = useAppSelector((state) => state.discounts);
  const context = useContext(discountContext);
  const transformedData = {
    start_date: context.valid_from ? formatDate(context.valid_from) : '',
    end_date: context.valid_to ? ` - ${formatDate(context.valid_to, true)}` : ' - DO ODWOŁANIA',
  };

  const handleFindShop = () => {
    navigation.navigate(RouteEnum.FIND_SHOP_MODAL_SCREEN, {
      screenType: ObjectTypeEnum.Card,
      shopId: context.brand.id,
      objectId: context.id,
      objectType: ObjectTypeEnum.Card,
    });
  };

  useEffect(() => {
    if (context.id) {
      dispatch(getDiscountAddresses({ id: context.id }));
    }
  }, [context.id]);

  return (
    <Styled.Container>
      <DetailSection
        leftText={'Obowiązuje: '}
        rightText={transformedData.start_date + transformedData.end_date}
      />
      <DetailSection
        bottomBorder={context.type !== 0 && context.type !== 2}
        leftText={'Status: '}
        rightText={`${ShopTypeEnumShow[context.type]}`.toUpperCase()}
        fontWeight="500"
      />
      {addresses.length > 0 ? (
        context.type === 0 || context.type === 2 ? (
          <DetailSection leftText={'Sklepy: '} bottomBorder={true}>
            <Styled.ButtonWrapper>
              <Button
                variant={ButtonVariant.FOURTH}
                size={ButtonSize.SMALL}
                borderRadius={20}
                typographyStyles={{
                  fontSize: 12,
                  fontWeight: '600',
                  color: theme.colors.purple['100'],
                }}
                onPress={handleFindShop}>
                {`znajdź sklep`.toUpperCase()}
              </Button>
            </Styled.ButtonWrapper>
          </DetailSection>
        ) : null
      ) : null}
    </Styled.Container>
  );
};
