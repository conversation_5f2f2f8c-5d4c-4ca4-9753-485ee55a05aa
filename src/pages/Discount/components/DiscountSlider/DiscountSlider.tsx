import { useContext } from 'react';
import { View } from 'react-native';

import { DiscountCard } from '~/components/Cards/DiscountCard/DiscountCard';
import { Section } from '~/components/Section/Section';
import { ContentSlider } from '~/components/Slider/Slider';
import { FilterContext } from '~/context/filterContext';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useNavigate } from '~/hooks/useNavigate';
import { discountContext } from '~/pages/Discount/context/discountContext';
import { RouteEnum } from '~/routes/routes';

export const DiscountSlider = () => {
  const { navigateToDiscountCollection, navigateToDiscountDetails } = useNavigate();
  const context = useContext(discountContext);
  const _filterContext = useContext(FilterContext);
  const { postFavorite } = useFavoritesQuery();

  return (
    <Section
      title={'Podobne Rabaty\nHomeProfit'}
      counter={`[${context.counters.discount}]`}
      onSectionHeaderPress={() => {
        _filterContext.clear();
        _filterContext.handleSimilarCategoryCollection(context.categories);
        navigateToDiscountCollection({
          params: {
            category_ids: context.categories,
          },
          prevScreen: RouteEnum.DISCOUNT_DETAIL,
        });
      }}>
      <ContentSlider
        data={context.similar.discounts.slice(0, 10)}
        renderItem={({ item, index }) => (
          <View style={{ width: 170 }}>
            <DiscountCard
              key={index}
              data={item}
              onFavoritePress={(objectId, isFavorite, categories, tags) => {
                postFavorite({
                  isFavorite,
                  params: {
                    payload: {
                      object_id: objectId,
                      object_type: ObjectTypeEnum.Card,
                    },
                    type: 'Card',
                  },
                  event: {
                    object_type: ObjectTypeEnum.Card,
                    categories_id_list: categories || [],
                    tags_id_list: tags || [],
                  },
                });
              }}
              onPress={() => navigateToDiscountDetails(item.id)}
            />
          </View>
        )}
      />
    </Section>
  );
};
