import React, { FC, useContext } from 'react';

import * as Styled from './DiscountHeader.styled';

import { CardBgImage } from '~/components/Cards/components/CardBgImage';
import { CardDesc } from '~/components/Cards/components/CardDesc';
import { CardFavourite } from '~/components/Cards/components/CardFavourite';
import { CardImage } from '~/components/Cards/components/CardImage';
import { CardPrice } from '~/components/Cards/components/CardPrice';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { validate } from '~/helpers/convertDate';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useNavigate } from '~/hooks/useNavigate';
import { discountContext } from '~/pages/Discount/context/discountContext';
import { RouteEnum } from '~/routes/routes';

export const DiscountHeader: FC = () => {
  const { navigation } = useNavigate();
  const context = useContext(discountContext);
  const isActive = validate(context.valid_to);
  const { postFavorite } = useFavoritesQuery();

  return (
    <Styled.Container>
      {/*----ABSOLUTE CONTENT------*/}
      <Styled.ImageWrapper>
        <Styled.PriceWrapper>
          <CardPrice
            valueType={context.value_type!}
            discountValue={context.discount_value}
            size={'large'}
          />
        </Styled.PriceWrapper>
        <CardBgImage
          borderRadius={4}
          uri={context.bg_image}
          bgColor={context.bg_color}
          isActive={isActive}
          triangle={{
            triangleSize: 'large',
            trianglePosition: 'top',
          }}
        />
        <Styled.IconWrapper>
          <CardFavourite
            active={context.is_favorite}
            onPress={(isFavorite) => {
              postFavorite({
                isFavorite,
                params: {
                  payload: {
                    object_id: context.id,
                    object_type: ObjectTypeEnum.Card,
                  },
                  type: 'Card',
                },
                event: {
                  object_type: ObjectTypeEnum.Card,
                  categories_id_list: context.categories || [],
                  tags_id_list: context.tags || [],
                },
              });
            }}
            size={'small'}
          />
        </Styled.IconWrapper>
        <Styled.BrandWrapper
          onPress={() =>
            navigation.navigate(RouteEnum.BRAND_DETAIL, {
              id: context.brand.id,
              is_rec: context.brand.is_rec,
            })
          }>
          <CardImage
            brand={context.brand}
            isActive={isActive}
            customLogo={context.logo}
            size={'large'}
          />
        </Styled.BrandWrapper>
      </Styled.ImageWrapper>

      {/*----MAIN DESCRIPTION----*/}
      <Styled.TitleWrapper>
        <CardDesc
          brand={
            context.custom_brand_name
              ? context.custom_brand_name
              : context.brand
              ? context.brand.name
              : null
          }
          title={context.name}
          size={'large'}
        />
      </Styled.TitleWrapper>
    </Styled.Container>
  );
};
