import styled from '@emotion/native';
import { Dimensions } from 'react-native';

const deviceWidth = Dimensions.get('window').width;

export const Container = styled.View`
  height: ${deviceWidth > 500 ? '387px' : '287px'};
  justify-content: space-between;
  padding-bottom: 40px;
`;
// ----IMAGES----
export const ImageWrapper = styled.View`
  position: relative;
  width: 100%;
  height: ${deviceWidth > 500 ? '250px' : '150px'};
`;

export const PriceWrapper = styled.View`
  position: absolute;
  z-index: 10;
  padding: 8px;
  right: 0;
`;
export const IconWrapper = styled.View`
  position: absolute;
  z-index: 10;
  top: 6px;
  left: 6px;
`;

export const BrandWrapper = styled.TouchableOpacity`
  position: absolute;
  bottom: -70px;
  padding: 8px;
  right: 0;
  z-index: 10;
`;
export const TitleWrapper = styled.View`
  height: 100%;
  padding-top: 42px;
  justify-content: center;
`;
