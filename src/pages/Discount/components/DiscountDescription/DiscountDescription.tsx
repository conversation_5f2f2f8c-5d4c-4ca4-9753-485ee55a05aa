import { FC, useContext } from 'react';

import * as Styled from './DiscountDescription.styled';

import { Typography } from '~/components/Typography/Typography';
import { discountContext } from '~/pages/Discount/context/discountContext';

export const DiscountDescription: FC = () => {
  const context = useContext(discountContext);
  return (
    <Styled.Wrapper>
      <Typography fontSize={18} fontWeight="600">
        Szczegóły oferty
      </Typography>
      {context.description ? (
        <Styled.Container>
          <Typography fontSize={14} fontWeight="300">
            {context.description}
          </Typography>
        </Styled.Container>
      ) : null}
    </Styled.Wrapper>
  );
};
