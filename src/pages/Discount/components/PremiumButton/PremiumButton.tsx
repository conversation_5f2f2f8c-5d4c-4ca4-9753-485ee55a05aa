import { FC, useContext, useEffect, useState } from 'react';

import * as Styled from './PremiumButton.styled';

import { Button } from '~/components/Button/Button';
import { ButtonVariant } from '~/components/Button/enum';
import { prepareCouponPrice, prepareDiscountPrice } from '~/components/Cards/helpers/prepareData';
import { DiscountTypeEnum, IIntendedForEnum } from '~/enums/shared';
import { useNavigate } from '~/hooks/useNavigate';
import { discountContext } from '~/pages/Discount/context/discountContext';
import { buttonStyles } from '~/pages/Discount/helpers/filterDiscounts';
import { useLazyGetDiscountCodeQuery } from '~/redux/api/discounts';
import { useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import theme from '~/theme';
import { assets } from '~/theme/assets';
import { ISingleDiscount } from '~/types/discount';
import { minusDiscountValueType } from '~/utils/constants/shared';

export const PremiumButton: FC = () => {
  const context = useContext(discountContext);
  const { token } = useAppSelector((state) => state.user);
  const [getDiscountCode] = useLazyGetDiscountCodeQuery();
  const [codes, setCodes] = useState<Array<{ id: number; code: string }>>([]);
  const { navigation } = useNavigate();

  const onButtonPress = (discount: ISingleDiscount, code?: string) => {
    // EVENTS - Interaction
    context.events.deep();
    if (!token) navigation.navigate(RouteEnum.AUTH_MODAL);
    if (discount.discount_type === DiscountTypeEnum.Ins) {
      context.actions.handleOpenModal(
        { ...discount },
        context,
        'ten string w tym przypadku nic nie robi ale coś musi być napisane zeby w komponencie DiscountModal warunek code? był spełniony',
      );
    }
    if (!code) return;
    context.actions.handleOpenModal({ ...discount }, context, code);
  };

  const premiumButtonData = prepareCouponPrice(context.value_type!, context.discount_value, false);

  const handleGetDiscountCode = async () => {
    let arr: Array<{ id: number; code: string }> = [];
    try {
      await Promise.all(
        context.discounts.map(async (discount) => {
          try {
            const res = await getDiscountCode({ id: discount.id });
            if (discount.discount_type === DiscountTypeEnum.Ins) {
              arr.push({ id: discount.id, code: '' });
            }
            if (res.data?.code) {
              arr.push({ id: discount.id, code: res.data.code });
            }
          } catch (err) {
            console.log('ERROR:', err);
          }
        }),
      );
      setCodes(arr);
    } catch (error) {
      console.error('Error:', error);
    }
  };

  useEffect(() => {
    handleGetDiscountCode();
  }, [context.discounts]);

  return (
    <Styled.Container>
      {context.discounts.map((discount) => {
        const data = prepareDiscountPrice(discount.discount_value_type!, discount.discount_value);
        if (codes.some((code) => code.id === discount.id) || token === null) {
          return (
            <Button
              iconPosition={'right'}
              icon={
                discount.intended_for === IIntendedForEnum.Prm ? assets.icons.star_svg : undefined
              }
              key={discount.id}
              variant={ButtonVariant.PRIMARY}
              borderRadius={30}
              typographyStyles={{
                color:
                  buttonStyles[context.actions.usrType][discount.intended_for]?.textColor ||
                  theme.colors.white['100'],
                fontWeight: '600',
                fontSize: 16,
              }}
              backgroundColor={
                buttonStyles[context.actions.usrType][discount.intended_for]?.backgroundColor ||
                theme.colors.black['300']
              }
              onPress={() =>
                onButtonPress(discount, codes.find((code) => code.id === discount.id)?.code)
              }>
              {`rabat ${discount.intended_for === IIntendedForEnum.Prm ? 'premium' : ''} ${
                minusDiscountValueType[discount.discount_value_type]
              }${data.price} ${data.unit}`.toUpperCase()}
            </Button>
          );
        } else null;
      })}
      {context.actions.isPremiumButtonExistInDiscountsCollection(context.discounts) &&
        (context.actions.usrType === IIntendedForEnum.All ||
        context.actions.usrType === IIntendedForEnum.Bsc ? (
          <Button
            variant={ButtonVariant.PRIMARY}
            borderRadius={30}
            typographyStyles={{
              color: theme.colors.purple['100'],
              fontWeight: '600',
              fontSize: 16,
            }}
            backgroundColor={theme.colors.purple['200']}
            onPress={() => {
              if (context.actions.usrType === IIntendedForEnum.All) {
                navigation.navigate(RouteEnum.AUTH_MODAL);
              } else {
                navigation.navigate(RouteEnum.PREMIUM_MODAL);
              }
            }}>
            {`Aktywuj premium ${premiumButtonData.range} ${premiumButtonData.price} ${premiumButtonData.unit}`.toUpperCase()}
          </Button>
        ) : null)}
    </Styled.Container>
  );
};
