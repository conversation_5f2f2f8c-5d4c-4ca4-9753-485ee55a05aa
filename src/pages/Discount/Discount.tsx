import { useNetInfo } from '@react-native-community/netinfo';
import { useFocusEffect } from '@react-navigation/native';
import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { RefreshControl, View } from 'react-native';

import { DiscountCard, DiscountCardLofi } from '~/components/Cards/DiscountCard/DiscountCard';
import { EmptyData } from '~/components/EmptyData/EmptyData';
import { FilterLayout } from '~/components/Filters/FilterLayout';
import { Layout } from '~/components/Layout/Layout';
import { List } from '~/components/List/List';
import { ListItem } from '~/components/List/ListItem';
import { Search } from '~/components/Search';
import { FilterContext } from '~/context/filterContext';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useDisplayContentType, useFilterCategoriesChange } from '~/events/hooks';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useListLayout } from '~/hooks/useListLayout';
import { useNavigate } from '~/hooks/useNavigate';
import { useResetSearchWithBack } from '~/hooks/useResetSearchWithBack';
import { useLazyGetCategoriesQuery } from '~/redux/api/categories';
import { useGetCardsQuery, useGetCardsWithPaginationQuery } from '~/redux/api/discounts';
import { useAppSelector } from '~/redux/store';
import * as Styled from '~/styles/common.styled';
import theme from '~/theme';
import { ParamsOptions } from '~/types/shared';

const ListEmptyComponent = () => {
  return (
    <>
      <View style={{ height: 230, flexDirection: 'row' }}>
        <DiscountCardLofi />
        <DiscountCardLofi />
      </View>
      <View style={{ height: 230, flexDirection: 'row' }}>
        <DiscountCardLofi />
        <DiscountCardLofi />
      </View>
    </>
  );
};

export const Discount = () => {
  const { columnsSwitch, setColumnsSwitch, screenWidth } = useListLayout();
  const { isConnected } = useNetInfo();

  const { navigateToDiscountCollection, navigateToDiscountDetails } = useNavigate();
  const _filterContext = useContext(FilterContext);
  const { handleBackWithReset } = useResetSearchWithBack();
  const { city } = useAppSelector((state) => state.user.detail);
  const [page, setPage] = useState(1);

  let queryParams: ParamsOptions = useMemo(() => {
    const params = _filterContext.buildFilterParams();

    return {
      ...params,
      ...(page > 1 ? { page: page } : {}),
      city_id: city,
    };
  }, [
    _filterContext.selectedCategoryFilter,
    _filterContext.selectedShopFilter,
    _filterContext.selectedSortFilter,
    _filterContext.selectedSimilarCategoryFilter,
    _filterContext.searchId,
    _filterContext.searchString,
    city,
    page,
  ]);

  /**
   * RTK Query
   */
  const { data, isFetching, error, refetch } = useGetCardsWithPaginationQuery(queryParams);
  const { data: mayInterestYouData } = useGetCardsQuery({ city_id: city });
  const [trigger] = useLazyGetCategoriesQuery();
  const { postFavorite } = useFavoritesQuery();

  /*
   * Trigger next page
   */
  let isFetchingNextPage = false;
  const onEndReached = () => {
    if (!isConnected) return;
    if (data?.next === null || isFetching || isFetchingNextPage) return;
    isFetchingNextPage = true;
    setPage(page + 1);
    setTimeout(() => {
      isFetchingNextPage = false;
    }, 500);
  };

  /*
   * Fetch categories for specific object type (Card, Coupon etc.)
   */
  useEffect(() => {
    trigger({ content_type: ObjectTypeEnum.Card }).then((res) => {
      _filterContext.setSelectedCategoryFilter(res.data);
    });
  }, []);

  /**
   * Events:
   */
  useDisplayContentType({
    type: ObjectTypeEnum.Card,
    collection: data,
  });
  useFilterCategoriesChange(_filterContext.buildFilterParams().category_ids || []);

  /**
   * Reset page on focus (it triggers reset cache of rtk query and fetch new data from server)
   */
  useFocusEffect(
    useCallback(() => {
      setPage(1);
      return () => {
        setPage(1);
      };
    }, [
      _filterContext.selectedCategoryFilter,
      _filterContext.selectedShopFilter,
      _filterContext.selectedSortFilter,
      _filterContext.selectedSimilarCategoryFilter,
      _filterContext.searchId,
      _filterContext.searchString,
      city,
    ]),
  );

  useEffect(() => {
    refetch();
  }, [isConnected]);

  return (
    <Layout
      error={error}
      padding={0}
      onGoBack={handleBackWithReset}
      headerOptions={{
        resultCounter: data?.count,
        label: 'Rabaty homeprofit',
        leftIcon: true,
        rightItem: <Search to={ObjectTypeEnum.Card} />,
        labelStyle: {
          fontSize: 16,
          fontWeight: '600',
          color: theme.colors.black['100'],
        },
      }}>
      <Styled.ListWrapper>
        <Styled.FilterWrapper>
          <FilterLayout
            removeSingleFilter={(id, type) => {
              setPage(1);
              _filterContext.removeSingleFilter(id, type);
            }}
            removeAllFilters={() => {
              setPage(1);
              _filterContext.clear();
            }}
            tiles={_filterContext.tiles}
            screenType={ObjectTypeEnum.Card}
            setColumnsSwitch={setColumnsSwitch}
            columnsSwitch={columnsSwitch}
          />
        </Styled.FilterWrapper>
        <Styled.Content>
          {isFetching || (data && data.results.length > 0) ? (
            <List
              contentContainerStyle={{ gap: columnsSwitch > 1 ? 10 : 0 }}
              loading={isFetching}
              data={data?.results || []}
              refreshControl={
                <RefreshControl
                  refreshing={isFetching}
                  onRefresh={() => {
                    setPage(1);
                  }}
                />
              }
              columnsSwitch={columnsSwitch}
              onEndReached={data?.results && !isFetching ? onEndReached : () => {}}
              ListEmptyComponent={isFetching ? ListEmptyComponent : undefined}
              renderItem={({ item }) => {
                return (
                  <ListItem columnsSwitch={columnsSwitch} gap={0} screenWidth={screenWidth}>
                    <DiscountCard
                      size={columnsSwitch > 1 ? 'small' : 'large'}
                      onFavoritePress={(objectId, isFavorite, categories, tags) => {
                        postFavorite({
                          isFavorite,
                          params: {
                            payload: {
                              object_id: objectId,
                              object_type: ObjectTypeEnum.Card,
                            },
                            type: 'Card',
                          },
                          event: {
                            object_type: ObjectTypeEnum.Card,
                            categories_id_list: categories || [],
                            tags_id_list: tags || [],
                          },
                        });
                      }}
                      data={item}
                      onPress={() => navigateToDiscountDetails(item.id, item.is_rec)}
                    />
                  </ListItem>
                );
              }}
            />
          ) : null}
          {/* EMPTY DATA */}
          {(!isFetching && !data) || data?.results.length === 0 ? (
            <EmptyData
              data={mayInterestYouData?.results.slice(0, 10)}
              counter={mayInterestYouData?.count}
              onSectionHeaderPress={() => {
                _filterContext.clear();
                navigateToDiscountCollection();
              }}
              renderItem={({ item }) => {
                return (
                  <View style={{ width: 170 }}>
                    <DiscountCard
                      onFavoritePress={(objectId, isFavorite, categories, tags) => {
                        postFavorite({
                          isFavorite,
                          params: {
                            payload: {
                              object_id: objectId,
                              object_type: ObjectTypeEnum.Card,
                            },
                            type: 'Card',
                          },
                          event: {
                            object_type: ObjectTypeEnum.Card,
                            categories_id_list: categories || [],
                            tags_id_list: tags || [],
                          },
                        });
                      }}
                      data={item}
                      onPress={() => navigateToDiscountDetails(item.id, item.is_rec)}
                    />
                  </View>
                );
              }}
            />
          ) : null}
        </Styled.Content>
      </Styled.ListWrapper>
    </Layout>
  );
};
