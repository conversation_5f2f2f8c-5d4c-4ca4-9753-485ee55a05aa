import { createContext, ReactNode } from 'react';

import { IIntendedForEnum, ShopTypeEnum, ValueTypeEnum } from '~/enums/shared';
import { IAddFavoriteMethod } from '~/hooks/useFavorites';
import { ICoupon } from '~/types/coupon';
import { IDiscount, IDiscountDetail, ISingleDiscount } from '~/types/discount';

export interface IDiscountContext extends IDiscountDetail {
  counters: {
    discount: number;
    coupons: number;
  };
  similar: {
    discounts: IDiscount[];
    coupons: ICoupon[];
  };
  codes: Array<{
    id: number;
    code: string;
  }>;
  actions: {
    usrType: IIntendedForEnum;
    //addFavorite: (content: IAddFavoriteMethod) => void;
    handleOpenModal: (payload: ISingleDiscount, card: IDiscountDetail, code: string) => void;
    handleCloseModal: () => void;
    isPremiumButtonExistInDiscountsCollection: (discounts: ISingleDiscount[]) => boolean;
    //error: { status: boolean; message: string };
  };
  events: {
    deep: () => void;
  };
}

export interface IDiscountContextProvider {
  providerValues: IDiscountContext;
  children: ReactNode;
}

const defaultContext: IDiscountContext = {
  id: 0,
  name: '',
  valid_from: '',
  valid_to: '',
  brand: {
    id: 0,
    name: '',
    logo: '',
    is_favorite: false,
  },
  tags: [],
  discount_value: '',
  categories: [],
  value_type: ValueTypeEnum.OTHER,
  bg_color: '',
  bg_image: '',
  description: '',
  is_favorite: false,
  site: '',
  type: ShopTypeEnum.ALL,
  counters: {
    coupons: 0,
    discount: 0,
  },
  similar: {
    discounts: [],
    coupons: [],
  },
  codes: [],
  discounts: [],
  actions: {
    usrType: IIntendedForEnum.All,
    //addFavorite: () => {},
    handleOpenModal: () => {},
    handleCloseModal: () => {},
    isPremiumButtonExistInDiscountsCollection: () => false,
    //error: { status: false, message: '' },
  },
  events: {
    deep: () => {},
  },
};

export const discountContext = createContext<IDiscountContext>(defaultContext);

export const DiscountContextProvider = ({ providerValues, children }: IDiscountContextProvider) => {
  return <discountContext.Provider value={providerValues}>{children}</discountContext.Provider>;
};
