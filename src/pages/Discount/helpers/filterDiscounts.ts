import { IIntendedForEnum } from '~/enums/shared';
import theme from '~/theme';
import { ISingleDiscount } from '~/types/discount';

const baseBlackStyle = {
  backgroundColor: theme.colors.black['300'],
  textColor: theme.colors.white['100'],
};

const basePurpleStyle = {
  backgroundColor: theme.colors.purple['100'],
  textColor: theme.colors.white['100'],
};

const baseGrayStyle = {
  backgroundColor: theme.colors.gray['1000'],
  textColor: theme.colors.black['300'],
};

export const buttonStyles = {
  [IIntendedForEnum.All]: {
    [IIntendedForEnum.All]: baseBlackStyle,
    [IIntendedForEnum.Bsc]: baseBlackStyle,
    [IIntendedForEnum.Prm]: basePurpleStyle,
  },
  [IIntendedForEnum.Bsc]: {
    [IIntendedForEnum.All]: baseBlackStyle,
    [IIntendedForEnum.Bsc]: baseBlackStyle,
    [IIntendedForEnum.Prm]: basePurpleStyle,
  },
  [IIntendedForEnum.Prm]: {
    [IIntendedForEnum.All]: baseGrayStyle,
    [IIntendedForEnum.Bsc]: baseGrayStyle,
    [IIntendedForEnum.Prm]: basePurpleStyle,
  },
};

export const filterDiscountButtonsByUserType = (
  discounts: ISingleDiscount[],
  userType: IIntendedForEnum,
) => {
  const all = [IIntendedForEnum.All, IIntendedForEnum.Bsc];

  if (userType === IIntendedForEnum.Bsc || userType === IIntendedForEnum.All) {
    const hasPremium = discounts.some((discount) => discount.intended_for === IIntendedForEnum.Prm);
    if (hasPremium) {
      return discounts.filter((discount) => discount.intended_for !== IIntendedForEnum.Prm);
    } else {
      return discounts.filter((discount) => all.includes(discount.intended_for));
    }
  } else if (userType === IIntendedForEnum.Prm) {
    return discounts;
  } else {
    return discounts;
  }
};
export const isPremiumButtonExistInDiscountsCollection = (
  discounts: ISingleDiscount[],
): boolean => {
  return discounts.some((discount) => discount.intended_for === IIntendedForEnum.Prm);
};
export const sortDiscountButtons = (
  discounts: ISingleDiscount[],
  userType: IIntendedForEnum,
): ISingleDiscount[] => {
  const filteredDiscounts = filterDiscountButtonsByUserType(discounts, userType);
  return [...filteredDiscounts].sort((a, b) => {
    return Number(a.intended_for) - Number(b.intended_for);
  });
};
