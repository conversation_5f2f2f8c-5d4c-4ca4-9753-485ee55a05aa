import { RouteProp, useRoute } from '@react-navigation/native';
import { RefreshControl, TouchableOpacity, View, ScrollView } from 'react-native';
import { useEffect, useMemo, useRef, useState } from 'react';

import { DiscountCoupon } from '../components/DiscountCoupon/DiscountCoupon';
import * as Styled from '../Discount.styled';

import { Icon } from '~/components/Icon/Icon';
import { Layout } from '~/components/Layout/Layout';
import { Loader } from '~/components/Loader/Loader';
import * as Lofi from '~/components/Loader/Lofi';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useDisplayCard } from '~/events/hooks/useDisplayCard';
import { useInteraction } from '~/events/hooks/useInteraction';
import { validate } from '~/helpers/convertDate';
import { useDiscountActions } from '~/hooks/useDiscountActions';
import { useNavigate } from '~/hooks/useNavigate';
import { DiscountDescription } from '~/pages/Discount/components/DiscountDescription/DiscountDescription';
import { DiscountDetail as Detail } from '~/pages/Discount/components/DiscountDetail/DiscountDetail';
import { DiscountHeader } from '~/pages/Discount/components/DiscountHeader/DiscountHeader';
import { DiscountSlider } from '~/pages/Discount/components/DiscountSlider/DiscountSlider';
import { PremiumButton } from '~/pages/Discount/components/PremiumButton/PremiumButton';
import { DiscountContextProvider } from '~/pages/Discount/context/discountContext';
import { isPremiumButtonExistInDiscountsCollection } from '~/pages/Discount/helpers/filterDiscounts';
import { userType } from '~/pages/Discount/helpers/userType';
import { useGetCouponsQuery, useGetSimilarCouponsQuery } from '~/redux/api/coupons';
import {
  useGetCardsDetailsQuery,
  useGetCardsQuery,
  useGetSimilarCardsQuery,
  useLazyGetCardsDetailsQuery,
} from '~/redux/api/discounts';
import { useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { RootStackParamList } from '~/routes/types';
import theme from '~/theme';
import { assets } from '~/theme/assets';
import { ParamsOptions } from '~/types/shared';

const DiscountDetailLofi = () => {
  return (
    <Lofi.Details>
      <Lofi.Container>
        <View style={{ position: 'absolute', right: 32, bottom: -32 }}>
          <Lofi.Circle border />
        </View>
      </Lofi.Container>
      <View style={{ gap: 16, paddingTop: 32, alignItems: 'flex-end' }}>
        <Lofi.Text width={'60%'} />
        <Lofi.Text width={'80%'} />
        <Lofi.Text width={'100%'} />
      </View>
      <View style={{ paddingTop: 16, gap: 16 }}>
        <Lofi.Container height={32} rx={32} />
        <Lofi.Container height={32} rx={32} />
        <Lofi.Container height={32} rx={32} />
      </View>
    </Lofi.Details>
  );
};

export const DiscountDetail = () => {
  const { navigateBack } = useNavigate();
  const scrollViewRef = useRef<ScrollView>(null);

  const activeSections = useAppSelector((state) => state.activeSections);
  const { handleDisplayCard } = useDisplayCard({
    type: ObjectTypeEnum.Card,
  });
  // const { share } = useShare();
  const { filterDiscounts, handleOpenModal, handleCloseModal } = useDiscountActions();

  const { premium, token } = useAppSelector((state) => state.user);
  const [filteredDiscounts, setFilteredDiscounts] = useState<any>([]);
  const usrType = userType(premium, token);

  const { params: detailParams } =
    useRoute<RouteProp<RootStackParamList, RouteEnum.DISCOUNT_DETAIL>>();
  const {
    data: detail,
    isLoading: isLoadingDetail,
    error: detailError,
  } = useGetCardsDetailsQuery({
    id: detailParams.id,
  });
  const [getCardsDetails, { isLoading }] = useLazyGetCardsDetailsQuery();

  let queryParams: ParamsOptions = useMemo(() => {
    return {
      category_ids: detail?.categories,
    };
  }, [detail]);

  //SIMILARS
  const { data: similarDiscounts, isLoading: isLoadingSimilarDisc } =
    useGetSimilarCardsQuery(queryParams);
  const { data: similarCoupons, isLoading: isLoadingSimilarCoupons } =
    useGetSimilarCouponsQuery(queryParams);

  useEffect(() => {
    if (detail?.discounts) {
      setFilteredDiscounts(filterDiscounts(detail.discounts, usrType));
    }
    scrollViewRef.current?.scrollTo({ y: 0, animated: true });
  }, [premium, detail]);

  /**
   * EVENTS:
   */
  useEffect(() => {
    if (detailParams.id === detail?.id) {
      handleDisplayCard({
        detail: detail,
        is_rec: detailParams.is_rec ? detailParams.is_rec : false,
      });
    }
  }, [detail]);
  const { handleDepp } = useInteraction({
    object_type: ObjectTypeEnum.Card,
    object_id: detailParams.id,
  });

  const similar = similarDiscounts?.results.filter((item) => item.id !== detail?.id);

  if (isLoadingDetail) return <DiscountDetailLofi />;
  return (
    <Layout
      error={detailError}
      onGoBack={() => navigateBack()}
      padding={0}
      headerOptions={{
        label: 'szczegóły rabatu',
        leftIcon: true,
        // rightItem: (
        //   <TouchableOpacity onPress={share}>
        //     <Icon iconSVG={assets.icons.share_svg} width={24} height={24} />
        //   </TouchableOpacity>
        // ),
        labelStyle: {
          fontSize: 16,
          fontWeight: '600',
          color: theme.colors.black['100'],
        },
      }}>
      <DiscountContextProvider
        providerValues={{
          ...detail!,
          discounts: detail ? filteredDiscounts : [],
          counters: {
            coupons: similarCoupons?.count ?? 0,
            discount: similarDiscounts?.count ?? 0,
          },
          similar: {
            discounts: similar || [],
            coupons: similarCoupons?.results || [],
          },
          codes: [],
          actions: {
            usrType,
            handleOpenModal,
            handleCloseModal,
            isPremiumButtonExistInDiscountsCollection: () =>
              detail ? isPremiumButtonExistInDiscountsCollection(detail.discounts) : false,
          },
          events: {
            deep: handleDepp,
          },
        }}>
        <Styled.Wrapper
            ref={scrollViewRef}
          refreshControl={
            <RefreshControl
              refreshing={isLoading}
              onRefresh={() => getCardsDetails({ id: detailParams.id })}
            />
          }>
          <Styled.Container>
            <DiscountHeader />
            <Styled.Gap>
              {detail?.discounts.length !== 0 && validate(detail?.valid_to) ? (
                <PremiumButton />
              ) : null}
              <Detail />
              <DiscountDescription />
            </Styled.Gap>
          </Styled.Container>
          <Styled.SliderWrapper>
            {isLoadingSimilarDisc ? (
              <Loader />
            ) : similar && similar.length > 0 ? (
              activeSections.card && <DiscountSlider />
            ) : null}
          </Styled.SliderWrapper>
          <Styled.SliderWrapper>
            {isLoadingSimilarCoupons ? (
              <Loader />
            ) : similarCoupons && similarCoupons.count > 0 ? (
              activeSections.coupon && <DiscountCoupon />
            ) : null}
          </Styled.SliderWrapper>
        </Styled.Wrapper>
      </DiscountContextProvider>
    </Layout>
  );
};
