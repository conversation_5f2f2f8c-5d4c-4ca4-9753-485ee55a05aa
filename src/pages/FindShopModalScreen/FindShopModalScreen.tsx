import { RouteProp, useRoute } from '@react-navigation/native';
import { View } from 'react-native';

import { ShopModal } from '~/components/Modals/ShopModal/ShopModal';
import { useRedirectionCall, useRedirectionNavi } from '~/events/hooks';
import { useFetchByContentType } from '~/hooks/useFetchByContentType';
import { useShopModalActions } from '~/hooks/useShopModalActions';
import { useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { RootStackParamList } from '~/routes/types';

export const FindShopModalScreen = () => {
  const user = useAppSelector((state) => state.user.detail);
  const {
    prepareDataForShopModal,
    handleCloseModal,
    onLocalizationPress,
    onPhonePress,
    onUserLocalizationChangePress,
  } = useShopModalActions();
  const { dynamicSelector } = useFetchByContentType();
  const { params } = useRoute<RouteProp<RootStackParamList, RouteEnum.FIND_SHOP_MODAL_SCREEN>>();
  const { addresses } = useAppSelector(dynamicSelector(params.screenType));
  const [userLocationAddresses, otherAddresses] = prepareDataForShopModal(
    addresses,
    user.city_name,
  );

  //----EVENTS----
  const _fnPhoneEvent = useRedirectionCall((data) => onPhonePress(data));
  const _fnMapEvent = useRedirectionNavi((data) => onLocalizationPress(data));
  // --X--EVENTS--X--
  return (
    <View style={{ height: '100%' }}>
      <ShopModal
        onUserLocalizationChangePress={() => onUserLocalizationChangePress(params)}
        onLocalizationPress={(data) =>
          _fnMapEvent(data, params.shopId, params.objectId, params.objectType)
        }
        onPhonePress={(data) => _fnPhoneEvent(data, params.shopId)}
        restShopCondition={!!Object.entries(otherAddresses).length}
        isOpen={true}
        onClose={() => handleCloseModal()}
        userLocationAddresses={userLocationAddresses}
        otherAddresses={otherAddresses}
        userCity={user?.city_name}
      />
    </View>
  );
};
