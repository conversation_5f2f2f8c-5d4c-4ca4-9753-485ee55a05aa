import { createContext, ReactNode } from 'react';

import { ValueTypeEnum } from '~/enums/shared';
import { IAddFavoriteMethod } from '~/hooks/useFavorites';
import { ICoupon, ICouponDetail } from '~/types/coupon';
import { IDiscount } from '~/types/discount';

interface ICouponContext extends ICouponDetail {
  counters: {
    coupons: number;
    discounts: number;
  };
  similar: {
    coupons: ICoupon[];
    discounts: IDiscount[];
  };
  events: {
    deep: () => void;
  };
}

export interface ICouponContextProvider {
  providerValues: ICouponContext;
  children: ReactNode;
}

const defaultContext: ICouponContext = {
  id: 0,
  title: '',
  start_date: '',
  end_date: '',
  brand: {
    id: 0,
    name: '',
    logo: '',
    is_favorite: false,
  },
  counters: {
    coupons: 0,
    discounts: 0,
  },
  similar: {
    coupons: [],
    discounts: [],
  },
  discount_value: '',
  categories: [],
  value_type: ValueTypeEnum.OTHER,
  bg_color: '',
  bg_image: '',
  short_description: '',
  affiliate_link: '',
  coupon_string: '',
  is_favorite: false,
  tags: [],
  events: {
    deep: () => {},
  },
};

export const couponContext = createContext<ICouponContext>(defaultContext);

export const CouponContextProvider = ({ providerValues, children }: ICouponContextProvider) => {
  return <couponContext.Provider value={providerValues}>{children}</couponContext.Provider>;
};
