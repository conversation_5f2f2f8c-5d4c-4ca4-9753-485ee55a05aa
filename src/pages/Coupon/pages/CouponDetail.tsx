import { RouteProp, useRoute } from '@react-navigation/native';
import { RefreshControl, TouchableOpacity, View, ScrollView } from 'react-native';
import { useEffect, useMemo, useRef } from 'react';

import * as Styled from '../Coupon.styled';

import { Icon } from '~/components/Icon/Icon';
import { Layout } from '~/components/Layout/Layout';
import { Loader } from '~/components/Loader/Loader';
import * as Lofi from '~/components/Loader/Lofi';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useDisplayCard } from '~/events/hooks/useDisplayCard';
import { useInteraction } from '~/events/hooks/useInteraction';
import { useNavigate } from '~/hooks/useNavigate';
import { CouponDescription } from '~/pages/Coupon/components/CouponDescription/CouponDescription';
import { CouponDetail as Detail } from '~/pages/Coupon/components/CouponDetail/CouponDetail';
import { CouponDiscount } from '~/pages/Coupon/components/CouponDiscount/CouponDiscount';
import { CouponHeader } from '~/pages/Coupon/components/CouponHeader/CouponHeader';
import { CouponSlider } from '~/pages/Coupon/components/CouponSlider/CouponSlider';
import { PremiumButton } from '~/pages/Coupon/components/PremiumButton/PremiumButton';
import { CouponContextProvider } from '~/pages/Coupon/context/couponContext';
import {
  useGetCouponsDetailsQuery,
  useGetCouponsQuery,
  useGetSimilarCouponsQuery,
  useLazyGetCouponsDetailsQuery,
} from '~/redux/api/coupons';
import { useGetCardsQuery, useGetSimilarCardsQuery } from '~/redux/api/discounts';
import { useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { RootStackParamList } from '~/routes/types';
import theme from '~/theme';
import { assets } from '~/theme/assets';
import { ParamsOptions } from '~/types/shared';

const CouponDetailLofi = () => {
  return (
    <Lofi.Details>
      <Lofi.Container>
        <View style={{ position: 'absolute', right: 32, bottom: -32 }}>
          <Lofi.Circle border />
        </View>
      </Lofi.Container>
      <View style={{ gap: 16, paddingTop: 32, alignItems: 'flex-end' }}>
        <Lofi.Text width={'60%'} />
        <Lofi.Text width={'80%'} />
        <Lofi.Text width={'100%'} />
      </View>
      <View style={{ paddingTop: 16, gap: 16 }}>
        <Lofi.Container height={32} rx={32} />
        <Lofi.Container height={32} rx={32} />
        <Lofi.Container height={32} rx={32} />
      </View>
    </Lofi.Details>
  );
};

export const CouponDetail = () => {
  const { navigateBack } = useNavigate();
  const scrollViewRef = useRef<ScrollView>(null);
  const activeSections = useAppSelector((state) => state.activeSections);
  // const { share } = useShare();

  const { params: detailParams } =
    useRoute<RouteProp<RootStackParamList, RouteEnum.COUPON_DETAIL>>();
  const {
    data: detail,
    isLoading: isLoadingDetail,
    error: detailError,
  } = useGetCouponsDetailsQuery({
    id: detailParams.id,
  });
  const [getCouponsDetails, { isLoading }] = useLazyGetCouponsDetailsQuery();

  let queryParams: ParamsOptions = useMemo(() => {
    return {
      category_ids: detail?.categories,
    };
  }, [detail]);

  //SIMILARS
  const { data: similarDiscounts, isLoading: isLoadingSimilarDiscounts } = useGetSimilarCardsQuery({
    ...queryParams,
  });
  const { data: similarCoupons, isLoading: isLoadingSimilarCoupons } = useGetSimilarCouponsQuery({
    ...queryParams,
  });

  const { handleDisplayCard } = useDisplayCard({
    type: ObjectTypeEnum.Coupon,
  });

  // EVENTS - DISPLAY CARD
  useEffect(() => {
    if (detailParams.id === detail?.id) {
      handleDisplayCard({
        detail: detail,
        is_rec: detailParams.is_rec ? detailParams.is_rec : false,
      });
    }
    scrollViewRef.current?.scrollTo({ y: 0, animated: true });
  }, [detail]);

  // EVENTS - INTERACTION
  const { handleDepp } = useInteraction({
    object_type: ObjectTypeEnum.Coupon,
    object_id: detailParams.id,
  });

  const similar = similarCoupons?.results.filter((item) => item.id !== detail?.id);

  if (isLoadingDetail) return <CouponDetailLofi />;
  return (
    <Layout
      error={detailError}
      onGoBack={() => navigateBack()}
      padding={0}
      headerOptions={{
        label: 'szczegóły okazji',
        leftIcon: true,
        // rightItem: (
        //   <TouchableOpacity onPress={share}>
        //     <Icon iconSVG={assets.icons.share_svg} width={24} height={24} />
        //   </TouchableOpacity>
        // ),
        labelStyle: {
          fontSize: 16,
          fontWeight: '600',
          color: theme.colors.black['100'],
        },
      }}>
      <CouponContextProvider
        providerValues={{
          ...detail!,
          counters: {
            coupons: similarCoupons?.count ?? 0,
            discounts: similarDiscounts?.count ?? 0,
          },
          similar: {
            discounts: similarDiscounts?.results || [],
            coupons: similar || [],
          },
          events: {
            deep: handleDepp,
          },
        }}>
        <Styled.Wrapper
          ref={scrollViewRef}
          refreshControl={
            <RefreshControl
              refreshing={isLoading}
              onRefresh={() => getCouponsDetails({ id: detailParams.id })}
            />
          }>
          <Styled.Container>
            <CouponHeader />
            <PremiumButton />
            <Detail />
          </Styled.Container>
          <Styled.Desc>
            <CouponDescription />
          </Styled.Desc>
          <Styled.SliderContainer>
            {isLoadingSimilarDiscounts ? (
              <Loader />
            ) : similarDiscounts && similarDiscounts.count > 0 ? (
              activeSections.card && <CouponDiscount />
            ) : null}
            {isLoadingSimilarCoupons ? (
              <Loader />
            ) : similar && similar.length > 0 ? (
              <CouponSlider />
            ) : null}
          </Styled.SliderContainer>
        </Styled.Wrapper>
      </CouponContextProvider>
    </Layout>
  );
};
