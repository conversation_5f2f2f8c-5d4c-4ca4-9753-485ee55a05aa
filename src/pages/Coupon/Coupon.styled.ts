import styled from '@emotion/native';

import { ItemListPageWrapper } from '~/styles/common.styled';
import theme from '~/theme';

export const ListWrapper = styled.View`
  ${ItemListPageWrapper}
`;

export const Wrapper = styled.ScrollView`
  flex: 1;
  flex-direction: column;
  gap: 15px;
`;
export const Container = styled.View`
  // ---- SPACE BETWEEN SECTIONS ----
  gap: 30px;
  padding: 16px 16px 0 16px;
`;
export const Desc = styled.View`
  padding: 16px 16px 0 16px;
  gap: 10px;
`;

export const SliderContainer = styled.View`
  gap: 30px;
  background-color: ${theme.colors.white['200']};
  padding-bottom: 50px;
`;
