import React, { FC, useContext } from 'react';
import { View } from 'react-native';

import { DiscountCard } from '~/components/Cards/DiscountCard/DiscountCard';
import { Section } from '~/components/Section/Section';
import { ContentSlider } from '~/components/Slider/Slider';
import { FilterContext } from '~/context/filterContext';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useNavigate } from '~/hooks/useNavigate';
import { couponContext } from '~/pages/Coupon/context/couponContext';
import { RouteEnum } from '~/routes/routes';

export const CouponDiscount: FC = () => {
  const _filterContext = useContext(FilterContext);
  const { navigateToDiscountCollection, navigateToDiscountDetails } = useNavigate();
  const context = useContext(couponContext);
  const { postFavorite } = useFavoritesQuery();

  return (
    <Section
      style={{ paddingTop: 25 }}
      isLoading={context.similar.discounts === null}
      title={'Podobne Rabaty\nHomeProfit'}
      counter={`[${context.counters.discounts}]`}
      onSectionHeaderPress={() => {
        _filterContext.clear();
        _filterContext.handleSimilarCategoryCollection(context.categories);
        navigateToDiscountCollection({
          params: {
            category_ids: context.categories,
          },
          prevScreen: RouteEnum.COUPON_DETAIL,
        });
      }}>
      <ContentSlider
        data={context.similar.discounts.slice(0, 10)}
        renderItem={({ item, index }) => (
          <View style={{ width: 170 }}>
            <DiscountCard
              key={index}
              data={item}
              onFavoritePress={(objectId, isFavorite, categories, tags) => {
                postFavorite({
                  isFavorite,
                  params: {
                    payload: {
                      object_id: objectId,
                      object_type: ObjectTypeEnum.Card,
                    },
                    type: 'Card',
                  },
                  event: {
                    object_type: ObjectTypeEnum.Card,
                    categories_id_list: categories || [],
                    tags_id_list: tags || [],
                  },
                });
              }}
              onPress={() => navigateToDiscountDetails(item.id)}
            />
          </View>
        )}
      />
    </Section>
  );
};
