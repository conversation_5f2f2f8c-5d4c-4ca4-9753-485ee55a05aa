import React, { FC, useContext } from 'react';

import * as Styled from './PremiumButton.styled';

import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { prepareCouponPrice } from '~/components/Cards/helpers/prepareData';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { DiscountTypeEnum, IIntendedForEnum } from '~/enums/shared';
import { useNavigate } from '~/hooks/useNavigate';
import { couponContext } from '~/pages/Coupon/context/couponContext';
import { RouteEnum } from '~/routes/routes';
import { minusValueType } from '~/utils/constants/shared';
import theme from '~/theme';
import { useLinking } from '~/hooks/useLinking';

export const PremiumButton: FC = () => {
  const context = useContext(couponContext);
  const { navigation } = useNavigate();
  const { openUrl } = useLinking();

  const handleNavigateToAffiliate = () => {
    if (context.affiliate_link) {
      openUrl(context.affiliate_link);
    }
  };

  const press = () => {
    // EVENTS - Interaction
    context.events.deep();

    const data = prepareCouponPrice(context.value_type!, context.discount_value, false);
    navigation.navigate(RouteEnum.DISCOUNT_MODAL_SCREEN, {
      code: context.coupon_string!,
      data: {
        id: context.id,
        discount_value: `${minusValueType[context.value_type!]}${data.price}${data.unit}`,
        intended_for: IIntendedForEnum.All,
        discount_type: DiscountTypeEnum.Txt,
        affiliate_link: context.affiliate_link,
        brand_id: context.brand.id,
        object_id: context.id,
        object_type: ObjectTypeEnum.Coupon,
        code: context.coupon_string!,
      },
    });
  };
  const data = prepareCouponPrice(context.value_type!, context.discount_value, false);

  // if (!context.coupon_string) return null;
  return (
    <Styled.Container>
      {!data.price || data.price.length <= 0 ? (
        <Button
          typographyStyles={{ color: theme.colors.white['100'], fontSize: 14, fontWeight: '600' }}
          variant={ButtonVariant.SECONDARY}
          backgroundColor={theme.colors.purple['100']}
          onPress={handleNavigateToAffiliate}>
          {`przejdź do sklepu`.toUpperCase()}
        </Button>
      ) : (
        <Button
          onPress={press}
          variant={ButtonVariant.PRIMARY}
          size={ButtonSize.MEDIUM}
          borderRadius={100}>
          {`odbierz okazję ${minusValueType[context.value_type!]}${data.price}${
            data.unit ? data.unit : ''
          } `.toUpperCase()}
        </Button>
      )}
    </Styled.Container>
  );
};
