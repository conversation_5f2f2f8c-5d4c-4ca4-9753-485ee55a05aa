import Bugsnag from '@bugsnag/react-native';
import { FC, useContext, useEffect } from 'react';

import * as Styled from './CouponDetail.styled';

import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { DetailSection } from '~/components/DetailSection/DetailSection';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { ShopTypeEnum, ShopTypeEnumShow } from '~/enums/shared';
import { convertEndDate, convertStartDate } from '~/helpers/convertDate';
import { useNavigate } from '~/hooks/useNavigate';
import { couponContext } from '~/pages/Coupon/context/couponContext';
import { getBrandAddresses } from '~/redux/reducers/content/brands/thunks';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import theme from '~/theme';

export const CouponDetail: FC = () => {
  const dispatch = useAppDispatch();
  const { navigation } = useNavigate();
  const context = useContext(couponContext);
  const { addresses } = useAppSelector((state) => state.brands);
  const startDate = convertStartDate(context.start_date);
  const endDate = context.end_date ? ' - ' + convertEndDate(context.end_date) : '';

  const handleFindShop = () => {
    dispatch(getBrandAddresses({ id: context.brand.id }))
      .then(() => {
        navigation.navigate(RouteEnum.FIND_SHOP_MODAL_SCREEN, {
          screenType: ObjectTypeEnum.Brand,
          shopId: context.brand.id,
          objectId: context.id,
          objectType: ObjectTypeEnum.Coupon,
        });
      })
      .catch((err) => {
        Bugsnag.notify(new Error(err));
      });
  };

  useEffect(() => {
    if (context.id) {
      dispatch(getBrandAddresses({ id: context.brand.id }));
    }
  }, [context.id]);

  return (
    <Styled.Container>
      <DetailSection leftText={'Obowiązuje: '} rightText={startDate + endDate} />
      {context.shop_type ? (
        <DetailSection
          leftText={'Status: '}
          rightText={`${ShopTypeEnumShow[context.shop_type]}`.toUpperCase()}
          fontWeight="500"
          bottomBorder={true}
        />
      ) : null}
      {addresses.length > 0 ? (
        context.shop_type !== ShopTypeEnum.ONLINE ? (
          <DetailSection leftText={'Sklepy: '} bottomBorder={true}>
            <Styled.ButtonWrapper>
              <Button
                variant={ButtonVariant.FOURTH}
                size={ButtonSize.SMALL}
                borderRadius={20}
                typographyStyles={{
                  fontSize: 12,
                  fontWeight: '600',
                  color: theme.colors.purple['100'],
                }}
                onPress={handleFindShop}>
                {`znajdź sklep`.toUpperCase()}
              </Button>
            </Styled.ButtonWrapper>
          </DetailSection>
        ) : null
      ) : null}
    </Styled.Container>
  );
};
