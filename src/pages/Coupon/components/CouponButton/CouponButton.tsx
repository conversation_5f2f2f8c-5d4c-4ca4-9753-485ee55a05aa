import React, { FC, useContext, useState } from 'react';

import * as Styled from './CouponButton.styled';

import { Button } from '~/components/Button/Button';
import { ButtonVariant } from '~/components/Button/enum';
import { couponContext } from '~/pages/Coupon/context/couponContext';
import { unit } from '~/utils/constants/shared';

/**
 * @description
 * W tym komponencie w sekcji showModal jest wykorzystany komponent DiscountModal
 * W zależności od typu jaki określimy przy wywołaniu, odpowiednie pola będą obecne w interfejsie lub nie
 * Przykładowo
 * ```
 * <DiscountModal<DiscountType.PHONE_CODE> - przyjmie pola phone i code
 * <DiscountModal<DiscountType.COPY_CODE> - przyjmie pole code
 * <DiscountModal<DiscountType.BAR_CODE> - przyjmie pole icon
 * <DiscountModal<DiscountType.QR_CODE> - przyjmie pole icon
 * ```
 * */

// TODO: Button musi zostać przerobiony aby działał tak samo jak na Discount (jednak to nie to samo) zostawiam to na później
export const CouponButton: FC = () => {
  const context = useContext(couponContext);
  const [showModal, setShowModal] = useState<boolean>(false);
  return (
    <Styled.Container>
      <Button
        variant={ButtonVariant.PRIMARY}
        borderRadius={30}
        onPress={() => setShowModal(!showModal)}>
        {`odbierz okazję ${
          context.discount_value + ' ' + unit[context.value_type!]
        } `.toUpperCase()}
      </Button>
    </Styled.Container>
  );
};
