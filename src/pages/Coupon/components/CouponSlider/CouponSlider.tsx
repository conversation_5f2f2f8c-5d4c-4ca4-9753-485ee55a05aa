import { FC, useContext } from 'react';
import { View } from 'react-native';

import { CouponCard } from '~/components/Cards/CouponCard/CouponCard';
import { Section } from '~/components/Section/Section';
import { ContentSlider } from '~/components/Slider/Slider';
import { FilterContext } from '~/context/filterContext';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useNavigate } from '~/hooks/useNavigate';
import { couponContext } from '~/pages/Coupon/context/couponContext';
import { RouteEnum } from '~/routes/routes';

export const CouponSlider: FC = () => {
  const context = useContext(couponContext);
  const _filterContext = useContext(FilterContext);
  const { navigateToCouponCollection, navigateToCouponDetails } = useNavigate();
  const { postFavorite } = useFavoritesQuery();

  return (
    <Section
      isLoading={context.similar.coupons === null}
      title={'Zobacz inne okazje'}
      counter={`[${context.counters.coupons}]`}
      onSectionHeaderPress={() => {
        _filterContext.clear();
        _filterContext.handleSimilarCategoryCollection(context.categories);
        navigateToCouponCollection({
          params: {
            category_ids: context.categories,
          },
          prevScreen: RouteEnum.COUPON_DETAIL,
        });
      }}>
      <ContentSlider
        data={context.similar.coupons.slice(0, 10)}
        renderItem={({ item, index }) => (
          <View style={{ width: 170 }}>
            <CouponCard
              key={index}
              data={item}
              onFavoritePress={(objectId, isFavorite, categories, tags) => {
                postFavorite({
                  isFavorite,
                  params: {
                    payload: {
                      object_id: objectId,
                      object_type: ObjectTypeEnum.Coupon,
                    },
                    type: 'Coupon',
                  },
                  event: {
                    object_type: ObjectTypeEnum.Coupon,
                    categories_id_list: categories || [],
                    tags_id_list: tags || [],
                  },
                });
              }}
              onPress={() => navigateToCouponDetails(item.id)}
            />
          </View>
        )}
      />
    </Section>
  );
};
