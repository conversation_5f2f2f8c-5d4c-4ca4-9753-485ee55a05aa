import { FC, useContext } from 'react';

import * as Styled from './CouponDescription.styled';

import { Typography } from '~/components/Typography/Typography';
import { couponContext } from '~/pages/Coupon/context/couponContext';

export const CouponDescription: FC = () => {
  const context = useContext(couponContext);
  return (
    <>
      {context.title_description ? (
        <Typography fontSize={18} fontWeight="600">
          {context.title_description}
        </Typography>
      ) : null}
      {context.short_description ? (
        <Styled.Container>
          <Typography fontSize={14} fontWeight="300">
            {context.short_description}
          </Typography>
        </Styled.Container>
      ) : null}
    </>
  );
};
