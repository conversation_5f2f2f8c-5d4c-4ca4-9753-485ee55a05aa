import styled from '@emotion/native';
import { Dimensions } from 'react-native';

const deviceWidth = Dimensions.get('window').width;
export const Container = styled.View`
  height: ${deviceWidth > 500 ? '387px' : '276px'};
`;
// ----IMAGES----
export const ImageWrapper = styled.View`
  position: relative;
  width: 100%;
  flex: ${deviceWidth > 500 ? `${2}` : `${1}`};
`;
export const TitleWrapper = styled.View`
  width: 100%;
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-start;
`;
export const IconPriceWrapper = styled.View`
  position: absolute;
  z-index: 10;
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 8px;
`;

export const BrandWrapper = styled.TouchableOpacity`
  position: absolute;
  bottom: -70px;
  padding: 8px;
  right: 0;
  z-index: 10;
`;
