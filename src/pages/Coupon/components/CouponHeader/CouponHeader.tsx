import React, { FC, useContext } from 'react';

import * as Styled from './CouponHeader.styled';

import { CardBgImage } from '~/components/Cards/components/CardBgImage';
import { CardFavourite } from '~/components/Cards/components/CardFavourite';
import { CardImage } from '~/components/Cards/components/CardImage';
import { CardPrice } from '~/components/Cards/components/CardPrice';
import { Typography } from '~/components/Typography/Typography';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { validate } from '~/helpers/convertDate';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useNavigate } from '~/hooks/useNavigate';
import { couponContext } from '~/pages/Coupon/context/couponContext';
import { RouteEnum } from '~/routes/routes';
import theme from '~/theme';

export const CouponHeader: FC = () => {
  const { navigation } = useNavigate();
  const context = useContext(couponContext);
  const isActive = validate(context.end_date);
  const { postFavorite } = useFavoritesQuery();

  return (
    <Styled.Container>
      {/*----ABSOLUTE CONTENT------*/}
      <Styled.ImageWrapper>
        <Styled.IconPriceWrapper>
          <CardFavourite
            active={context.is_favorite}
            onPress={(isFavorite) => {
              postFavorite({
                isFavorite,
                params: {
                  payload: {
                    object_id: context.id,
                    object_type: ObjectTypeEnum.Coupon,
                  },
                  type: 'Coupon',
                },
                event: {
                  object_type: ObjectTypeEnum.Coupon,
                  categories_id_list: context.categories || [],
                  tags_id_list: context.tags || [],
                },
              });
            }}
          />
          {context.value_type !== null && context.value_type !== undefined ? (
            <CardPrice
              valueType={context.value_type}
              discountValue={context.discount_value}
              size={'large'}
            />
          ) : null}
        </Styled.IconPriceWrapper>
        <CardBgImage
          borderRadius={4}
          isActive={isActive}
          uri={context.bg_image}
          bgColor={context.bg_color}
        />
        <Styled.BrandWrapper
          onPress={() =>
            navigation.navigate(RouteEnum.BRAND_DETAIL, {
              id: context.brand.id,
              is_rec: context.brand.is_rec,
            })
          }>
          <CardImage
            brand={context.brand}
            isActive={isActive}
            customLogo={context.logo}
            size={'large'}
          />
        </Styled.BrandWrapper>
      </Styled.ImageWrapper>

      {/*----MAIN DESCRIPTION----*/}
      <Styled.TitleWrapper>
        {context.brand ? (
          <Typography fontSize={14} fontWeight="500" color={theme.colors.gray['800']}>
            {context.brand.name}
          </Typography>
        ) : null}

        <Typography fontSize={20} fontWeight="600" color={theme.colors.black['100']}>
          {context.title}
        </Typography>
      </Styled.TitleWrapper>
    </Styled.Container>
  );
};
