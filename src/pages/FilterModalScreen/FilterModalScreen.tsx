import { RouteProp, useRoute } from '@react-navigation/native';
import { useContext } from 'react';
import { View } from 'react-native';

import { shopCollection, sortCollection } from './constants';

import { FilterModal } from '~/components/Modals/FilterModal/FilterModal';
import { FilterContext } from '~/context/filterContext';
import { useFilterShopChange, useFilterStatus } from '~/events/hooks';
import { useDiscountActions } from '~/hooks/useDiscountActions';
import { useFilters } from '~/hooks/useFilters';
import { useNavigate } from '~/hooks/useNavigate';
import { RouteEnum } from '~/routes/routes';
import { RootStackParamList } from '~/routes/types';

export const FilterModalScreen = () => {
  const { params } = useRoute<RouteProp<RootStackParamList, RouteEnum.FILTER_MODAL_SCREEN>>();
  const { handleCloseModal } = useDiscountActions();
  const { navigateToLocalization, navigateBack } = useNavigate();
  const _filterContext = useContext(FilterContext);
  const { cityId, cityName } = useFilters();

  // ----EVENTS----
  useFilterShopChange(_filterContext.selectedShopFilter?.type);
  const modalCloseWithEvent = useFilterStatus(() => handleCloseModal());
  // --X--EVENTS--X--

  return (
    <View style={{ height: '100%' }}>
      <FilterModal
        onUserLocalizationChangePress={() =>
          navigateToLocalization({ prevScreen: RouteEnum.FILTER_MODAL_SCREEN, params: params })
        }
        screenType={params.type}
        handleAll={_filterContext.handleAll}
        isOpen={true}
        onClose={() => modalCloseWithEvent()}
        submit={navigateBack}
        clear={() => _filterContext.clear()}
        handleSort={_filterContext.handleSortCollection}
        handleShop={(item) => _filterContext.handleShopCollection(item)}
        handleCategory={_filterContext.handleCategoryCollection}
        cityId={cityId}
        cityName={cityName}
        data={{
          sortCollection: sortCollection,
          shopCollection: shopCollection,
          selectedSortFilter: _filterContext.selectedSortFilter,
          selectedShopFilter: _filterContext.selectedShopFilter,
          selectedCategoryFilter: _filterContext.selectedCategoryFilter,
        }}
      />
    </View>
  );
};
