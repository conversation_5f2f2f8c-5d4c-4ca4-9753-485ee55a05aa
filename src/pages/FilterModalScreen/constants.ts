import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { ShopTypeEnum, SortByEnum } from '~/enums/shared';
import { ICategoryShop, ICategorySort } from '~/types/category';

export const sortCollection: ICategorySort[] = [
  {
    id: 1,
    name: '<PERSON><PERSON><PERSON><PERSON>',
    selected: false,
    type: SortByEnum.NEWEST,
    displayFor: [
      ObjectTypeEnum.Card,
      ObjectTypeEnum.Coupon,
      ObjectTypeEnum.Brand,
      ObjectTypeEnum.Leaflet,
      ObjectTypeEnum.Article,
      ObjectTypeEnum.Inspiration,
    ],
  },
  {
    id: 2,
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    selected: false,
    type: SortByEnum.EXPIRING,
    displayFor: [ObjectTypeEnum.Card, ObjectTypeEnum.Coupon, ObjectTypeEnum.Leaflet],
  },
  // TODO FOR FEATURE
  // {
  //   id: 3,
  //   name: 'Naj<PERSON><PERSON><PERSON><PERSON> rabat',
  //   selected: false,
  //   type: SortByEnum.BIGGEST_DISCOUNT,
  //   displayFor: [ObjectTypeEnum.Card, ObjectTypeEnum.Coupon],
  // },
  {
    id: 4,
    name: 'A-z',
    selected: false,
    type: SortByEnum.ALPHABETICAL,
    displayFor: [
      ObjectTypeEnum.Card,
      ObjectTypeEnum.Coupon,
      ObjectTypeEnum.Brand,
      ObjectTypeEnum.Leaflet,
      ObjectTypeEnum.Article,
      ObjectTypeEnum.Inspiration,
    ],
  },
];

export const shopCollection: ICategoryShop[] = [
  {
    id: 1,
    name: 'Wszystkie',
    type: ShopTypeEnum.ALL,
    selected: false,
    displayFor: [
      ObjectTypeEnum.Card,
      ObjectTypeEnum.Coupon,
      ObjectTypeEnum.Brand,
      ObjectTypeEnum.Leaflet,
      ObjectTypeEnum.Article,
      ObjectTypeEnum.Inspiration,
    ],
  },
  {
    id: 2,
    name: 'Stacjonarne',
    selected: false,
    type: ShopTypeEnum.STATIONARY,
    displayFor: [ObjectTypeEnum.Card, ObjectTypeEnum.Coupon, ObjectTypeEnum.Brand],
  },
  {
    id: 3,
    name: 'Internetowe',
    selected: false,
    type: ShopTypeEnum.ONLINE,
    displayFor: [ObjectTypeEnum.Card, ObjectTypeEnum.Coupon, ObjectTypeEnum.Brand],
  },
];
