import { RouteProp, useRoute } from '@react-navigation/native';

import { NotificationModal } from '~/components/Modals/NotificationModal/NotificationModal';
import { RouteEnum } from '~/routes/routes';
import { RootStackParamList } from '~/routes/types';

export const NotificationDetail = () => {
  const { params } = useRoute<RouteProp<RootStackParamList, RouteEnum.NOTIFICATION_DETAIL>>();

  return <NotificationModal {...params} />;
};
