import React from 'react';
import { FlatList } from 'react-native';

import * as Styled from './Notifications.styled';

import { Layout } from '~/components/Layout/Layout';
import { useNotificationView } from '~/events/hooks/useNotificationView';
import { useNavigate } from '~/hooks/useNavigate';
import { useNotifications } from '~/hooks/useNotifications';
import { Item } from '~/pages/Notifications/components/Item';

export const Notifications = () => {
  const { navigateBack } = useNavigate();
  const { loading, collection } = useNotifications();

  //EVENT - notification view
  useNotificationView();

  return (
    <Layout
      onGoBack={() => navigateBack()}
      padding={0}
      headerOptions={{
        label: 'Aktualności',
        leftIcon: true,
      }}>
      <Styled.Wrapper>
        {!loading ? (
          <FlatList
            data={collection.results}
            ItemSeparatorComponent={() => <Styled.Separator />}
            renderItem={({ item }) => {
              return <Item {...item} />;
            }}
          />
        ) : null}
      </Styled.Wrapper>
    </Layout>
  );
};
