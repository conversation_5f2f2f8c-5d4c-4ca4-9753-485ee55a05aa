import styled from '@emotion/native';

import { flexColStart } from '~/styles/flex.styled';
import theme from '~/theme';

export const Container = styled.TouchableOpacity`
  position: relative;
`;
export const Wrapper = styled.View`
  ${flexColStart};
  margin-left: 16px;
`;
export const LabelWrapper = styled.View`
  gap: 8px;
`;
export const MarkdownWrapper = styled.View`
  padding-left: 16px;
  padding-right: 16px;
`;
export const Line = styled.View`
  position: absolute;
  top: 0;
  left: -6px;
  width: 12px;
  background-color: ${theme.colors.purple[100]};
  border-top-right-radius: 100px;
  border-bottom-right-radius: 100px;
  height: 100%;
`;
