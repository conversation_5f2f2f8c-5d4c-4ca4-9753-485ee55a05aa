import Markdown, { MarkdownIt } from '@jonasmerlin/react-native-markdown-display';
import React from 'react';

import * as Styled from './Item.styled';

import { Label } from '~/components/Notification/Label/Label';
import { Typography } from '~/components/Typography/Typography';
import { useNavigate } from '~/hooks/useNavigate';
import { userShowNotification } from '~/redux/reducers/user/actions';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { markdownStyles } from '~/styles/markdown.styled';
import theme from '~/theme';
import { INotification } from '~/types/notification';

export const Item = (props: INotification) => {
  const dispatch = useAppDispatch();
  const { navigation } = useNavigate();
  const { title, type, text, created_at } = props;
  const { notification } = useAppSelector((state) => state.user);

  const handlePress = () => {
    dispatch(userShowNotification(created_at));
    navigation.navigate(RouteEnum.NOTIFICATION_DETAIL, { ...props });
  };

  return (
    <Styled.Container onPress={() => handlePress()}>
      {notification ? <>{!notification.includes(created_at) ? <Styled.Line /> : null}</> : null}
      <Styled.Wrapper>
        <Styled.MarkdownWrapper style={{ paddingHorizontal: 16 }}>
          <Styled.LabelWrapper>
            <Label type={type} />
            <Typography fontSize={16} fontWeight="600" color={theme.colors.black[100]}>
              {title}
            </Typography>
          </Styled.LabelWrapper>
          <Markdown
            style={markdownStyles}
            markdownit={MarkdownIt({ typographer: true }).disable(['image'])}>
            {`${text.slice(0, 100)} ...`}
          </Markdown>
        </Styled.MarkdownWrapper>
      </Styled.Wrapper>
    </Styled.Container>
  );
};
