import { TouchableOpacity } from 'react-native';
import FastImage from 'react-native-fast-image';

import * as Styled from './MyData.styled';

import { Layout } from '~/components/Layout/Layout';
import { Typography } from '~/components/Typography/Typography';
import { LAYOUT_PADDING } from '~/helpers/layoutPadding';
import { useNavigate } from '~/hooks/useNavigate';
import { MyDataItem } from '~/pages/Settings/components/MyDataItem/MyDataItem';
import { useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import theme from '~/theme';
import { assets } from '~/theme/assets';

export const MyData = () => {
  const { navigation, navigateBack } = useNavigate();
  const { detail, me } = useAppSelector((state) => state.user);

  return (
    <Layout
      onGoBack={() => navigateBack()}
      padding={LAYOUT_PADDING}
      headerOptions={{
        leftIcon: true,
        rightItem: (
          <TouchableOpacity onPress={() => navigation.navigate(RouteEnum.EDIT_PROFILE)}>
            <Typography fontSize={14} color={theme.colors.gray['200']} fontWeight="400">
              edytuj
            </Typography>
          </TouchableOpacity>
        ),
      }}>
      <Styled.Wrapper>
        <Styled.Data>
          <FastImage source={assets.images.my_data_png} style={{ width: 64, height: 64 }} />
          <Typography fontSize={22} fontWeight="600" color={theme.colors.black[300]}>
            {'Moje dane'}
          </Typography>
          <MyDataItem data={detail?.first_name} label={'Imię'} />
          {/* <MyDataItem data={detail?.last_name} label={'Nazwisko'} /> */}
          <MyDataItem data={me?.email.toLowerCase()} label={'e-mail'} />
          <MyDataItem data={detail?.phone_number} label={'nr telefonu'} />
        </Styled.Data>
        <Styled.Delete>
          <Typography
            style={{ textDecorationLine: 'underline' }}
            fontSize={12}
            fontWeight="600"
            color={theme.colors.black[300]}
            onPress={() => navigation.navigate(RouteEnum.ACCOUNT_DELETE)}>
            Usuń konto
          </Typography>
        </Styled.Delete>
      </Styled.Wrapper>
    </Layout>
  );
};
