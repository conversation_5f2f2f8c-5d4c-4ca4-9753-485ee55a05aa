import styled from '@emotion/native';

import { LAYOUT_PADDING } from '~/helpers/layoutPadding';
import { flexColCenter } from '~/styles/flex.styled';

export const Wrapper = styled.View`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 0 ${`${LAYOUT_PADDING}px`};
`;
export const ButtonWrapper = styled.View`
  width: 150px;
  margin: 15px auto;
`;

export const Status = styled.View`
  ${flexColCenter};
  gap: 16px;
  width: 300px;
`;
