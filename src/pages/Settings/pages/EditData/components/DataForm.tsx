import React from 'react';
import { FormProvider } from 'react-hook-form';

import { But<PERSON> } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { Input } from '~/components/Inputs/Input/Input';
import { useChangeUserData } from '~/hooks/useChangeUserData';
import * as Styled from '~/pages/Settings/pages/EditData/EditData.styled';
import { IDataForm } from '~/pages/Settings/pages/EditData/types';

export const DataForm = ({ setLoading, setShowEmailForm, setStatus }: IDataForm) => {
  const { form, onSubmit } = useChangeUserData({
    setLoading,
    setStatus,
    setShowEmailForm,
  });

  return (
    <FormProvider {...form}>
      <Input name="name" label="Imię" />
      {/* <Input name={'surname'} label={'<PERSON>z<PERSON><PERSON>'} /> */}
      <Input editable={false} name="email" label="e-mail" />
      <Input keyboardType="numeric" name="phone" label="nr telefonu" />
      <Styled.ButtonWrapper>
        <Button
          onPress={form.handleSubmit(onSubmit)}
          variant={ButtonVariant.FIFTH}
          size={ButtonSize.MEDIUM}
          borderRadius={24}>
          {`Zapisz`.toUpperCase()}
        </Button>
      </Styled.ButtonWrapper>
    </FormProvider>
  );
};
