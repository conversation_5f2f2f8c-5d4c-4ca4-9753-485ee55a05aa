import { FormProvider } from 'react-hook-form';

import * as Styled from '../EditData.styled';

import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { Input } from '~/components/Inputs/Input/Input';
import { useChangeUserEmail } from '~/hooks/useChangeUserEmail';
import { IEmailForm } from '~/pages/Settings/pages/EditData/types';
import { assets } from '~/theme/assets';

export const EmailForm = ({ email, setLoading, setStatus }: IEmailForm) => {
  const { form, onSubmit } = useChangeUserEmail({ setLoading, setStatus, email });

  return (
    <FormProvider {...form}>
      <Input
        name={'password'}
        label={'Hasło'}
        isPassword={true}
        icon={assets.icons.eye_password_icon_svg}
      />
      <Input name={'verification_code'} label={'Kod weryfikacyjny'} />
      <Styled.ButtonWrapper>
        <Button
          onPress={form.handleSubmit(onSubmit)}
          variant={ButtonVariant.FIFTH}
          size={ButtonSize.SMALL}
          borderRadius={24}>
          {`Zweryfikuj`.toUpperCase()}
        </Button>
      </Styled.ButtonWrapper>
    </FormProvider>
  );
};
