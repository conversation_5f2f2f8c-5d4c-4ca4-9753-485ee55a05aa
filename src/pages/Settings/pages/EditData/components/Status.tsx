import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { Typography } from '~/components/Typography/Typography';
import { useNavigate } from '~/hooks/useNavigate';
import * as Styled from '~/pages/Settings/pages/EditData/EditData.styled';
import theme from '~/theme';
import { IStatus } from '~/types/errors';

export const Status = ({ status }: { status: IStatus | undefined }) => {
  const { navigation } = useNavigate();

  return (
    <>
      {status ? (
        <Styled.Status>
          <Typography
            fontSize={18}
            color={status.status === 'error' ? theme.colors.red[100] : theme.colors.purple[100]}
            style={{ marginTop: 16, textAlign: 'center' }}>
            {status.message}
          </Typography>
          <Button
            onPress={() => navigation.goBack()}
            variant={ButtonVariant.FIFTH}
            size={ButtonSize.SMALL}
            borderRadius={24}>
            {` Powrót `.toUpperCase()}
          </Button>
        </Styled.Status>
      ) : null}
    </>
  );
};
