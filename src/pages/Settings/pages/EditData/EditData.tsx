import { useState } from 'react';

import * as Styled from './EditData.styled';

import { Loader } from '~/components/Loader/Loader';
import { ModalHeader } from '~/components/Modals/common/ModalHeader/ModalHeader';
import * as Popup from '~/components/Popup';
import { useNavigate } from '~/hooks/useNavigate';
import { DataForm } from '~/pages/Settings/pages/EditData/components/DataForm';
import { EmailForm } from '~/pages/Settings/pages/EditData/components/EmailForm';
import { Status } from '~/pages/Settings/pages/EditData/components/Status';
import { assets } from '~/theme/assets';
import { IStatus } from '~/types/errors';

export const EditData = () => {
  const { navigation } = useNavigate();
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState<IStatus | undefined>();
  const [showEmailForm, setShowEmailForm] = useState<string | undefined>();

  return (
    <Popup.DynamicHeight onClose={() => navigation.goBack()}>
      <Styled.Wrapper>
        <ModalHeader icon={assets.icons.edit_data_svg} title={'Edytuj dane'} />
        {loading ? (
          <Loader />
        ) : (
          <>
            {status?.status !== 'success' ? (
              <>
                {!showEmailForm ? (
                  <DataForm
                    setStatus={setStatus}
                    setLoading={setLoading}
                    setShowEmailForm={setShowEmailForm}
                  />
                ) : (
                  <EmailForm setStatus={setStatus} email={showEmailForm} setLoading={setLoading} />
                )}
              </>
            ) : null}
          </>
        )}
        <Status status={status} />
      </Styled.Wrapper>
    </Popup.DynamicHeight>
  );
};
