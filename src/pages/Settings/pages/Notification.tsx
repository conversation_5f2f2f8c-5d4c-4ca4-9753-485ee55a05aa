import { View } from 'react-native';

import { Layout } from '~/components/Layout/Layout';
import { Typography } from '~/components/Typography/Typography';
import { useNotificationChange } from '~/events/hooks/useNotificationChange';
import { useNavigate } from '~/hooks/useNavigate';
import { NotificationItem } from '~/pages/Settings/components/NotificationItem/NotificationItem';
import { notification } from '~/pages/Settings/data/notification';
import theme from '~/theme';

export const Notification = () => {
  const { navigateBack } = useNavigate();

  // EVENT - notification change
  useNotificationChange();

  return (
    <Layout
      onGoBack={() => navigateBack()}
      padding={0}
      headerOptions={{
        label: 'Ustawienia powiadomień',
        leftIcon: true,
      }}>
      <View>
        <Typography
          fontSize={14}
          fontWeight="400"
          color={theme.colors.black[300]}
          style={{ paddingHorizontal: 15, paddingVertical: 10 }}>
          {
            'Włącz powiadomienia, aby o<PERSON><PERSON><PERSON><PERSON><PERSON>\ninformacje o ważności okazji, rabatów czy gazetek.'
          }
        </Typography>
        {notification.map((item, index) => {
          return <NotificationItem {...item} key={index} />;
        })}
      </View>
    </Layout>
  );
};
