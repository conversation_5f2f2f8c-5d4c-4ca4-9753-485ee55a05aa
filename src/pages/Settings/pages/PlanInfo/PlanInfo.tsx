import Markdown, { MarkdownIt } from '@jonasmerlin/react-native-markdown-display';
import React, { useEffect } from 'react';

import * as Styled from './PlanInfo.styled';

import { Layout } from '~/components/Layout/Layout';
import { useNavigate } from '~/hooks/useNavigate';
import { getPlanInfo } from '~/redux/reducers/info/premiumPlan/thunks';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { markdownStyles } from '~/styles/markdown.styled';

export const PlanInfo = () => {
  const dispatch = useAppDispatch();
  const { navigateBack } = useNavigate();
  const { detail } = useAppSelector((state) => state.info);

  useEffect(() => {
    if (detail.value.length === 0) {
      dispatch(getPlanInfo());
    }
  }, []);

  return (
    <Layout
      onGoBack={() => navigateBack()}
      padding={0}
      headerOptions={{
        label: 'Plan Premium',
        leftIcon: true,
      }}>
      <Styled.Wrapper>
        <Markdown
          style={markdownStyles}
          markdownit={MarkdownIt({ typographer: true }).disable(['image'])}>
          {detail.value}
        </Markdown>
      </Styled.Wrapper>
    </Layout>
  );
};
