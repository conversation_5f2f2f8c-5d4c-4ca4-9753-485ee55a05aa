import styled from '@emotion/native';

import { LAYOUT_PADDING } from '~/helpers/layoutPadding';
import theme from '~/theme';

export const Container = styled.View`
  position: relative;
  overflow: hidden;
  width: 100%;
`;

export const Wrapper = styled.View`
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  border-width: 1px;
  border-color: ${theme.colors.purple[100]};
  background-color: ${theme.colors.purple[200]};
  padding: 8px ${`${LAYOUT_PADDING}px`};
  width: 100%;
`;

export const Corner = styled.View`
  position: absolute;
  width: 26px;
  height: 26px;
  top: -14px;
  left: -14px;
  border-width: 1px;
  transform: rotate(45deg);
  border-color: ${theme.colors.purple[100]};
  background-color: ${theme.colors.white[100]};
`;
