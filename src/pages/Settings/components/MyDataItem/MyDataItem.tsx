import { View } from 'react-native';

import { Typography } from '~/components/Typography/Typography';
import * as Styled from '~/pages/Settings/components/MyDataItem/MyDataItem.styled';
import { IMyDataItem } from '~/pages/Settings/types';
import theme from '~/theme';

export const MyDataItem = ({ label, data }: IMyDataItem) => {
  return (
    <Styled.Container>
      <Styled.Wrapper>
        <View>
          <Typography fontSize={12} fontWeight="300">
            {label ? label : '...'}
          </Typography>
          <Typography fontSize={16} fontWeight="400" color={theme.colors.black[100]}>
            {data ? data : '...'}
          </Typography>
        </View>
        <Styled.Corner />
      </Styled.Wrapper>
    </Styled.Container>
  );
};
