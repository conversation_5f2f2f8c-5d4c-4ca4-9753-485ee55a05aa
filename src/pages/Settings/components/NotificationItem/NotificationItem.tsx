import React, { useEffect, useState } from 'react';
import { View } from 'react-native';

import { Loader } from '~/components/Loader/Loader';
import { RadioSwitch } from '~/components/RadioSwitch/RadioSwitch';
import { Typography } from '~/components/Typography/Typography';
import { SERVER_URL } from '~/config.api';
import { configNotifyTypes } from '~/enums/notifyTypes';
import * as Styled from '~/pages/Settings/components/NotificationItem/NotificationItem.styled';
import { INotificationItem } from '~/pages/Settings/types';
import { userNotifySettingsChange } from '~/redux/reducers/user/actions';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import axios from '~/utils/axios.config';

export const NotificationItem = ({ title, type }: INotificationItem) => {
  const dispatch = useAppDispatch();
  const { notification_settings } = useAppSelector((state) => state.user);
  const [status, setStatus] = useState<boolean | undefined>(undefined);

  const handleChangeSettings = async () => {
    axios({
      method: 'PATCH',
      url: `${SERVER_URL}/user/me/profile/`,
      headers: {
        Accept: 'application/json',
      },
      data: {
        [configNotifyTypes[type]]: !status,
      },
    }).then(() => {
      dispatch(userNotifySettingsChange({ type, status: !status }));
    });
  };

  const handleGetSettings = () => {
    if (notification_settings) {
      const settings = notification_settings?.find((item) => item.type === type);
      setStatus(settings?.status || false);
    }
  };

  useEffect(() => {
    handleGetSettings();
  }, []);

  useEffect(() => {
    handleGetSettings();
  }, [notification_settings]);

  if (status === undefined) return <Loader />;
  else
    return (
      <Styled.Wrapper>
        <Styled.LeftWrapper>
          <Typography fontSize={16} fontWeight="600">
            {title}
          </Typography>
        </Styled.LeftWrapper>
        <View style={{ flex: 0 }}>
          <RadioSwitch checked={status} onChange={() => handleChangeSettings()} />
        </View>
      </Styled.Wrapper>
    );
};
