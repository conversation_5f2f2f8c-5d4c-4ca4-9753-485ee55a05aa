import { View } from 'react-native';

import * as Styled from './Section.styled';

import { Typography } from '~/components/Typography/Typography';
import { ISection } from '~/pages/Settings/types';
import theme from '~/theme';

export const Section = ({ label, children }: ISection) => {
  return (
    <Styled.Wrapper>
      <View
        style={{
          paddingBottom: 20,
          borderBottomColor: theme.colors.gray[300],
          borderBottomWidth: 1,
        }}>
        <Typography fontSize={14} fontWeight="600">
          {label}
        </Typography>
      </View>

      {children}
    </Styled.Wrapper>
  );
};
