import * as Styled from './SettingsItem.styled';

import { Icon } from '~/components/Icon/Icon';
import { Typography } from '~/components/Typography/Typography';
import { useNavigate } from '~/hooks/useNavigate';
import { ISettingsItem } from '~/pages/Settings/types';
import { assets } from '~/theme/assets';

export const SettingsItem = ({ label, path, routeOptions }: ISettingsItem) => {
  const { navigation } = useNavigate();

  const handleAction = () => {
    if (path) {
      navigation.navigate(path, routeOptions);
    }
  };

  return (
    <Styled.Wrapper onPress={() => handleAction()}>
      <Typography fontSize={14} fontWeight="400">
        {`${label}`}
      </Typography>
      <Icon iconSVG={assets.icons.arrow_right_svg} width={24} height={24} />
    </Styled.Wrapper>
  );
};
