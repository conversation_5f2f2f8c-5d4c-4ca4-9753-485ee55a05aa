import styled from '@emotion/native';

import { LAYOUT_PADDING } from '~/helpers/layoutPadding';
import theme from '~/theme';

export const Wrapper = styled.TouchableOpacity`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  border-color: ${theme.colors.gray[300]};
  border-bottom-width: 1px;
  padding: 20px ${`${LAYOUT_PADDING}px`} 20px ${`${LAYOUT_PADDING}px`};
  width: 100%;
`;
