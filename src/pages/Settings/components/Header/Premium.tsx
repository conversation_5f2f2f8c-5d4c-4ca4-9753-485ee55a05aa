import * as Styled from './Common.styled';

import { Typography } from '~/components/Typography/Typography';
import { IPremium } from '~/pages/Settings/types';
import theme from '~/theme';

export const Premium = ({ plan, date }: IPremium) => {
  return (
    <Styled.Title>
      <Styled.TitleLeft>
        <Typography fontSize={18} fontWeight="600">
          {`Plan ${plan.toUpperCase()}`}
        </Typography>
        {date ? (
          <Typography fontSize={12} fontWeight="200" color={theme.colors.black[300]}>
            {`Ważny do: ${date.toUpperCase()}`}
          </Typography>
        ) : null}
      </Styled.TitleLeft>
    </Styled.Title>
  );
};
