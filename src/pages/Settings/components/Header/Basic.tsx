import * as Styled from './Common.styled';

import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { Typography } from '~/components/Typography/Typography';
import { useNavigate } from '~/hooks/useNavigate';
import { RouteEnum } from '~/routes/routes';
import theme from '~/theme';

export const Basic = () => {
  const { navigation } = useNavigate();

  return (
    <Styled.Title>
      <Styled.TitleLeft>
        <Typography fontSize={18} fontWeight="600">
          {`Plan\n${`podstawowy`.toUpperCase()}`}
        </Typography>
      </Styled.TitleLeft>
      <Styled.TitleRight>
        <Button
          onPress={() => navigation.navigate(RouteEnum.PREMIUM_MODAL)}
          variant={ButtonVariant.FIFTH}
          size={ButtonSize.XSMALL}
          borderRadius={32}
          typographyStyles={{ fontSize: 12, fontWeight: '600', color: theme.colors.white[100] }}>
          {'Aktywuj premium'.toUpperCase()}
        </Button>
      </Styled.TitleRight>
    </Styled.Title>
  );
};
