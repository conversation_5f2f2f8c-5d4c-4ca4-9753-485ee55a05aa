import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { Typography } from '~/components/Typography/Typography';
import { useNavigate } from '~/hooks/useNavigate';
import * as Styled from '~/pages/Settings/components/Header/Common.styled';
import { ILocalization } from '~/pages/Settings/types';
import { RouteEnum } from '~/routes/routes';
import theme from '~/theme';

export const Localization = ({ locale }: ILocalization) => {
  const { navigation } = useNavigate();

  return (
    <Styled.Title>
      <Styled.TitleLeft>
        <Typography fontSize={14} fontWeight="400">
          {`Moja lokalizacja:`}
        </Typography>
        <Typography fontSize={14} fontWeight="600">
          {`${locale}`}
        </Typography>
      </Styled.TitleLeft>
      <Styled.TitleRight>
        <Styled.ChangeButtoWrapper>
          <Button
            onPress={() => navigation.navigate(RouteEnum.LOCALIZATION)}
            variant={ButtonVariant.FOURTH}
            size={ButtonSize.XSMALL}
            borderRadius={32}
            typographyStyles={{ fontSize: 12, fontWeight: '600', color: theme.colors.purple[100] }}>
            {'Zmień'.toUpperCase()}
          </Button>
        </Styled.ChangeButtoWrapper>
      </Styled.TitleRight>
    </Styled.Title>
  );
};
