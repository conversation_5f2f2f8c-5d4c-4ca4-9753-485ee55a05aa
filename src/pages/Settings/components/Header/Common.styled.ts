import styled from '@emotion/native';

export const Title = styled.View`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
`;
export const TitleLeft = styled.View`
  display: flex;
  flex-direction: column;
  flex: 1;
`;
export const TitleRight = styled.View`
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  flex: 1;
`;
export const ChangeButtoWrapper = styled.View`
  width: 120px;
`;
export const Footer = styled.View``;
