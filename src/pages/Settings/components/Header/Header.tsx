import { StyleSheet, View } from 'react-native';

import * as Styled from './Header.styled';

import { CornerBox } from '~/components/CornerBox/CornerBox';
import { Typography } from '~/components/Typography/Typography';
import { Basic } from '~/pages/Settings/components/Header/Basic';
import { Localization } from '~/pages/Settings/components/Header/Localization';
import { Premium } from '~/pages/Settings/components/Header/Premium';
import { useAppSelector } from '~/redux/store';
import theme from '~/theme';

export const Header = () => {
  const { premium, detail } = useAppSelector((state) => state.user);

  return (
    <Styled.Wrapper>
      {/*ALL USERS / TWO VIEWS*/}
      <CornerBox
        backgroundColor={theme.colors.white[100]}
        cornerColor={theme.colors.purple[200]}
        cornerSize={32}>
        <View style={[styles.sectionWrapper]}>
          {premium ? <Premium plan={'Premium'} date={premium.valid_to} /> : <Basic />}
        </View>
      </CornerBox>

      {/*ONLY PREMIUM*/}

      {premium ? (
        <>
          <CornerBox
            backgroundColor={theme.colors.white[100]}
            cornerColor={theme.colors.purple[200]}
            cornerSize={32}>
            <View style={[styles.sectionWrapper, styles.rowBetween]}>
              <Typography fontSize={14} fontWeight="400">
                {`Mój kod:`}
              </Typography>
              <Typography
                fontSize={14}
                fontWeight="600"
                color={theme.colors.purple[100]}
                style={{ paddingRight: 4 }}>
                {premium.code}
              </Typography>
            </View>
          </CornerBox>
        </>
      ) : null}

      {/*ALL USERS*/}
      <CornerBox
        backgroundColor={theme.colors.white[100]}
        cornerColor={theme.colors.purple[200]}
        cornerSize={32}>
        <View style={[styles.sectionWrapper]}>
          <Localization locale={detail?.city_name ? detail.city_name : 'Cała Polska'} />
        </View>
      </CornerBox>
    </Styled.Wrapper>
  );
};

const styles = StyleSheet.create({
  sectionWrapper: {
    flex: 1,
    padding: 16,
    gap: 16,
  },
  center: {
    alignItems: 'center',
  },
  rowBetween: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
});
