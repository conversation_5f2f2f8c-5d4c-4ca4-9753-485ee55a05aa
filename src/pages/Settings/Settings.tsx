import { FC, ReactElement } from 'react';
import { TouchableOpacity } from 'react-native';
import AppVersion from 'react-native-version-check';

import * as Styled from './Settings.styled';

import { Layout } from '~/components/Layout/Layout';
import { Typography } from '~/components/Typography/Typography';
import { useUserLoginStatus } from '~/events/hooks/useUserLoginStatus';
import { googleLogout } from '~/helpers/socialLogout';
import { useNavigate } from '~/hooks/useNavigate';
import { Header } from '~/pages/Settings/components/Header/Header';
import { Section } from '~/pages/Settings/components/Section/Section';
import { SettingsItem } from '~/pages/Settings/components/SettingsItem/SettingsItem';
import { options } from '~/pages/Settings/data/options';
import { ISettingsItem } from '~/pages/Settings/types';
import { contentApi } from '~/redux/api/contentApi';
import { userLogout } from '~/redux/reducers/user/actions';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import theme from '~/theme';

export const Settings: FC = (): ReactElement => {
  const dispatch = useAppDispatch();
  const { token, me } = useAppSelector((state) => state.user);

  const { navigation, navigateBack } = useNavigate();

  const handleLogout = () => {
    dispatch(userLogout());
    dispatch(contentApi.util.resetApiState());
    navigation.navigate(RouteEnum.LOGIN);
    googleLogout();
  };
  //----EVENTS----
  const _fnLoginStatus = useUserLoginStatus();
  //--X--EVENTS--X--

  return (
    <Layout
      onGoBack={() => navigateBack()}
      padding={0}
      headerOptions={{
        label: 'Moje konto',
        leftIcon: true,
        rightItem: (
          <TouchableOpacity
            onPress={async () => {
              await _fnLoginStatus(token?.access, true);
              await handleLogout();
            }}>
            <Typography fontSize={14} color={theme.colors.gray[200]}>
              Wyloguj
            </Typography>
          </TouchableOpacity>
        ),
      }}>
      <Styled.Scroll>
        <Styled.Wrapper>
          <Header />
          {Object.keys(options).map((section: string, key: number) => {
            return (
              <Section label={options[section as keyof typeof options].label} key={key}>
                <>
                  {options[section as keyof typeof options].items.map(
                    (item: ISettingsItem, index: number) => {
                      if (
                        item.path === RouteEnum.PASSWORD_CHANGE &&
                        me?.auth_provider === 'firebase'
                      )
                        return null;
                      return (
                        <SettingsItem
                          label={item.label}
                          path={item.path}
                          routeOptions={item.routeOptions}
                          key={index}
                        />
                      );
                    },
                  )}
                </>
              </Section>
            );
          })}
          <Typography fontSize={14} color={theme.colors.gray[400]} style={{ marginTop: 20 }}>
            {`Wersja aplikacji: v${AppVersion.getCurrentVersion()}`}
          </Typography>
        </Styled.Wrapper>
      </Styled.Scroll>
    </Layout>
  );
};
