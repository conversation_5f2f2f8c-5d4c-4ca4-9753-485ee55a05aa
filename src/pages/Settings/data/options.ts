import { ISettings } from '~/pages/Settings/types';
import { RouteEnum } from '~/routes/routes';

export const options: ISettings = {
  settings: {
    label: '<PERSON>je ustawienia',
    items: [
      {
        label: '<PERSON>je dane',
        path: RouteEnum.MY_DATA,
      },
      {
        label: '<PERSON>mie<PERSON> hasło',
        path: RouteEnum.PASSWORD_CHANGE,
      },
      {
        label: 'Ustawienia powiadomień',
        path: RouteEnum.NOTIFICATION_SETTING,
      },
    ],
  },
  myProfit: {
    label: 'HomeProfit',
    items: [
      {
        label: 'Kontakt',
        path: RouteEnum.CONTACT,
      },
      {
        label: 'Regulamin',
        path: RouteEnum.POLICY,
        routeOptions: {
          type: 'terms',
        },
      },
      {
        label: '<PERSON>ity<PERSON> Prywat<PERSON>ści',
        path: RouteEnum.POLICY,
        routeOptions: {
          type: 'policy',
        },
      },
    ],
  },
};
