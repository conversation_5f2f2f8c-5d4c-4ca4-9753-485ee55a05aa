import { ReactElement } from 'react';

import { NotifyTypes } from '~/enums/notifyTypes';
import { RouteEnum } from '~/routes/routes';

export interface IPremium {
  plan: string;
  date?: string | null;
}

export interface ILocalization {
  locale: string;
}

export interface ISettings {
  settings: ISettingsSection;
  myProfit: ISettingsSection;
}

export interface ISettingsSection {
  label: string;
  items: Array<ISettingsItem>;
}

export interface ISettingsItem {
  label: string;
  path:
    | RouteEnum.MY_DATA
    | RouteEnum.POLICY
    | RouteEnum.PROFILE
    | RouteEnum.PASSWORD_CHANGE
    | RouteEnum.ACCOUNT_DELETE
    | RouteEnum.CONTACT
    | RouteEnum.NOTIFICATION_SETTING;
  routeOptions?: any;
}

export interface IMyDataItem {
  label?: string;
  data?: string;
}

export interface INotificationItem {
  title: string;
  description: string;
  type: NotifyTypes;
}

export interface ISection {
  label: string;
  children: ReactElement;
}
