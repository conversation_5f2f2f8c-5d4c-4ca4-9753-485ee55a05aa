import styled from '@emotion/native';

import { LAYOUT_PADDING } from '~/helpers/layoutPadding';
import theme from '~/theme';

export const Scroll = styled.ScrollView`
  background-color: ${theme.colors.purple[200]};
`;

export const Wrapper = styled.View`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  padding: ${`${LAYOUT_PADDING}px`} ${`${LAYOUT_PADDING}px`} 80px;
  gap: 16px;
`;
