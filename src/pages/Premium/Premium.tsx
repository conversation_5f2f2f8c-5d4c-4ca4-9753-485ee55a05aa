import Markdown, { MarkdownIt } from '@jonasmerlin/react-native-markdown-display';
import React, { FC, ReactElement } from 'react';

import * as Styled from './Premium.styled';

import { Button } from '~/components/Button/Button';
import { ButtonVariant } from '~/components/Button/enum';
import * as Error from '~/components/Error';
import { Icon } from '~/components/Icon/Icon';
import { ImageComponent } from '~/components/ImageComponent/ImageComponent';
import { Layout } from '~/components/Layout/Layout';
import { Typography } from '~/components/Typography/Typography';
import { useNavigate } from '~/hooks/useNavigate';
import { usePzInfo } from '~/hooks/usePzInfo';
import { useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { markdownStyles } from '~/styles/markdown.styled';
import theme from '~/theme';
import { assets } from '~/theme/assets';

export const Premium: FC = (): ReactElement => {
  const { result, loading, isConnected } = usePzInfo();
  const { premium, token } = useAppSelector((state) => state.user);
  const { navigation, navigateBack } = useNavigate();
  return (
    <Layout
      onGoBack={() => navigateBack()}
      isLoading={loading}
      headerOptions={{
        label: 'sTrEfA pReMiUm',
        leftIcon: true,
        rightItem: (
          <>
            <Icon
              iconSVG={assets.icons.start_svg}
              width={38}
              height={38}
              fill={theme.colors.purple[100]}
              isStroke={false}
              isFill={true}
            />
          </>
        ),
      }}>
      {isConnected ? (
        <Styled.Container>
          <Styled.Content>
            {result?.['1_description_image'] && (
              <Styled.ImageContainer>
                <ImageComponent uri={result?.['1_description_image']} />
              </Styled.ImageContainer>
            )}
            {result?.title && (
              <Styled.Title>
                <Typography fontSize={24} fontWeight="600" color={theme.colors.purple['100']}>
                  {result.title}
                </Typography>
              </Styled.Title>
            )}
            {result?.['1_description'] && (
              <Markdown
                style={markdownStyles}
                markdownit={MarkdownIt({ typographer: true }).disable(['image'])}>
                {result?.['1_description']}
              </Markdown>
            )}
            {result?.['2_description_image'] && (
              <Styled.ImageContainer>
                <ImageComponent uri={result?.['2_description_image']} />
              </Styled.ImageContainer>
            )}
            {result?.['2_description'] && (
              <Markdown
                style={markdownStyles}
                markdownit={MarkdownIt({ typographer: true }).disable(['image'])}>
                {result?.['2_description']}
              </Markdown>
            )}
            {premium === null && (
              <Styled.ButtonContainer>
                <Button
                  variant={ButtonVariant.FIFTH}
                  onPress={() => {
                    if (token?.access) {
                      navigation.navigate(RouteEnum.PREMIUM_MODAL);
                    } else {
                      navigation.navigate(RouteEnum.AUTH_MODAL);
                    }
                  }}>
                  {`aktywuj premium`.toUpperCase()}
                </Button>
              </Styled.ButtonContainer>
            )}
          </Styled.Content>
        </Styled.Container>
      ) : (
        <Error.NetworkError />
      )}
    </Layout>
  );
};
