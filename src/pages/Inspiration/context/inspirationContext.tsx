import { createContext, ReactNode } from 'react';

import { IInspiration, IInspirationDetails } from '~/types/inspirations';

export interface IInspirationContext extends IInspirationDetails {
  counters: {
    inspirations: number;
  };
  similar: {
    inspirations?: IInspiration[] | null;
  };
  actions: {
    isModalOpen: boolean;
    setModalOpen: (value: boolean) => void;
  };
}

export interface IInspirationContextProvider {
  providerValues: IInspirationContext;
  children: ReactNode;
}

export const defaultContext: IInspirationContext = {
  id: 0,
  brand: {
    id: 0,
    name: '',
    logo: '',
    is_favorite: false,
  },
  first_image: '',
  categories: [],
  is_favorite: false,
  images: [],
  title: '',
  subtitle: '',
  description: '',
  products: [],
  counters: {
    inspirations: 0,
  },
  similar: {
    inspirations: null,
  },
  actions: {
    isModalOpen: false,
    setModalOpen: () => {},
  },
};

export const InspirationContext = createContext<IInspirationContext>(defaultContext);

export const InspirationContextProvider = ({
  providerValues,
  children,
}: IInspirationContextProvider) => {
  return (
    <InspirationContext.Provider value={providerValues}>{children}</InspirationContext.Provider>
  );
};
