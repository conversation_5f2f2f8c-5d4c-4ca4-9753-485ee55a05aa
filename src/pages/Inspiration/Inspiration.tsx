import { useNetInfo } from '@react-native-community/netinfo';
import { useFocusEffect } from '@react-navigation/native';
import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { RefreshControl, View } from 'react-native';

import { EmptyData } from '~/components/EmptyData/EmptyData';
import { FilterLayout } from '~/components/Filters/FilterLayout';
import { InspirationImage } from '~/components/Inspiration/components/InspirationImage/InspirationImage';
import { Inspiration as InspirationItem } from '~/components/Inspiration/Inspiration';
import { Layout } from '~/components/Layout/Layout';
import { List } from '~/components/List/List';
import { FilterContext } from '~/context/filterContext';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useDisplayContentType, useFilterCategoriesChange } from '~/events/hooks';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useInspirations } from '~/hooks/useInspirations';
import { useNavigate } from '~/hooks/useNavigate';
import { usePagination } from '~/hooks/usePagination';
import { useLazyGetCategoriesQuery } from '~/redux/api/categories';
import {
  useGetInspirationsQuery,
  useGetInspirationsWithPaginationQuery,
} from '~/redux/api/inspirations';
import { useAppSelector } from '~/redux/store';
import * as Styled from '~/styles/common.styled';
import theme from '~/theme';
import { IInspiration } from '~/types/inspirations';
import { ParamsOptions } from '~/types/shared';
import { Search } from '~/components/Search';
import { useResetSearchWithBack } from '~/hooks/useResetSearchWithBack';

type InspirationState = {
  count: number;
  previous: string | null;
  next: string | null;
  results: IInspiration[][];
};

export const Inspiration = () => {
  const { navigateToInspirationCollection, navigateToInspirationDetails } = useNavigate();
  const _filterContext = useContext(FilterContext);
  const { handleBackWithReset } = useResetSearchWithBack();
  const { isConnected } = useNetInfo();
  const { city } = useAppSelector((state) => state.user.detail);
  const { page, setPage, onEndReached } = usePagination();

  let queryParams: ParamsOptions = useMemo(() => {
    const params = _filterContext.buildFilterParams();

    return {
      ...params,
      ...(page > 1 ? { page: page } : {}),
      city_id: city,
    };
  }, [
    _filterContext.selectedCategoryFilter,
    _filterContext.selectedShopFilter,
    _filterContext.selectedSortFilter,
    _filterContext.selectedSimilarCategoryFilter,
    _filterContext.searchId,
    _filterContext.searchString,
    city,
    page,
  ]);
  /**
   * RTK Query:
   */
  const { chunkArray } = useInspirations();
  const { data, isFetching, error, refetch } = useGetInspirationsWithPaginationQuery(queryParams);
  const { data: mayInterestYouData } = useGetInspirationsQuery({ city_id: city });
  const [trigger] = useLazyGetCategoriesQuery();
  const { postFavorite } = useFavoritesQuery();

  const [chunkedCollectionResults, setChunkedArrayCollectionResults] = useState<InspirationState>();

  useEffect(() => {
    if (data) {
      setChunkedArrayCollectionResults({
        ...data,
        results: chunkArray([...data?.results!], 4),
      });
    }
  }, [data]);

  /*
   * Fetch categories for specific object type (Card, Coupon etc.)
   */
  useEffect(() => {
    trigger({ content_type: ObjectTypeEnum.Inspiration }).then((res) => {
      _filterContext.setSelectedCategoryFilter(res.data);
    });
  }, []);

  /**
   * Events:
   */
  useDisplayContentType({
    type: ObjectTypeEnum.Inspiration,
    collection: data,
  });
  useFilterCategoriesChange(_filterContext.buildFilterParams().category_ids || []);

  /**
   * Reset page on focus (it triggers reset cache of rtk query and fetch new data from server)
   */
  useFocusEffect(
    useCallback(() => {
      setPage(1);
      return () => {
        setPage(1);
      };
    }, [
      _filterContext.selectedCategoryFilter,
      _filterContext.selectedShopFilter,
      _filterContext.selectedSortFilter,
      _filterContext.selectedSimilarCategoryFilter,
      _filterContext.searchId,
      _filterContext.searchString,
      city,
    ]),
  );

  useEffect(() => {
    refetch();
  }, [isConnected]);

  return (
    <Layout
      error={error}
      padding={0}
      onGoBack={handleBackWithReset}
      headerOptions={{
        resultCounter: data?.count,
        label: 'INSPIRACJE',
        leftIcon: true,
        rightItem: <Search to={ObjectTypeEnum.Inspiration} />,
        labelStyle: {
          fontSize: 16,
          fontWeight: '600',
          color: theme.colors.black['100'],
        },
      }}>
      <Styled.ListWrapper>
        <Styled.FilterWrapper>
          <FilterLayout
            removeSingleFilter={(id, type) => {
              setPage(1);
              _filterContext.removeSingleFilter(id, type);
            }}
            removeAllFilters={() => {
              setPage(1);
              _filterContext.clear();
            }}
            tiles={_filterContext.tiles}
            screenType={ObjectTypeEnum.Inspiration}
            setColumnsSwitch={() => {}}
            columnsSwitch={1}
            desc="Zainspiruj się!"
            isFilter={true}
            isSwitch={false}
          />
        </Styled.FilterWrapper>

        <Styled.ContentWhite>
          {isFetching || (data && data.results.length > 0) ? (
            <List
              onEndReached={
                data?.results && !isFetching
                  ? () => {
                      onEndReached(isFetching, data?.next);
                    }
                  : () => {}
              }
              refreshControl={
                <RefreshControl
                  refreshing={isFetching}
                  onRefresh={() => {
                    setPage(1);
                  }}
                />
              }
              loading={isFetching}
              data={chunkedCollectionResults?.results}
              columnsSwitch={1}
              renderItem={({ item, index }) => {
                return (
                  <View style={{ width: '100%' }}>
                    <InspirationItem
                      onFavoritePress={(objectId, isFavorite, categories, tags) =>
                        postFavorite({
                          isFavorite,
                          params: {
                            payload: {
                              object_id: objectId,
                              object_type: ObjectTypeEnum.Inspiration,
                            },
                            type: 'Inspiration',
                          },
                          event: {
                            object_type: ObjectTypeEnum.Inspiration,
                            categories_id_list: categories || [],
                            tags_id_list: tags || [],
                          },
                        })
                      }
                      key={index}
                      data={item}
                      onPress={(id, is_rec) => navigateToInspirationDetails(id, is_rec)}
                    />
                  </View>
                );
              }}
            />
          ) : null}
          {/* EMPTY DATA */}
          {(!isFetching && !data) || data?.results.length === 0 ? (
            <EmptyData
              ItemSeparatorComponent={() => <View style={{ width: 12 }} />}
              data={mayInterestYouData?.results.slice(0, 10)}
              counter={mayInterestYouData?.count}
              onSectionHeaderPress={() => {
                _filterContext.clear();
                navigateToInspirationCollection();
              }}
              renderItem={({ item, index }) => (
                <View style={{ width: 170, height: 170 }}>
                  <InspirationImage
                    onFavoritePress={(objectId, isFavorite, categories, tags) =>
                      postFavorite({
                        isFavorite,
                        params: {
                          payload: {
                            object_id: objectId,
                            object_type: ObjectTypeEnum.Inspiration,
                          },
                          type: 'Inspiration',
                        },
                        event: {
                          object_type: ObjectTypeEnum.Inspiration,
                          categories_id_list: categories || [],
                          tags_id_list: tags || [],
                        },
                      })
                    }
                    key={index}
                    data={item}
                    onPress={() => navigateToInspirationDetails(item.id, item.is_rec)}
                  />
                </View>
              )}
            />
          ) : null}
        </Styled.ContentWhite>
      </Styled.ListWrapper>
    </Layout>
  );
};
