import styled from '@emotion/native';

import theme from '~/theme';

export const Wrapper = styled.View`
  flex: 1;

  flex-direction: column;
  gap: 15px;
`;

export const ScrollWrapper = styled.ScrollView``;

export const ContentWrapper = styled.View`
  gap: 30px;
  padding: 16px 16px 0 16px;
`;

export const Hr = styled.View`
  margin: 10px 0 16px 0;
  height: 1px;
  width: 100%;
  background-color: ${theme.colors.gray[300]};
`;
