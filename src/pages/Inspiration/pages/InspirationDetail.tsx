import { RouteProp, useRoute } from '@react-navigation/native';
import { RefreshControl, View, ScrollView, Text } from 'react-native';
import { useEffect, useMemo, useRef, useState } from 'react';

import * as Styled from './InspirationDetail.styled';

import { Layout } from '~/components/Layout/Layout';
import { Lightbox } from '~/components/Lightbox/Lightbox';
import { Loader } from '~/components/Loader/Loader';
import * as Lofi from '~/components/Loader/Lofi';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useDisplayCard } from '~/events/hooks/useDisplayCard';
import { useInteraction } from '~/events/hooks/useInteraction';
import { useNavigate } from '~/hooks/useNavigate';
import { InspirationDesc } from '~/pages/Inspiration/components/InspirationDesc/InspirationDesc';
import { InspirationGallery } from '~/pages/Inspiration/components/InspirationGallery/InspirationGallery';
import { InspirationProductSlider } from '~/pages/Inspiration/components/InspirationProductSlider/InspirationProductSlider';
import { InspirationSimilarSlider } from '~/pages/Inspiration/components/InspirationSimilar/InspirationSimilarSlider';
import { InspirationContextProvider } from '~/pages/Inspiration/context/inspirationContext';
import {
  useGetInspirationsDetailsQuery,
  useGetSimilarInspirationsQuery,
  useLazyGetInspirationsDetailsQuery,
} from '~/redux/api/inspirations';
import { RouteEnum } from '~/routes/routes';
import { RootStackParamList } from '~/routes/types';
import theme from '~/theme';
import { ParamsOptions } from '~/types/shared';

const InspirationDetailLofi = () => {
  return (
    <Lofi.Details>
      <Lofi.Container>
        <View style={{ position: 'absolute', right: 32, bottom: -32 }}>
          <Lofi.Circle border />
        </View>
      </Lofi.Container>
      <View style={{ gap: 16, paddingTop: 32, alignItems: 'flex-end' }}>
        <Lofi.Text width={'60%'} />
        <Lofi.Text width={'80%'} />
        <Lofi.Text width={'100%'} />
      </View>
      <View style={{ paddingTop: 16, gap: 16 }}>
        <Lofi.Container height={32} rx={32} />
        <Lofi.Container height={32} rx={32} />
        <Lofi.Container height={32} rx={32} />
      </View>
    </Lofi.Details>
  );
};

export const InspirationDetail = () => {
  const { navigateBack, navigation } = useNavigate();
  const scrollViewRef = useRef<ScrollView>(null);
  const [isModalOpen, setModalOpen] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);
  const { handleDisplayCard } = useDisplayCard({
    type: ObjectTypeEnum.Inspiration,
  });

  const { params: detailParams } =
    useRoute<RouteProp<RootStackParamList, RouteEnum.INSPIRATION_DETAIL>>();
  const {
    data: detail,
    isLoading: isLoadingDetail,
    error: detailError,
  } = useGetInspirationsDetailsQuery({
    id: detailParams.id,
  });
  const [getInspirationsDetails, { isLoading }] = useLazyGetInspirationsDetailsQuery();

  let queryParams: ParamsOptions = useMemo(() => {
    return {
      category_ids: detail?.categories,
    };
  }, [detail]);

  //SIMILARS
  const { data: similarInspirations, isLoading: isLoadingSimilarInspirations } =
    useGetSimilarInspirationsQuery(queryParams);

  // EVENTS - DISPLAY CARD
  useEffect(() => {
    if (detailParams.id === detail?.id) {
      handleDisplayCard({
        detail: detail,
        is_rec: detailParams.is_rec ? detailParams.is_rec : false,
      });
    }
    scrollViewRef.current?.scrollTo({ y: 0, animated: true });
  }, [detail]);

  useEffect(() => {
    if (isModalOpen) {
      navigation.setOptions({ orientation: 'all' });
    } else {
      navigation.setOptions({ orientation: 'portrait' });
    }
  }, [isModalOpen]);

  // EVENTS - INTERACTION
  useInteraction({
    object_type: ObjectTypeEnum.Inspiration,
    object_id: detailParams.id,
  });

  const similar = similarInspirations?.results.filter((item) => item.id !== detail?.id);

  if (isLoadingDetail) return <InspirationDetailLofi />;
  return (
    <Layout
      error={detailError}
      onGoBack={() => navigateBack()}
      padding={0}
      headerShown={isModalOpen ? false : true}
      footerShown={isModalOpen ? false : true}
      headerOptions={{
        label: 'inspiracje',
        leftIcon: true,
        labelStyle: {
          fontSize: 16,
          fontWeight: '600',
          color: theme.colors.black['100'],
        },
      }}>
      <InspirationContextProvider
        providerValues={{
          ...detail!,
          counters: {
            inspirations: similarInspirations?.count ?? 0,
          },
          similar: {
            inspirations: similar,
          },
          actions: {
            isModalOpen,
            setModalOpen,
          },
        }}>
        <Styled.ScrollWrapper
          ref={scrollViewRef}
          refreshControl={
            <RefreshControl
              refreshing={isLoading}
              onRefresh={() => getInspirationsDetails({ id: detailParams.id })}
            />
          }>
          <InspirationGallery setActiveSlide={setActiveIndex} />
          <Styled.ContentWrapper>
            <InspirationDesc />
            <Text>{activeIndex}</Text>
          </Styled.ContentWrapper>
          {isLoadingDetail ? (
            <Loader />
          ) : detail && detail.products.length > 0 ? (
            <InspirationProductSlider />
          ) : null}
          {isLoadingSimilarInspirations ? (
            <Loader />
          ) : similar && similar.length > 0 ? (
            <InspirationSimilarSlider />
          ) : null}
        </Styled.ScrollWrapper>
      </InspirationContextProvider>
      <>
        {isModalOpen && detail ? (
          <Lightbox
            index={activeIndex}
            onClose={() => setModalOpen(false)}
            uri={detail.images.map((item) => item.image)}
          />
        ) : null}
      </>
    </Layout>
  );
};
