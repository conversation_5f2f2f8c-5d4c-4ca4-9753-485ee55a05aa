import { useContext } from 'react';
import { View } from 'react-native';

import { InspirationImage } from '~/components/Inspiration/components/InspirationImage/InspirationImage';
import { Section } from '~/components/Section/Section';
import { ContentSlider } from '~/components/Slider/Slider';
import { FilterContext } from '~/context/filterContext';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useNavigate } from '~/hooks/useNavigate';
import { InspirationContext } from '~/pages/Inspiration/context/inspirationContext';
import { RouteEnum } from '~/routes/routes';
import theme from '~/theme';

export const InspirationSimilarSlider = () => {
  const context = useContext(InspirationContext);
  const _filterContext = useContext(FilterContext);
  const { navigateToInspirationDetails, navigateToInspirationCollection } = useNavigate();
  const { postFavorite } = useFavoritesQuery();

  return (
    <Section
      bgColor={theme.colors.white['100']}
      style={{ paddingTop: 20, paddingBottom: 40 }}
      showBottomLine={false}
      title={'Podobne inspiracje'}
      counter={`[${context.counters.inspirations}]`}
      onSectionHeaderPress={() => {
        _filterContext.clear();
        _filterContext.handleSimilarCategoryCollection(context.categories);
        navigateToInspirationCollection({
          params: {
            category_ids: context.categories,
          },
          prevScreen: RouteEnum.INSPIRATION_DETAIL,
        });
      }}>
      <ContentSlider
        ItemSeparatorComponent={() => <View style={{ width: 12 }} />}
        data={context.similar.inspirations!.slice(0, 10)}
        renderItem={({ item, index }) => (
          <View style={{ width: 170, height: 132 }}>
            <InspirationImage
              onFavoritePress={(objectId, isFavorite, categories, tags) =>
                postFavorite({
                  isFavorite,
                  params: {
                    payload: {
                      object_id: objectId,
                      object_type: ObjectTypeEnum.Inspiration,
                    },
                    type: 'Inspiration',
                  },
                  event: {
                    object_type: ObjectTypeEnum.Inspiration,
                    categories_id_list: categories || [],
                    tags_id_list: tags || [],
                  },
                })
              }
              key={index}
              data={item}
              onPress={() => navigateToInspirationDetails(item.id)}
            />
          </View>
        )}
      />
    </Section>
  );
};
