import React, { useContext } from 'react';
import { View } from 'react-native';

import { ProductCard } from '~/components/Cards/ProductCard/ProductCard';
import { Section } from '~/components/Section/Section';
import { ContentSlider } from '~/components/Slider/Slider';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useRedirectionUrl } from '~/events/hooks';
import { useLinking } from '~/hooks/useLinking';
import { InspirationContext } from '~/pages/Inspiration/context/inspirationContext';
import theme from '~/theme';

export const InspirationProductSlider = () => {
  //----EVENTS----
  const _fnUrlEvent = useRedirectionUrl((link) => openUrl(link));
  //--X--EVENTS--X--
  const context = useContext(InspirationContext);
  const { openUrl } = useLinking();
  return (
    <Section
      bgColor={theme.colors.white['100']}
      title={'Produkty pasujące\ndo inspiracji'}
      style={{ paddingTop: 50 }}>
      <ContentSlider
        data={context.products}
        renderItem={({ item, index }) => (
          <View style={{ width: 170 }}>
            <ProductCard
              key={index}
              data={item}
              onPress={() =>
                _fnUrlEvent(context.brand.id, context.id, ObjectTypeEnum.Leaflet, item.link)
              }
            />
          </View>
        )}
      />
    </Section>
  );
};
