import { FC, useContext } from 'react';
import { Dimensions, TouchableOpacity, View } from 'react-native';
import Swiper from 'react-native-swiper';

import * as Styled from './InspirationGallery.styled';

import { CardFavourite } from '~/components/Cards/components/CardFavourite';
import { Icon } from '~/components/Icon/Icon';
import { ImageComponent } from '~/components/ImageComponent/ImageComponent';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { InspirationContext } from '~/pages/Inspiration/context/inspirationContext';
import { assets } from '~/theme/assets';

const deviceWidth = Dimensions.get('window').width;

interface Props {
  setActiveSlide: (slide: number) => void;
}

export const InspirationGallery: FC<Props> = ({ setActiveSlide }) => {
  const context = useContext(InspirationContext);
  const { postFavorite } = useFavoritesQuery();

  const RenderFavourite = () => {
    return (
      <Styled.Favourite>
        <CardFavourite
          active={context.is_favorite}
          onPress={(isFavorite) => {
            postFavorite({
              isFavorite,
              params: {
                payload: {
                  object_id: context.id,
                  object_type: ObjectTypeEnum.Inspiration,
                },
                type: 'Inspiration',
              },
              event: {
                object_type: ObjectTypeEnum.Inspiration,
                categories_id_list: context.categories || [],
                tags_id_list: context.tags || [],
              },
            });
          }}
        />
      </Styled.Favourite>
    );
  };

  if (context.images.length > 1) {
    return (
      <Styled.Container>
        <RenderFavourite />
        <Swiper
          style={{
            height: 260,
            paddingHorizontal: 0,
            paddingVertical: 0,
          }}
          pagingEnabled={true}
          showsButtons={false}
          showAdjacentViews={true}
          adjacentViewsPadding={0.5}
          adjacentViewsWidth={10}
          snapToAlignment={'center'}
          decelerationRate={0.7}
          onIndexChanged={(index) => setActiveSlide(index)}
          loop={true}
          bounces={false}
          showsPagination={true}
          scrollEnabled={true}
          removeClippedSubviews={false}
          autoplay={false}
          paginationStyle={{ bottom: 14 }}
          dot={
            <View style={{ marginHorizontal: 5 }}>
              <Icon iconSVG={assets.icons.line_svg} width={25} height={10} onPress={() => {}} />
            </View>
          }
          activeDot={
            <View style={{ marginHorizontal: 5 }}>
              <Icon
                iconSVG={assets.icons.line_active_svg}
                width={25}
                height={10}
                onPress={() => {}}
              />
            </View>
          }>
          {context.images.map((item, index) => {
            return (
              <View style={{ paddingHorizontal: 4 }} key={index}>
                <View style={{ borderRadius: 8, overflow: 'hidden' }}>
                  <ImageComponent
                    uri={item.image}
                    noCache={true}
                    height={220}
                    resizeMode="cover"
                    onPress={() => context.actions.setModalOpen(true)}
                  />
                </View>
              </View>
            );
          })}
        </Swiper>
      </Styled.Container>
    );
  } else {
    return (
      <View
        style={{
          height: deviceWidth > 500 ? 300 : 200,
          paddingHorizontal: 16,
        }}>
        <RenderFavourite />
        <TouchableOpacity onPress={() => context.actions.setModalOpen(true)}>
          <ImageComponent
            noCache={true}
            resizeMode="cover"
            borderRadius={8}
            style={{ position: 'relative', zIndex: 1 }}
            uri={context.images.length > 0 ? context.images[0].image : undefined}
            onPress={() => context.actions.setModalOpen(true)}
          />
        </TouchableOpacity>
      </View>
    );
  }
};
