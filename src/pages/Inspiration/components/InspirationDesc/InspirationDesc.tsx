import { useContext } from 'react';

import * as Styled from './InspirationDesc.styled';

import { Typography } from '~/components/Typography/Typography';
import { InspirationContext } from '~/pages/Inspiration/context/inspirationContext';
import theme from '~/theme';

export const InspirationDesc = () => {
  const context = useContext(InspirationContext);

  return (
    <Styled.Content>
      <Typography fontSize={10} fontWeight="500" color={theme.colors.gray['800']}>
        {context.title}
      </Typography>
      {context.subtitle ? (
        <Typography fontSize={20} fontWeight="600" color={theme.colors.black['300']}>
          {context.subtitle}
        </Typography>
      ) : null}
      {context.description ? (
        <Typography fontSize={15} fontWeight="400" color={theme.colors.black['300']}>
          {context.description}
        </Typography>
      ) : null}
    </Styled.Content>
  );
};
