import { useRef, useState } from 'react';
import { FlatList, NativeScrollEvent, NativeSyntheticEvent, Image, Linking } from 'react-native';

import * as Styled from './StartInfo.styled';

import { Layout } from '~/components/Layout/Layout';
import { Loader } from '~/components/Loader/Loader';
import { Typography } from '~/components/Typography/Typography';
import { useNavigate } from '~/hooks/useNavigate';
import { RouteEnum } from '~/routes/routes';
import { Control, Stepper } from '~/components/Onboarding';
import theme from '~/theme';
import { Button } from '~/components/Button/Button';
import { ButtonVariant, ButtonSize } from '~/components/Button/enum';
import { useStartupInfo } from '~/hooks/useStartupInfo';
import { saveShowOnce } from '~/helpers/startupInfo';
import { useAppDispatch } from '~/redux/store';
import { userSeeStartupInfo } from '~/redux/reducers/user/actions';

export const StartupInfo = () => {
  const flatListRef = useRef<FlatList>(null);
  const { data, isLoading } = useStartupInfo();
  const dispatch = useAppDispatch();

  const { navigation } = useNavigate();
  const [step, setStep] = useState(0);

  const handleNextStep = async () => {
    if (data && step < data.length - 1) {
      setStep(step + 1);
      flatListRef.current?.scrollToIndex({
        animated: true,
        index: step + 1,
      });
    } else {
      await saveShowOnce(data);
      dispatch(userSeeStartupInfo(true));
      navigation.navigate(RouteEnum.HOME);
    }
  };

  const handleCurrentVisibleIndex = (e: NativeSyntheticEvent<NativeScrollEvent>) => {
    const index = Math.round(e.nativeEvent.contentOffset.x / 300);
    setStep(index);
  };

  const onPressButton = (url: string) => {
    Linking.openURL(url);
  };

  if (isLoading) {
    return (
      <Layout footerShown={false} headerShown={false}>
        <Styled.Wrapper>
          <Loader />
        </Styled.Wrapper>
      </Layout>
    );
  }
  return (
    <Layout footerShown={false} headerShown={false}>
      <Styled.Wrapper>
        {data ? (
          <Styled.ListWrapper>
            <FlatList
              ref={flatListRef}
              data={data}
              pagingEnabled={true}
              horizontal={true}
              snapToAlignment={'center'}
              showsHorizontalScrollIndicator={false}
              onMomentumScrollEnd={handleCurrentVisibleIndex}
              keyExtractor={(_, index) => index.toString()}
              renderItem={({ item, index }) => (
                <Styled.Item key={index}>
                  {item.image && (
                    <Image source={{ uri: item.image }} style={{ width: 320, height: 320 }} />
                  )}
                  <Styled.Desc>
                    <Typography
                      fontSize={18}
                      fontWeight="400"
                      color={theme.colors.black[300]}
                      style={{ textAlign: 'center', padding: 0 }}>
                      {item.text}
                    </Typography>
                  </Styled.Desc>
                  {item.button_url && (
                    <Styled.ItemButton>
                      <Button
                        variant={ButtonVariant.FIFTH}
                        size={ButtonSize.MEDIUM}
                        onPress={() => onPressButton(item.button_url)}>
                        {item.button_text}
                      </Button>
                    </Styled.ItemButton>
                  )}
                </Styled.Item>
              )}
            />
            <Stepper active={step} data={data} />
          </Styled.ListWrapper>
        ) : null}
        <Control step={step} onPress={handleNextStep} length={data?.length} />
      </Styled.Wrapper>
    </Layout>
  );
};
