import axios from 'axios';

import { SERVER_URL } from '~/config.api';
import { googleLogout } from '~/helpers/socialLogout';
import { userSession } from '~/redux/reducers/states/actions';
import { SessionStates } from '~/redux/reducers/states/SessionStates';
import { userLogout, userTokenSuccess } from '~/redux/reducers/user/actions';
import store from '~/redux/store';

let refreshPromise: any = null;
const clearPromise = () => (refreshPromise = null);

export async function refreshToken(refresh: string) {
  try {
    const response = await fetch(`${SERVER_URL}/user/token/refresh/`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        refresh: refresh,
      }),
    });

    const data = await response.json();
    if (data.code === 'token_not_valid') {
      throw new Error('Token not valid');
    }
    store.dispatch(userTokenSuccess({ access: data.access, refresh: refresh }));

    return data.access;
  } catch (err) {
    store.dispatch(userLogout());
    await googleLogout();
    throw err;
  }
}

axios.interceptors.request.use(
  (config) => {
    const state = store.getState();
    const token = state.user.token;
    if (token && token.access) {
      config.headers.Authorization = `Bearer ${token.access}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  },
);

axios.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const config = error.config;

    if (error.response && error.response.status === 401 && !config._retry) {
      config._retry = true;
      if (!refreshPromise) {
        const state = store.getState();
        const token = state.user.token;
        if (token) {
          refreshPromise = refreshToken(token.refresh)
            .catch(() => {
              store.dispatch(userSession(SessionStates.EXPIRED));
            })
            .finally(clearPromise);
        } else {
          store.dispatch(userSession(SessionStates.UNAUTHENTICATED));
        }
      }
      const token = await refreshPromise;
      config.headers.Authorization = `Bearer ${token}`;
      return axios(config);
    }

    return Promise.reject(error);
  },
);

export default axios;
