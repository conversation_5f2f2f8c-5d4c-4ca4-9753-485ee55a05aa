import { ActionC<PERSON>WithoutPayload, ActionCreatorWithPayload } from '@reduxjs/toolkit';
import { AxiosResponse } from 'axios';

import { AppDispatch, RootState } from '~/redux/store';
import { IError } from '~/redux/types/common';

export const createFetchThunk =
  <T>(
    actionInit: ActionCreatorWithoutPayload,
    actionSuccess: ActionCreatorWithPayload<T>,
    actionFailure: ActionCreatorWithPayload<IError>,
    actionCache: ActionCreatorWithPayload<{ id: number; data: T; timestamp: number }>,
    api: (payload: { id: number }) => Promise<AxiosResponse<T>>,
    payload: { id: number },
    reducerName: string,
  ) =>
  (dispatch: AppDispatch, getState: () => RootState) => {
    const cache = getState()[reducerName].cache.detail[payload.id];
    return new Promise((resolve: (value: T) => void, reject) => {
      if (cache && Date.now() - cache.timestamp < 900000) {
        dispatch(actionSuccess(cache.data));
        resolve(cache.data);
      } else {
        dispatch(actionInit());
        api(payload)
          .then((response) => {
            dispatch(actionSuccess(response.data));
            dispatch(actionCache({ id: payload.id, data: response.data, timestamp: Date.now() }));
            resolve(response.data);
          })
          .catch((error) => {
            dispatch(
              actionFailure({
                message: `Error during ${api.name} execution`,
                status: true,
                id: payload.id,
              }),
            );
            reject(error);
          });
      }
    });
  };
