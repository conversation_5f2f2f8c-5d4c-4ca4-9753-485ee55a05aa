import {
  BaseQueryApi,
  BaseQueryFn,
  <PERSON>tchArgs,
  FetchBaseQueryError,
  fetchBaseQuery,
} from '@reduxjs/toolkit/query';
import { Mutex } from 'async-mutex';

import { RootState } from '../redux/store';

import { SERVER_URL } from '~/config.api';
import { userSession } from '~/redux/reducers/states/actions';
import { SessionStates } from '~/redux/reducers/states/SessionStates';
import { userLogout } from '~/redux/reducers/user/actions';

export interface RefreshResultData {
  access: string;
}

interface RefreshResult {
  data?: RefreshResultData;
}
const mutex = new Mutex();
const excludedUrls = ['content/discount/'];

let refreshToken: string | null | undefined = null;

/**
 * @function
 * Build request headers
 */
const buildHeaders = (
  headers: Headers,
  api: Pick<BaseQueryApi, 'getState' | 'extra' | 'endpoint' | 'type' | 'forced'>,
) => {
  const { token } = (api.getState() as RootState).user;
  refreshToken = token?.refresh;
  if (token?.access) {
    headers.set('Authorization', `Bearer ${token?.access}`);
  }
  return headers;
};

/**
 * @function
 * Build request params
 */
const buildParams = (params: Record<string, any>) => {
  return Object.keys(params)
    .flatMap((key) => {
      const value = params[key];

      if (value !== undefined && value !== null) {
        if (Array.isArray(value) && value.length > 0) {
          return value.map((v) => `${key}=${v}`);
        } else if (typeof value === 'boolean') {
          return `${key}=${value.toString()}`;
        } else if (typeof value === 'number' || typeof value === 'string') {
          return `${key}=${value}`;
        }
      }

      return '';
    })
    .filter((param) => param !== '')
    .join('&');
};

export const customBaseQuery: BaseQueryFn<FetchArgs, unknown, FetchBaseQueryError> = async (
  args,
  api,
  extraOptions,
) => {
  const baseQueryWithHeaders = fetchBaseQuery({
    baseUrl: '/',
    prepareHeaders: buildHeaders,
    paramsSerializer: buildParams,
  });
  args.url = `${SERVER_URL}/${args.url}`;

  let result = await baseQueryWithHeaders(args, api, extraOptions);

  if (excludedUrls.some((url) => args.url.includes(url))) return result;

  if (result.error && result.error.status === 401) {
    try {
      const response = (await baseQueryWithHeaders(
        {
          ...args,
          url: `${SERVER_URL}/user/token/refresh/`,
          method: 'POST',
          body: {
            refresh: refreshToken,
          },
        },
        api,
        extraOptions,
      )) as RefreshResult;
      if (response.data) {
        api.dispatch(userSession(SessionStates.AUTHENTICATED));
        result = await baseQueryWithHeaders(args, api, extraOptions);
      } else {
        api.dispatch(userLogout());
        api.dispatch(userSession(SessionStates.EXPIRED));
      }
    } catch (e) {
      api.dispatch(userLogout());
      api.dispatch(userSession(SessionStates.EXPIRED));
    }
  }
  return result;

  // if (result.error && result.error.status === 401) {
  //   if (!mutex.isLocked()) {
  //     // lock the mutex so that other requests will wait until the token has been refreshed
  //     const release = await mutex.acquire();
  //     try {
  //       // Try to refresh the token
  //       const refreshResult = (await baseQueryWithHeaders(
  //         {
  //           ...args,
  //           url: `${SERVER_URL}/user/token/refresh/`,
  //           method: 'POST',
  //           body: {
  //             refresh: refreshToken,
  //           },
  //         },
  //         api,
  //         extraOptions,
  //       )) as RefreshResult;

  //       if (refreshResult.data) {
  //         console.log('HERE_1: ', refreshResult.data);
  //         api.dispatch(userSession(SessionStates.AUTHENTICATED));
  //         // Retry the initial request
  //         result = await baseQueryWithHeaders(args, api, extraOptions);
  //       } else {
  //         api.dispatch(userLogout());
  //         api.dispatch(userSession(SessionStates.EXPIRED));
  //       }
  //     } catch (e) {
  //     } finally {
  //       release();
  //     }
  //   } else {
  //     // wait until the mutex is available without locking it
  //     await mutex.waitForUnlock();
  //     result = await baseQueryWithHeaders(args, api, extraOptions);
  //   }
  // }
  //return result;
};
