import { DiscountValueTypeEnum, ValueTypeEnum } from '~/enums/shared';

export const range = {
  [ValueTypeEnum.OTHER]: 'OTHER',
  [ValueTypeEnum.PERCENTAGE_UP_TO]: 'do',
  [ValueTypeEnum.PERCENTAGE_FROM]: 'od',
  [ValueTypeEnum.PLN_UP_TO]: 'do',
  [ValueTypeEnum.PLN_FROM]: 'od',
  [ValueTypeEnum.PERCENTAGE_EQUAL]: '',
  [ValueTypeEnum.PLN_EQUAL]: '',
};
export const unit = {
  [ValueTypeEnum.OTHER]: 'OTHER',
  [ValueTypeEnum.PERCENTAGE_UP_TO]: '%',
  [ValueTypeEnum.PERCENTAGE_FROM]: '%',
  [ValueTypeEnum.PLN_UP_TO]: 'zł',
  [ValueTypeEnum.PLN_FROM]: 'zł',
  [ValueTypeEnum.PERCENTAGE_EQUAL]: '%',
  [ValueTypeEnum.PLN_EQUAL]: 'zł',
};
export const minusValueType = {
  [ValueTypeEnum.OTHER]: '',
  [ValueTypeEnum.PERCENTAGE_UP_TO]: '-',
  [ValueTypeEnum.PERCENTAGE_FROM]: '-',
  [ValueTypeEnum.PLN_UP_TO]: '',
  [ValueTypeEnum.PLN_FROM]: '',
  [ValueTypeEnum.PERCENTAGE_EQUAL]: '',
  [ValueTypeEnum.PLN_EQUAL]: '',
};
export const minusDiscountValueType = {
  [DiscountValueTypeEnum.PERCENTAGE]: '-',
  [DiscountValueTypeEnum.PLN]: '',
};
export const percentageValues = [
  ValueTypeEnum.PERCENTAGE_FROM,
  ValueTypeEnum.PERCENTAGE_UP_TO,
  ValueTypeEnum.PERCENTAGE_EQUAL,
  DiscountValueTypeEnum.PERCENTAGE,
];

export const WIDE_SCREEN = 500;
