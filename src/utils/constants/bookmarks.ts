import { IBookmark } from '~/components/Modals/FavoriteModal/types';
import { BookmarkEnum } from '~/enums/bookmarkEnum';

export const bookmarks: Array<Pick<IBookmark, 'code' | 'name'>> = [
  {
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    code: BookmarkEnum.All,
  },
  {
    name: '<PERSON><PERSON><PERSON><PERSON>',
    code: BookmarkEnum.Inspiration,
  },
  {
    name: 'Pomysły',
    code: BookmarkEnum.Arranging,
  },
  {
    name: '<PERSON><PERSON>',
    code: BookmarkEnum.Live,
  },
];
