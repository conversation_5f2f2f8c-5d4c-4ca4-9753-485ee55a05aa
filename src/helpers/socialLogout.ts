import auth from '@react-native-firebase/auth';
import appleAuth from '@invertase/react-native-apple-authentication';
import { GoogleSignin } from '@react-native-google-signin/google-signin';

const revokeSignInWithAppleToken = async () => {
  const { authorizationCode } = await appleAuth.performRequest({
    requestedOperation: appleAuth.Operation.REFRESH,
  });

  if (!authorizationCode) {
    throw new Error('Apple Revocation failed - no authorizationCode returned');
  }

  auth().revokeToken(authorizationCode);
};

export const googleLogout = async () => {
  await GoogleSignin.revokeAccess();
  await GoogleSignin.signOut();
  await revokeSignInWithAppleToken();
};
