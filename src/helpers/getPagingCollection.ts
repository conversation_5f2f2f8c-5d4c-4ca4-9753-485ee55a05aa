import { AxiosResponse } from 'axios';

import { axiosParamsSerializer, buildPrams } from '~/helpers/buildApiParam';
import { ParamsOptions } from '~/types/shared';
import axios from '~/utils/axios.config';

interface IGetPagingCollection {
  url: string;
  payload?: ParamsOptions;
}

export const getPagingCollection = <T>({
  url,
  payload,
}: IGetPagingCollection): Promise<AxiosResponse<T>> => {
  const params: ParamsOptions = payload ? buildPrams(payload) : {};
  return axios<T>({
    method: 'GET',
    url: `${url}`,
    headers: {
      Accept: 'application/json',
    },
    params,
    paramsSerializer: axiosParamsSerializer,
  });
};
