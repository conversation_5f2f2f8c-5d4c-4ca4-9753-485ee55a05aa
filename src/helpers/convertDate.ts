export const convertStartDate = (date: string) => {
  const parts = date.split('-');
  return `${parts[2]}.${parts[1]}`;
};
export const convertEndDate = (date: string) => {
  const parts = date.split('-');
  return parts.reverse().join('.');
};

/**
 * it checks if item is expired or not
 * return true if item is not expired
 * return false if item is expired
 */
export const validate = (dateString: string | null | undefined): boolean => {
  if (dateString === null || dateString === undefined) return true;
  if (typeof dateString === 'string') {
    const currentDate = new Date();
    const parsedDate = new Date(
      Date.UTC(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate()),
    );
    const givenDate = new Date(Date.parse(dateString));
    return parsedDate <= givenDate;
  }
  return false;
};
