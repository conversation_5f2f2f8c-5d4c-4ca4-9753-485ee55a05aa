import { SERVER_URL } from '~/config.api';
import axios from '~/utils/axios.config';

interface IHeaders {
  Accept: string;
  Authorization?: string;
}

export const handleGetOTC = (
  email: string,
  type: 'registration' | 'password_reset' | 'email_update',
  token?: any,
) => {
  const headers: IHeaders = {
    Accept: 'application/json',
  };

  if (token) headers.Authorization = `Bearer ${token}`;

  return axios({
    method: 'POST',
    url: `${SERVER_URL}/user/otc/`,
    headers: {
      ...headers,
    },
    data: {
      email: email,
      purpose: type,
    },
  });
};
