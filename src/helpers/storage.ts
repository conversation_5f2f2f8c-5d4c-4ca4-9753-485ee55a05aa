import AsyncStorage from '@react-native-async-storage/async-storage';

import { StorageEnum } from '~/enums/storage';

export async function getItem(key: StorageEnum): Promise<string | null> {
  return await AsyncStorage.getItem(key);
}

export async function setItem(key: StorageEnum, value: string): Promise<void> {
  await AsyncStorage.setItem(key, value);
}

export async function removeItem(key: StorageEnum): Promise<void> {
  await AsyncStorage.removeItem(key);
}

export async function clear(): Promise<void> {
  await AsyncStorage.clear();
}
