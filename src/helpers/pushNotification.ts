import { getItem, removeItem } from './storage';

import { StorageEnum } from '~/enums/storage';
import { deleteDeviceToken } from '~/redux/api/notifications';
import { INotifySettings } from '~/types/user';

interface Props {
  userEmail?: string;
}

export const clearPushNotifications = async ({ userEmail }: Props) => {
  const last_device = await getItem(StorageEnum.LAST_DEVICE_TOKEN);
  const email = await getItem(StorageEnum.LAST_LOGIN_EMAIL);
  const activeEmail = userEmail || email;
  if (activeEmail && last_device) {
    deleteDeviceToken({
      email: activeEmail.toLowerCase(),
      token: last_device,
    }).then(() => {
      removeItem(StorageEnum.LAST_LOGIN_EMAIL);
      removeItem(StorageEnum.LAST_DEVICE_TOKEN);
    });
  }
};

export const checkPushStatus = (
  notification_settings: Array<INotifySettings>,
): boolean | undefined => {
  const pushNotification = notification_settings.find(
    (notification) => notification.type === 'push',
  );

  if (pushNotification) {
    return pushNotification.status;
  }

  return undefined;
};
