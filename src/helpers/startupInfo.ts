import { StorageEnum } from '~/enums/storage';
import { IStartupInfo } from '~/types/startupInfo';
import { getItem, setItem } from './storage';

export const parseStorageItems = async () => {
  const storedItems: string | null = await getItem(StorageEnum.STARTUP_INFO_ITEMS);
  const storedItemsParsed: Array<IStartupInfo> = JSON.parse(storedItems || '[]');
  return storedItemsParsed;
};

export const saveShowOnce = async (data: Array<IStartupInfo>) => {
  const filteredData: Array<IStartupInfo> = data.filter((item) => item.show_once);
  const storedItemsParsed = await parseStorageItems();
  const mergedArray = [...new Set([...filteredData, ...storedItemsParsed])];
  const arrayToSave = mergedArray.map((item) => ({ id: item.id, show_once: true }));
  await setItem(StorageEnum.STARTUP_INFO_ITEMS, JSON.stringify(arrayToSave));
  console.log('🚀 ~ saveShowOnce ~ arrayToSave', arrayToSave);
};

export const filterShowOnce = async (data: Array<IStartupInfo>) => {
  const storedItemsParsed = await parseStorageItems();
  const filteredData = data.filter(
    (item) => !storedItemsParsed.some((storedItem) => storedItem.id === item.id),
  );
  return filteredData;
};
