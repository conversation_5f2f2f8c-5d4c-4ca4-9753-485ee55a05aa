import { ParamsOptions } from '~/types/shared';

export const buildPrams = (payload: ParamsOptions) => {
  return payload
    ? {
        ...(payload.category_ids && { category_ids: payload.category_ids }),
        ...(payload.is_favorite !== undefined && { is_favorite: payload.is_favorite }),
        ...(payload.page && { page: payload.page }),
        ...(payload.page_size && { page_size: payload.page_size }),
        ...(payload.search && { search: payload.search }),
        ...(payload.search_id && { search_id: payload.search_id }),
        ...(payload.sort_by && { sort_by: payload.sort_by }),
        ...(payload.brand_id && { brand_id: payload.brand_id }),
        ...(payload.store_type !== undefined && { store_type: payload.store_type }),
        ...(payload.content_type && { content_type: payload.content_type }),
        ...(payload.object_type && { object_type: payload.object_type }),
        ...(payload.object_id && { object_id: payload.object_id }),
        ...(payload.phrase && { phrase: payload.phrase }),
        ...(payload.city_id && { city_id: payload.city_id }),
        ...(payload.bookmark && { bookmark: payload.bookmark }),
      }
    : {};
};

export const axiosParamsSerializer = (params: any) => {
  return Object.keys(params)
    .flatMap((key) => {
      const value = params[key];

      if (Array.isArray(value)) {
        // Jeżeli wartość to tablica, twórz string w formie "key=value1&key=value2&..."
        return value.map((v) => `${key}=${v}`).join('&');
      } else if (typeof value === 'boolean') {
        // Jeżeli wartość to boolean, twórz string w formie "key=true" lub "key=false"
        return `${key}=${value.toString()}`;
      } else if (typeof value === 'number' || typeof value === 'string') {
        // Jeżeli wartość to liczba lub string, twórz string w formie "key=value"
        return `${key}=${value}`;
      } else {
        // Dla innych przypadków, zwróć pusty string (możesz również rzucić błędem)
        return '';
      }
    })
    .join('&');
};
