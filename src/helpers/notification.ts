import notifee from '@notifee/react-native';

export const initNotification = async () => {
  await notifee.requestPermission();

  return await notifee.createChannel({
    id: 'brumNotification',
    name: 'brumNotification',
  });
};

export const cancelNotification = async (notificationId?: string) => {
  if (notificationId) {
    await notifee.cancelNotification(notificationId);
  }
};

export const sendNotification = async (text: string, channelId: string) => {
  await notifee.displayNotification({
    title: 'HomeProfit',
    body: text,
    android: {
      channelId,
      smallIcon: 'ic_launcher',
      pressAction: {
        id: 'default',
      },
    },
  });
};
