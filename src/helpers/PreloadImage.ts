import FastImage from 'react-native-fast-image';

import { IArticleCollections } from '~/types/article';
import { IBrandCollections } from '~/types/brands';
import { ICouponCollections } from '~/types/coupon';
import { IDiscountCollections } from '~/types/discount';
import { IInspirationCollections } from '~/types/inspirations';
import { INewspaperCollections } from '~/types/newspaper';

export class PreloadImages {
  private loadedImages: void[] = [];
  constructor(
    couponCollection: ICouponCollections,
    discountCollection: IDiscountCollections,
    brandCollection: IBrandCollections,
    newspaperCollection: INewspaperCollections,
    articleCollection: IArticleCollections,
    inspirationCollection: IInspirationCollections,
  ) {
    this.preloadCouponImages(couponCollection);
    this.preloadDiscountImages(discountCollection);
    this.preloadBrandImages(brandCollection);
    this.preloadNewspaperImages(newspaperCollection);
    this.preloadArticleImages(articleCollection);
    this.preloadInspirationImages(inspirationCollection);
  }
  public waitForImagesToLoad(): Promise<void[]> {
    return Promise.all(this.loadedImages);
  }

  private preloadCouponImages(coupons: ICouponCollections): void {
    coupons.results.forEach((coupon) => {
      if (coupon.bg_image) {
        this.loadedImages.push(FastImage.preload([{ uri: coupon.bg_image }]));
      }
      if (coupon.brand) {
        const logoUri = coupon.brand.logo ?? undefined;
        FastImage.preload([{ uri: logoUri }]);
      }
    });
  }
  private preloadDiscountImages(discounts: IDiscountCollections): void {
    discounts.results.forEach((discount) => {
      if (discount.bg_image) {
        this.loadedImages.push(FastImage.preload([{ uri: discount.bg_image }]));
      }
      if (discount.brand) {
        const logoUri = discount.brand.logo ?? undefined;
        FastImage.preload([{ uri: logoUri }]);
      }
    });
  }
  private preloadBrandImages(brands: IBrandCollections): void {
    brands.results.forEach((brand) => {
      if (brand.logo) {
        FastImage.preload([{ uri: brand.logo }]);
        this.loadedImages.push(FastImage.preload([{ uri: brand.logo }]));
      }
    });
  }
  private preloadNewspaperImages(newspapers: INewspaperCollections): void {
    newspapers.results.forEach((newspaper) => {
      if (newspaper.image) {
        FastImage.preload([{ uri: newspaper.image }]);
        this.loadedImages.push(FastImage.preload([{ uri: newspaper.image }]));
      }
      if (newspaper.brand) {
        const logoUri = newspaper.brand.logo ?? undefined;
        FastImage.preload([{ uri: logoUri }]);
      }
    });
  }
  private preloadArticleImages(articles: IArticleCollections): void {
    articles.results.forEach((article) => {
      if (article.first_image) {
        this.loadedImages.push(FastImage.preload([{ uri: article.first_image }]));
      }
      if (article.brand) {
        const logoUri = article.brand.logo ?? undefined;
        FastImage.preload([{ uri: logoUri }]);
      }
    });
  }
  private preloadInspirationImages(inspirations: IInspirationCollections): void {
    inspirations.results.forEach((inspiration) => {
      if (inspiration.first_image) {
        FastImage.preload([{ uri: inspiration.first_image }]);
        this.loadedImages.push(FastImage.preload([{ uri: inspiration.first_image }]));
      }
      if (inspiration.brand) {
        const logoUri = inspiration.brand.logo ?? undefined;
        FastImage.preload([{ uri: logoUri }]);
      }
    });
  }
}
