import { RouteProp, useRoute } from '@react-navigation/native';
import { useRef, useState } from 'react';
import Pdf from 'react-native-pdf';

import { checkCollectionType } from '~/helpers/checkCollectionType';
import { getPagingCollection } from '~/helpers/getPagingCollection';
import { useNavigate } from '~/hooks/useNavigate';
import {
  newspapersMayInterestYou,
  newspapersPage,
} from '~/redux/reducers/content/newspapers/actions';
import {
  getFilteredNewspapers,
  getNewspaperDetail,
  getNewspapers,
  getSimilarNewspapers,
} from '~/redux/reducers/content/newspapers/thunks';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { RootStackParamList } from '~/routes/types';
import { INewspaperCollections } from '~/types/newspaper';
import { ParamsOptions } from '~/types/shared';

export const useNewspapers = () => {
  const dispatch = useAppDispatch();
  const { isConnected } = useAppSelector((state) => state.network);
  const [pagingLoading, setPagingLoading] = useState(false);
  const pdfRef = useRef<Pdf>(null);
  const { collection, loading, detail, similar, filteredNewspapers } = useAppSelector(
    (state) => state.newspapers,
  );
  const [dataCollection, setDataCollection] = useState<INewspaperCollections>({
    count: 0,
    previous: null,
    next: null,
    results: [],
  });
  const [numberPages, setNumberPages] = useState(0);
  const [activePage, setActivePage] = useState(1);
  const [source, setSource] = useState<any>(null);

  const { params: routeCollectionParams } =
    useRoute<RouteProp<RootStackParamList, RouteEnum.NEWSPAPER>>();
  const { params: detailParams } =
    useRoute<RouteProp<RootStackParamList, RouteEnum.NEWSPAPER_DETAIL>>();
  const { navigateToCouponCollection, navigateToCouponDetails, navigateToSearch } = useNavigate();

  const [collectionParams, setCollectionParams] = useState<
    { params: ParamsOptions; prevScreen: RouteEnum } | undefined
  >(routeCollectionParams);

  const fetchNewspaperCollection = (): Promise<INewspaperCollections> => {
    return new Promise((resolve, reject) => {
      dispatch(getNewspapers())
        .then((res) => {
          dispatch(newspapersMayInterestYou(res.results.slice(0, 5)));
          resolve(res);
        })
        .catch((error) => {
          reject(error);
        });
    });
  };
  const fetchSimilarNewspapers = (params: ParamsOptions) => {
    dispatch(getSimilarNewspapers(params))
      .then((res) => {
        setDataCollection(res);
      })
      .catch((err) => {
        console.log(err);
      });
  };
  const fetchNewspaperDetail = async (id: number) => {
    return await dispatch(getNewspaperDetail({ id: id }))
      .then((res) => {
        return res;
      })
      .catch((err) => {
        throw err;
      });
  };
  const fetchByScreenType = async (screenType: RouteEnum, params: ParamsOptions) => {
    const obj = {
      [RouteEnum.BRAND_DETAIL]: async () => {
        return await dispatch(getSimilarNewspapers(params));
      },
      // TODO: hide search button
      // [RouteEnum.SEARCH]: async () => {
      //   return await dispatch(getFilteredNewspapers(params));
      // },
      [RouteEnum.FILTER_MODAL_SCREEN]: async () => {
        return await dispatch(getFilteredNewspapers(params));
      },
    };
    return await obj[screenType]();
  };
  const getByScreenType = (screenType: RouteEnum) => {
    const obj = {
      [RouteEnum.BRAND_DETAIL]: similar.newspapers,
      // TODO: hide search button
      // [RouteEnum.SEARCH]: filteredNewspapers,
      [RouteEnum.FILTER_MODAL_SCREEN]: filteredNewspapers,
    };
    setDataCollection(obj[screenType]);
  };

  const handlePaging = (screenType?: RouteEnum) => {
    if (isConnected && dataCollection.next && dataCollection.next !== null) {
      setPagingLoading(true);
      getPagingCollection<INewspaperCollections>({
        url: dataCollection.next,
        payload: collectionParams?.params,
      })
        .then((res) => {
          dispatch(
            newspapersPage({
              listType: checkCollectionType(screenType),
              collection: res.data,
            }),
          );
          setDataCollection({
            count: res.data.count,
            next: res.data.next,
            previous: res.data.previous,
            results: [...dataCollection.results, ...res.data.results],
          });
          setPagingLoading(false);
        })
        .catch((err) => {
          console.log(err);
          setPagingLoading(false);
        });
    }
  };

  return {
    similarNewspapers: similar.newspapers,
    collection: collectionParams ? filteredNewspapers : collection,
    initialCollection: collection,
    loading,
    filteredNewspapers,
    similar,
    detail,
    detailParams,
    collectionParams,
    dataCollection,
    numberPages,
    activePage,
    source,
    pagingLoading,
    setNumberPages,
    setActivePage,
    fetchSimilarNewspapers,
    setSource,
    fetchByScreenType,
    getByScreenType,
    navigateToCouponCollection,
    navigateToCouponDetails,
    navigateToSearch,
    fetchNewspaperCollection,
    fetchNewspaperDetail,
    setDataCollection,
    dispatch,
    pdfRef,
    routeCollectionParams,
    setCollectionParams,
    handlePaging,
  };
};
