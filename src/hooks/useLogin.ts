import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

import { useUserLoginStatus } from '~/events/hooks/useUserLoginStatus';
import { useNavigate } from '~/hooks/useNavigate';
import { getMe, getPremium, getProfile, getToken } from '~/redux/reducers/user/thunks';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { IStatus } from '~/types/errors';
import { formDefault } from '~/validation/loginForm/Default';
import { formSchema } from '~/validation/loginForm/Schema';
import { TForm } from '~/validation/loginForm/types';

export const useLogin = () => {
  //----EVENTS----
  const _fnLoginStatus = useUserLoginStatus();
  //--X--EVENTS--X--
  const { navigation } = useNavigate();
  const [loading, setLoading] = useState<boolean>(false);
  const [status, setStatus] = useState<IStatus | undefined>(undefined);
  const { onboarding } = useAppSelector((state) => state.user);
  const [route, setRoute] = useState<RouteEnum.HOME | RouteEnum.ONBOARDING>(RouteEnum.ONBOARDING);
  const dispatch = useAppDispatch();

  const form = useForm<TForm>({
    resolver: zodResolver(formSchema),
    defaultValues: formDefault,
    mode: 'onSubmit',
    reValidateMode: 'onChange',
  });

  useEffect(() => {
    if (onboarding) {
      setRoute(RouteEnum.ONBOARDING);
    } else {
      setRoute(RouteEnum.HOME);
    }
  }, [onboarding]);

  const handleLogin = (data: TForm) => {
    setLoading(true);
    // Nie potrzebujemy już trim() tutaj, ponieważ jest wykonywany na poziomie walidacji
    dispatch(getToken({ email: data.email, password: data.password }))
      .then((res) => {
        dispatch(getProfile({ token: res.access }))
          .then(() => {
            _fnLoginStatus(res.access);
            dispatch(getPremium({ token: res.access }))
              .catch(() => {
                /**
                 * @description
                 * Brak działania w catch jest zamierzony - przy braku premium otrzymujemy błąd
                 */
              })
              .finally(() => {
                dispatch(getMe({ token: res.access }))
                  .catch(() => {
                    setLoading(false);
                    setStatus({
                      message: 'Wystąpił błąd podczas pobierania danych użytkownika 1',
                      status: 'error',
                    });
                  })
                  .finally(() => {
                    navigation.navigate(route);
                  });
              });
          })
          .catch(() => {
            setLoading(false);
            setStatus({
              message: 'Wystąpił błąd podczas logowania',
              status: 'error',
            });
          });
      })
      .catch(() => {
        setLoading(false);
        setStatus({
          message: 'Niepoprawny login lub hasło',
          status: 'error',
        });
      });
  };

  return {
    form,
    loading,
    navigation,
    status,
    route,
    handleLogin,
    setLoading,
    setStatus,
  };
};
