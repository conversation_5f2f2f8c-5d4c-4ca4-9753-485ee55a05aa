import { Linking } from 'react-native';
import { useNavigate } from '~/hooks/useNavigate';
import { getBanners } from '~/redux/reducers/content/banners/thunks';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { IBanner } from '~/types/banner';

export const useBanners = () => {
  const dispatch = useAppDispatch();
  const { navigation } = useNavigate();
  const { collection } = useAppSelector((state) => state.banners);

  const fetchBannerCollection = () => {
    return new Promise((resolve, reject) => {
      dispatch(getBanners())
        .then((res) => {
          resolve(res);
        })
        .catch((error) => {
          reject(error);
        });
    });
  };
  const handleDetails = (banner: IBanner) => {
    if (banner.card) {
      navigation.navigate(RouteEnum.DISCOUNT_DETAIL, { id: banner.card });
    }

    if (banner.url) {
      Linking.openURL(banner.url);
    }
  };

  return {
    collection,
    navigation,
    fetchBannerCollection,
    handleDetails,
  };
};
