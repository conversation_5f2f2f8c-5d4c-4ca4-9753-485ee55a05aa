import { NavigationContainer } from '@react-navigation/native';
import { renderHook } from '@testing-library/react-native';
import { ReactNode } from 'react';
import { Provider } from 'react-redux';

import { useShopModalActions } from '~/hooks/useShopModalActions';
import store from '~/redux/store';
import { IAddress } from '~/types/addresses';

jest.mock('@react-native-google-signin/google-signin', () => {});

const mockedResult: IAddress[] = [
  {
    name: 'test',
    email: 'test',
    city: 'Poznań',
    zip_code: 'test',
    street: 'test',
    house_number: 'test',
    phone_number1: 'test',
    phone_number2: 'test',
    location: 'test',
  },
];
const common: IAddress = {
  name: 'test',
  email: 'test',
  zip_code: 'test',
  street: 'test',
  house_number: 'test',
  phone_number1: 'test',
  phone_number2: 'test',
  location: 'test',
};
const mockedData: IAddress[] = [
  {
    ...common,
    city: 'Poznań',
  },
  {
    ...common,
    city: 'Poznań',
  },
  {
    ...common,
    city: 'Wrocław',
  },
  {
    ...common,
    city: 'Wrocław',
  },
  {
    ...common,
    city: 'Wrocław',
  },
  {
    ...common,
    city: 'Gdańsk',
  },
  {
    ...common,
    city: 'Gdańsk',
  },
  {
    ...common,
    city: 'Gdańsk',
  },
];

jest.mock('~/redux/reducers/content/brands/thunks', () => ({
  getBrandAddresses: jest.fn(() => Promise.resolve(mockedResult)),
}));

describe('useShopModalActions hook', () => {
  const wrapper = ({ children }: { children: ReactNode }) => (
    <Provider store={store}>
      <NavigationContainer>{children}</NavigationContainer>
    </Provider>
  );
  const { result } = renderHook(() => useShopModalActions(), { wrapper });

  const sortAddressesByUserLocation = result.current.sortAddressesByUserLocation;
  const groupAddressesByCity = result.current.groupAddressesByCity;

  it('should sort addresses by user location', () => {
    expect(sortAddressesByUserLocation(mockedData, 'Poznań')).toMatchSnapshot();
    expect(sortAddressesByUserLocation(mockedData, 'Gdańsk')).toMatchSnapshot();
    expect(sortAddressesByUserLocation(mockedData, 'Wrocław')).toMatchSnapshot();
  });

  it('should group addresses by city', () => {
    expect(groupAddressesByCity(mockedData, mockedData)).toMatchSnapshot();
  });
});
