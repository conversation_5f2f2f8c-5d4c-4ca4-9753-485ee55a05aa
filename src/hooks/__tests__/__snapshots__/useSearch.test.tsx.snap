// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`useSearch hook should correctly render component based on screen type 1`] = `
<CouponCard
  data={
    {
      "bg_color": "test",
      "bg_image": "test",
      "brand": {
        "id": 1,
        "is_favorite": true,
        "logo": "test",
        "name": "test",
      },
      "categories": [
        1,
      ],
      "custom_brand_name": "test",
      "discount_value": "test",
      "end_date": "2021-01-01",
      "id": 1,
      "is_favorite": true,
      "logo": "test",
      "start_date": "2021-01-01",
      "tags": [
        1,
      ],
      "title": "test",
      "value_type": 1,
    }
  }
  onFavoritePress={[Function]}
  onPress={[Function]}
/>
`;

exports[`useSearch hook should correctly render component based on screen type 2`] = `
<BrandCard
  data={
    {
      "id": 1,
      "is_favorite": true,
      "logo": "test",
      "name": "test",
    }
  }
  onFavoritePress={[Function]}
  onPress={[Function]}
/>
`;

exports[`useSearch hook should correctly render component based on screen type 3`] = `
<DiscountCard
  data={
    {
      "bg_color": "test",
      "brand": {
        "id": 1,
        "is_favorite": true,
        "logo": "test",
        "name": "test",
      },
      "categories": [
        1,
      ],
      "discount_value": "test",
      "id": 1,
      "is_favorite": true,
      "name": "test",
      "tags": [
        1,
      ],
      "valid_from": "2021-01-01",
      "valid_to": "2021-01-01",
      "value_type": 0,
    }
  }
  onFavoritePress={[Function]}
  onPress={[Function]}
/>
`;

exports[`useSearch hook should correctly render component based on screen type 4`] = `
<ArticleCard
  data={
    {
      "brand": {
        "id": 1,
        "is_favorite": true,
        "logo": "test",
        "name": "test",
      },
      "categories": [
        1,
      ],
      "first_image": "test",
      "id": 1,
      "is_favorite": true,
      "publish_date": "2021-01-01",
      "tags": [
        27,
      ],
      "title": "test",
    }
  }
  onFavoritePress={[Function]}
  onPress={[Function]}
/>
`;

exports[`useSearch hook should correctly render component based on screen type 5`] = `
<NewspaperCard
  data={
    {
      "brand": {
        "id": 1,
        "is_favorite": true,
        "logo": "test",
        "name": "test",
      },
      "id": 1,
      "is_favorite": false,
      "title": "title",
      "valid_from": "2021-01-01",
    }
  }
  onFavoritePress={[Function]}
  onPress={[Function]}
/>
`;

exports[`useSearch hook should correctly render component based on screen type 6`] = `
<View
  style={
    {
      "height": 132,
      "width": 170,
    }
  }
>
  <InspirationImage
    data={
      {
        "brand": {
          "id": 1,
          "is_favorite": true,
          "logo": "test",
          "name": "test",
        },
        "categories": [
          1,
        ],
        "first_image": "test",
        "id": 1,
        "is_favorite": true,
      }
    }
    onFavoritePress={[Function]}
    onPress={[Function]}
  />
</View>
`;
