// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`useShopModalActions hook should group addresses by city 1`] = `
[
  {
    "Gdańsk": [
      {
        "city": "Gdańsk",
        "email": "test",
        "house_number": "test",
        "location": "test",
        "name": "test",
        "phone_number1": "test",
        "phone_number2": "test",
        "street": "test",
        "zip_code": "test",
      },
      {
        "city": "Gdańsk",
        "email": "test",
        "house_number": "test",
        "location": "test",
        "name": "test",
        "phone_number1": "test",
        "phone_number2": "test",
        "street": "test",
        "zip_code": "test",
      },
      {
        "city": "Gdańsk",
        "email": "test",
        "house_number": "test",
        "location": "test",
        "name": "test",
        "phone_number1": "test",
        "phone_number2": "test",
        "street": "test",
        "zip_code": "test",
      },
    ],
    "Poznań": [
      {
        "city": "Poznań",
        "email": "test",
        "house_number": "test",
        "location": "test",
        "name": "test",
        "phone_number1": "test",
        "phone_number2": "test",
        "street": "test",
        "zip_code": "test",
      },
      {
        "city": "Poznań",
        "email": "test",
        "house_number": "test",
        "location": "test",
        "name": "test",
        "phone_number1": "test",
        "phone_number2": "test",
        "street": "test",
        "zip_code": "test",
      },
    ],
    "Wrocław": [
      {
        "city": "Wrocław",
        "email": "test",
        "house_number": "test",
        "location": "test",
        "name": "test",
        "phone_number1": "test",
        "phone_number2": "test",
        "street": "test",
        "zip_code": "test",
      },
      {
        "city": "Wrocław",
        "email": "test",
        "house_number": "test",
        "location": "test",
        "name": "test",
        "phone_number1": "test",
        "phone_number2": "test",
        "street": "test",
        "zip_code": "test",
      },
      {
        "city": "Wrocław",
        "email": "test",
        "house_number": "test",
        "location": "test",
        "name": "test",
        "phone_number1": "test",
        "phone_number2": "test",
        "street": "test",
        "zip_code": "test",
      },
    ],
  },
  {
    "Gdańsk": [
      {
        "city": "Gdańsk",
        "email": "test",
        "house_number": "test",
        "location": "test",
        "name": "test",
        "phone_number1": "test",
        "phone_number2": "test",
        "street": "test",
        "zip_code": "test",
      },
      {
        "city": "Gdańsk",
        "email": "test",
        "house_number": "test",
        "location": "test",
        "name": "test",
        "phone_number1": "test",
        "phone_number2": "test",
        "street": "test",
        "zip_code": "test",
      },
      {
        "city": "Gdańsk",
        "email": "test",
        "house_number": "test",
        "location": "test",
        "name": "test",
        "phone_number1": "test",
        "phone_number2": "test",
        "street": "test",
        "zip_code": "test",
      },
    ],
    "Poznań": [
      {
        "city": "Poznań",
        "email": "test",
        "house_number": "test",
        "location": "test",
        "name": "test",
        "phone_number1": "test",
        "phone_number2": "test",
        "street": "test",
        "zip_code": "test",
      },
      {
        "city": "Poznań",
        "email": "test",
        "house_number": "test",
        "location": "test",
        "name": "test",
        "phone_number1": "test",
        "phone_number2": "test",
        "street": "test",
        "zip_code": "test",
      },
    ],
    "Wrocław": [
      {
        "city": "Wrocław",
        "email": "test",
        "house_number": "test",
        "location": "test",
        "name": "test",
        "phone_number1": "test",
        "phone_number2": "test",
        "street": "test",
        "zip_code": "test",
      },
      {
        "city": "Wrocław",
        "email": "test",
        "house_number": "test",
        "location": "test",
        "name": "test",
        "phone_number1": "test",
        "phone_number2": "test",
        "street": "test",
        "zip_code": "test",
      },
      {
        "city": "Wrocław",
        "email": "test",
        "house_number": "test",
        "location": "test",
        "name": "test",
        "phone_number1": "test",
        "phone_number2": "test",
        "street": "test",
        "zip_code": "test",
      },
    ],
  },
]
`;

exports[`useShopModalActions hook should sort addresses by user location 1`] = `
[
  [
    {
      "city": "Poznań",
      "email": "test",
      "house_number": "test",
      "location": "test",
      "name": "test",
      "phone_number1": "test",
      "phone_number2": "test",
      "street": "test",
      "zip_code": "test",
    },
    {
      "city": "Poznań",
      "email": "test",
      "house_number": "test",
      "location": "test",
      "name": "test",
      "phone_number1": "test",
      "phone_number2": "test",
      "street": "test",
      "zip_code": "test",
    },
  ],
  [
    {
      "city": "Wrocław",
      "email": "test",
      "house_number": "test",
      "location": "test",
      "name": "test",
      "phone_number1": "test",
      "phone_number2": "test",
      "street": "test",
      "zip_code": "test",
    },
    {
      "city": "Wrocław",
      "email": "test",
      "house_number": "test",
      "location": "test",
      "name": "test",
      "phone_number1": "test",
      "phone_number2": "test",
      "street": "test",
      "zip_code": "test",
    },
    {
      "city": "Wrocław",
      "email": "test",
      "house_number": "test",
      "location": "test",
      "name": "test",
      "phone_number1": "test",
      "phone_number2": "test",
      "street": "test",
      "zip_code": "test",
    },
    {
      "city": "Gdańsk",
      "email": "test",
      "house_number": "test",
      "location": "test",
      "name": "test",
      "phone_number1": "test",
      "phone_number2": "test",
      "street": "test",
      "zip_code": "test",
    },
    {
      "city": "Gdańsk",
      "email": "test",
      "house_number": "test",
      "location": "test",
      "name": "test",
      "phone_number1": "test",
      "phone_number2": "test",
      "street": "test",
      "zip_code": "test",
    },
    {
      "city": "Gdańsk",
      "email": "test",
      "house_number": "test",
      "location": "test",
      "name": "test",
      "phone_number1": "test",
      "phone_number2": "test",
      "street": "test",
      "zip_code": "test",
    },
  ],
]
`;

exports[`useShopModalActions hook should sort addresses by user location 2`] = `
[
  [
    {
      "city": "Gdańsk",
      "email": "test",
      "house_number": "test",
      "location": "test",
      "name": "test",
      "phone_number1": "test",
      "phone_number2": "test",
      "street": "test",
      "zip_code": "test",
    },
    {
      "city": "Gdańsk",
      "email": "test",
      "house_number": "test",
      "location": "test",
      "name": "test",
      "phone_number1": "test",
      "phone_number2": "test",
      "street": "test",
      "zip_code": "test",
    },
    {
      "city": "Gdańsk",
      "email": "test",
      "house_number": "test",
      "location": "test",
      "name": "test",
      "phone_number1": "test",
      "phone_number2": "test",
      "street": "test",
      "zip_code": "test",
    },
  ],
  [
    {
      "city": "Poznań",
      "email": "test",
      "house_number": "test",
      "location": "test",
      "name": "test",
      "phone_number1": "test",
      "phone_number2": "test",
      "street": "test",
      "zip_code": "test",
    },
    {
      "city": "Poznań",
      "email": "test",
      "house_number": "test",
      "location": "test",
      "name": "test",
      "phone_number1": "test",
      "phone_number2": "test",
      "street": "test",
      "zip_code": "test",
    },
    {
      "city": "Wrocław",
      "email": "test",
      "house_number": "test",
      "location": "test",
      "name": "test",
      "phone_number1": "test",
      "phone_number2": "test",
      "street": "test",
      "zip_code": "test",
    },
    {
      "city": "Wrocław",
      "email": "test",
      "house_number": "test",
      "location": "test",
      "name": "test",
      "phone_number1": "test",
      "phone_number2": "test",
      "street": "test",
      "zip_code": "test",
    },
    {
      "city": "Wrocław",
      "email": "test",
      "house_number": "test",
      "location": "test",
      "name": "test",
      "phone_number1": "test",
      "phone_number2": "test",
      "street": "test",
      "zip_code": "test",
    },
  ],
]
`;

exports[`useShopModalActions hook should sort addresses by user location 3`] = `
[
  [
    {
      "city": "Wrocław",
      "email": "test",
      "house_number": "test",
      "location": "test",
      "name": "test",
      "phone_number1": "test",
      "phone_number2": "test",
      "street": "test",
      "zip_code": "test",
    },
    {
      "city": "Wrocław",
      "email": "test",
      "house_number": "test",
      "location": "test",
      "name": "test",
      "phone_number1": "test",
      "phone_number2": "test",
      "street": "test",
      "zip_code": "test",
    },
    {
      "city": "Wrocław",
      "email": "test",
      "house_number": "test",
      "location": "test",
      "name": "test",
      "phone_number1": "test",
      "phone_number2": "test",
      "street": "test",
      "zip_code": "test",
    },
  ],
  [
    {
      "city": "Poznań",
      "email": "test",
      "house_number": "test",
      "location": "test",
      "name": "test",
      "phone_number1": "test",
      "phone_number2": "test",
      "street": "test",
      "zip_code": "test",
    },
    {
      "city": "Poznań",
      "email": "test",
      "house_number": "test",
      "location": "test",
      "name": "test",
      "phone_number1": "test",
      "phone_number2": "test",
      "street": "test",
      "zip_code": "test",
    },
    {
      "city": "Gdańsk",
      "email": "test",
      "house_number": "test",
      "location": "test",
      "name": "test",
      "phone_number1": "test",
      "phone_number2": "test",
      "street": "test",
      "zip_code": "test",
    },
    {
      "city": "Gdańsk",
      "email": "test",
      "house_number": "test",
      "location": "test",
      "name": "test",
      "phone_number1": "test",
      "phone_number2": "test",
      "street": "test",
      "zip_code": "test",
    },
    {
      "city": "Gdańsk",
      "email": "test",
      "house_number": "test",
      "location": "test",
      "name": "test",
      "phone_number1": "test",
      "phone_number2": "test",
      "street": "test",
      "zip_code": "test",
    },
  ],
]
`;
