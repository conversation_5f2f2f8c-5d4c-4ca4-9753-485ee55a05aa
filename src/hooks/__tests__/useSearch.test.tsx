import { NavigationContainer } from '@react-navigation/native';
import { renderHook } from '@testing-library/react-native';
import { ReactNode } from 'react';
import { Provider } from 'react-redux';

import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { ValueTypeEnum } from '~/enums/shared';
import { useSearch } from '~/hooks/useSearch';
import store from '~/redux/store';
import { IArticle } from '~/types/article';
import { IBaseBrand } from '~/types/brands';
import { ICoupon } from '~/types/coupon';
import { IDiscount } from '~/types/discount';
import { IInspiration } from '~/types/inspirations';
import { INewspaper } from '~/types/newspaper';
import { ISearchResultData } from '~/types/search';

const mockedResult: ISearchResultData[] = [
  {
    id: 1,
    name: 'test',
    count: 1,
  },
];

jest.mock('~/redux/reducers/content/search/thunks', () => ({
  getSearch: jest.fn(() => Promise.resolve(mockedResult)),
}));

jest.mock('@react-native-google-signin/google-signin', () => {});

describe('useSearch hook', () => {
  const wrapper = ({ children }: { children: ReactNode }) => (
    <Provider store={store}>
      <NavigationContainer>{children}</NavigationContainer>
    </Provider>
  );
  const { result } = renderHook(() => useSearch(), { wrapper });

  const renderContent = result.current.renderContent;
  const categoryName = result.current.categoryName;
  const fetchSearchCollection = result.current.fetchSearchCollection;

  const mockBrandData: IBaseBrand = {
    id: 1,
    name: 'test',
    logo: 'test',
    is_favorite: true,
  };
  const common = {
    id: 1,
    brand: mockBrandData,
    categories: [1],
    is_favorite: true,
  };
  const mockCouponData: ICoupon = {
    title: 'test',
    start_date: '2021-01-01',
    end_date: '2021-01-01',
    custom_brand_name: 'test',
    discount_value: 'test',
    value_type: ValueTypeEnum.PERCENTAGE_UP_TO,
    bg_color: 'test',
    logo: 'test',
    bg_image: 'test',
    tags: [1],
    ...common,
  };
  const mockDiscountData: IDiscount = {
    name: 'test',
    valid_from: '2021-01-01',
    valid_to: '2021-01-01',
    value_type: ValueTypeEnum.OTHER,
    discount_value: 'test',
    bg_color: 'test',
    tags: [1],
    ...common,
  };
  const mockArticleData: IArticle = {
    tags: [27],
    title: 'test',
    first_image: 'test',
    publish_date: '2021-01-01',
    ...common,
  };
  const mockLeafletData: INewspaper = {
    id: 1,
    title: 'title',
    valid_from: '2021-01-01',
    brand: mockBrandData,
    is_favorite: false,
  };
  const mockInspirationData: IInspiration = {
    ...common,
    first_image: 'test',
  };

  it('should correctly render component based on screen type', () => {
    expect(renderContent(ObjectTypeEnum.Coupon, mockCouponData, 0)).toMatchSnapshot();
    expect(renderContent(ObjectTypeEnum.Brand, mockBrandData, 0)).toMatchSnapshot();
    expect(renderContent(ObjectTypeEnum.Card, mockDiscountData, 0)).toMatchSnapshot();
    expect(renderContent(ObjectTypeEnum.Article, mockArticleData, 0)).toMatchSnapshot();
    expect(renderContent(ObjectTypeEnum.Leaflet, mockLeafletData, 0)).toMatchSnapshot();
    expect(renderContent(ObjectTypeEnum.Inspiration, mockInspirationData, 0)).toMatchSnapshot();
  });
  it('should correctly return category name on screen type', () => {
    expect(categoryName(ObjectTypeEnum.Coupon)).toBe('Okazji');
    expect(categoryName(ObjectTypeEnum.Brand)).toBe('Marek');
    expect(categoryName(ObjectTypeEnum.Card)).toBe('Rabaty HomeProfit');
    expect(categoryName(ObjectTypeEnum.Article)).toBe('Artykułów');
    expect(categoryName(ObjectTypeEnum.Leaflet)).toBe('Gazetek');
    expect(categoryName(ObjectTypeEnum.Inspiration)).toBe('Inspiracji');
  });
  it('should fetch search collection correctly', async () => {
    const val = 'test';
    const screenType = ObjectTypeEnum.Coupon;
    const res = await fetchSearchCollection(val, screenType);
    expect(res).toBe(mockedResult);
  });
  it('should fetch submit correctly', async () => {
    const val = 'test';
    const screenType = ObjectTypeEnum.Coupon;
    const res = await fetchSearchCollection(val, screenType);
    expect(res).toBe(mockedResult);
  });
});
