import { useContext } from 'react';
import { FilterContext } from '~/context/filterContext';
import { useNavigate } from './useNavigate';

export const useResetSearchWithBack = () => {
  const _filterContext = useContext(FilterContext);
  const { navigateBack } = useNavigate();

  const handleBackWithReset = () => {
    if (_filterContext.searchString) {
      _filterContext.clearSearch();
    } else {
      navigateBack();
    }
  };

  return {
    handleBackWithReset,
  };
};
