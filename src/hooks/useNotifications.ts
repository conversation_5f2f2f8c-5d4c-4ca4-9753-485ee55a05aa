import { useFocusEffect } from '@react-navigation/native';
import { useCallback, useContext } from 'react';

import { notificationContext } from '~/context/notificationContext';
import { NotifyTypes } from '~/enums/notifyTypes';
import { sendNotification } from '~/helpers/notification';
import { getNotifications } from '~/redux/reducers/content/notifications/thunks';
import { userSetLastNotify } from '~/redux/reducers/user/actions';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { INotification } from '~/types/notification';

export const useNotifications = () => {
  const dispatch = useAppDispatch();
  const { channelId } = useContext(notificationContext);
  const { collection, loading } = useAppSelector((state) => state.notifications);
  const { last_notify, notification_settings } = useAppSelector((state) => state.user);

  const checkNotify = async (last_result: INotification) => {
    /**
     * @description
     * Condition to situation when user disable push notification
     */
    if (notification_settings.find((item) => item.type === NotifyTypes.push)?.status === true) {
      if (last_notify !== last_result.modified_at) {
        /**
         * @description
         * Condition to situation when admin remove last notify from DB
         * */
        const lastNotify = new Date(last_notify);
        const newNotify = new Date(last_result.modified_at);
        if (newNotify > lastNotify) {
          await sendNotification(last_result.title, channelId);
        }
      }
    }
  };

  const handleGetNotifications = () => {
    dispatch(getNotifications())
      .then((res) => {
        if (res.results.length > 0) {
          checkNotify(res.results[0])
            .then(() => {
              dispatch(userSetLastNotify(res.results[0].modified_at));
            })
            .catch(() => {});
        }
      })
      .catch(() => {});
  };

  useFocusEffect(
    useCallback(() => {
      handleGetNotifications();
    }, [last_notify, notification_settings]),
  );

  return {
    loading,
    collection,
  };
};
