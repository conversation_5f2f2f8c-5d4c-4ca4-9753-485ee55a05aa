import { RouteProp, useRoute } from '@react-navigation/native';
import { useState } from 'react';

import { checkCollectionType } from '~/helpers/checkCollectionType';
import { getPagingCollection } from '~/helpers/getPagingCollection';
import { useNavigate } from '~/hooks/useNavigate';
import { brandPage, brandsMayInterestYou } from '~/redux/reducers/content/brands/actions';
import {
  getBrandAddresses,
  getBrandDetail,
  getBrands,
  getFilteredBrands,
} from '~/redux/reducers/content/brands/thunks';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { RootStackParamList } from '~/routes/types';
import { IBrandCollections } from '~/types/brands';
import { ParamsOptions } from '~/types/shared';

export const useBrands = () => {
  const dispatch = useAppDispatch();
  const { isConnected } = useAppSelector((state) => state.network);
  const [pagingLoading, setPagingLoading] = useState(false);
  const { collection, detail, filteredBrands, loading } = useAppSelector((state) => state.brands);
  const [dataCollection, setDataCollection] = useState<IBrandCollections>({
    count: 0,
    previous: null,
    next: null,
    results: [],
  });

  const { params: routeCollectionParams } =
    useRoute<RouteProp<RootStackParamList, RouteEnum.BRAND>>();
  const { params: detailParams } =
    useRoute<RouteProp<RootStackParamList, RouteEnum.BRAND_DETAIL>>();
  const { navigateToSearch, navigateToBrandCollection, navigateToBrandDetails } = useNavigate();

  const [collectionParams, setCollectionParams] = useState<
    { params: ParamsOptions; prevScreen: RouteEnum } | undefined
  >(routeCollectionParams);

  const fetchBrandCollection = (): Promise<IBrandCollections> => {
    return new Promise((resolve, reject) => {
      dispatch(getBrands())
        .then((res) => {
          dispatch(brandsMayInterestYou(res.results.slice(0, 5)));
          resolve(res);
        })
        .catch((error) => {
          reject(error);
        });
    });
  };
  const fetchBrandDetail = async (id: number) => {
    return await dispatch(getBrandDetail({ id: id }))
      .then((res) => {
        return res;
      })
      .catch((err) => {
        throw err;
      });
  };

  const fetchBrandAdresses = async (id: number) => {
    dispatch(getBrandAddresses({ id }));
  };

  const fetchByScreenType = async (screenType: RouteEnum, params: ParamsOptions) => {
    const obj = {
      // TODO: hide search button
      // [RouteEnum.SEARCH]: async () => {
      //   return await dispatch(getFilteredBrands(params));
      // },
      [RouteEnum.FILTER_MODAL_SCREEN]: async () => {
        return await dispatch(getFilteredBrands(params));
      },
    };
    return await obj[screenType]();
  };
  const getByScreenType = (screenType: RouteEnum) => {
    const obj = {
      // TODO: hide search button
      // [RouteEnum.SEARCH]: filteredBrands,
      [RouteEnum.FILTER_MODAL_SCREEN]: filteredBrands,
    };
    setDataCollection(obj[screenType]);
  };
  const handlePaging = (screenType?: RouteEnum) => {
    if (isConnected && dataCollection.next && dataCollection.next !== null) {
      setPagingLoading(true);
      getPagingCollection<IBrandCollections>({
        url: dataCollection.next,
        payload: collectionParams?.params,
      })
        .then((res) => {
          dispatch(
            brandPage({
              listType: checkCollectionType(screenType),
              collection: res.data,
            }),
          );
          setDataCollection({
            count: res.data.count,
            next: res.data.next,
            previous: res.data.previous,
            results: [...dataCollection.results, ...res.data.results],
          });
          setPagingLoading(false);
        })
        .catch((err) => {
          console.log(err);
          setPagingLoading(false);
        });
    }
  };

  return {
    collection: collectionParams ? filteredBrands : collection,
    initialCollection: collection,
    detail,
    detailParams,
    filteredBrands,
    collectionParams,
    dataCollection,
    loading,
    routeCollectionParams,
    pagingLoading,
    fetchBrandAdresses,
    fetchBrandCollection,
    fetchByScreenType,
    getByScreenType,
    navigateToSearch,
    fetchBrandDetail,
    setDataCollection,
    dispatch,
    handlePaging,
    setCollectionParams,
    navigateToBrandCollection,
    navigateToBrandDetails,
  };
};
