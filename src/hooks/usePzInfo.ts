import { useEffect, useState } from 'react';

import { getPzInfo } from '~/redux/reducers/info/pzInfo/thunks';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { IPzInfo } from '~/types/pzInfo';

export const usePzInfo = () => {
  const dispatch = useAppDispatch();
  const { isConnected } = useAppSelector((state) => state.network);
  const [loading, setLoading] = useState<boolean>(false);
  const [result, setResult] = useState<IPzInfo>();

  useEffect(() => {
    if (!isConnected) return;
    setLoading(true);
    dispatch(getPzInfo())
      .then((response) => {
        setResult(response);
        setLoading(false);
      })
      .catch((error) => {
        console.log(error);
      });
  }, [isConnected]);
  return { result, loading, isConnected };
};
