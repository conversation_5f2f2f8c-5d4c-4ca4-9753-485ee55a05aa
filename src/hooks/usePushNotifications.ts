import messaging from '@react-native-firebase/messaging';
import { useEffect } from 'react';

import { StorageEnum } from '~/enums/storage';
import { checkPushStatus, clearPushNotifications } from '~/helpers/pushNotification';
import { getItem, setItem } from '~/helpers/storage';
import { pushDeviceToken } from '~/redux/api/notifications';
import { useAppSelector } from '~/redux/store';

export const usePushNotifications = () => {
  const { notification_settings, token, me } = useAppSelector((state) => state.user);

  const requestUserPermission = async () => {
    await messaging().requestPermission();
  };

  const getDeviceToken = async () => {
    const deviceToken = await messaging().getToken();
    console.log('DEVICE TOKEN: ', deviceToken);
    return deviceToken;
  };

  const handleSetUpPushNotifications = async () => {
    if (checkPushStatus(notification_settings) === true) {
      requestUserPermission();
      getDeviceToken().then(async (res) => {
        const last_device = await getItem(StorageEnum.LAST_DEVICE_TOKEN);
        if (token?.access && last_device !== res) {
          pushDeviceToken({ token: res })
            .then(() => {
              setItem(StorageEnum.LAST_DEVICE_TOKEN, res);
            })
            .catch((err) => {
              console.log('PUSH NOTIFY ERR: ', err);
            });
        } else {
          clearPushNotifications({ userEmail: me?.email.toLowerCase() });
        }
      });
    } else {
      clearPushNotifications({ userEmail: me?.email.toLowerCase() });
    }
  };

  useEffect(() => {
    getItem(StorageEnum.LAST_LOGIN_EMAIL);
    handleSetUpPushNotifications();
  }, [token?.access, notification_settings]);
};
