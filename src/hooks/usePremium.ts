import { zodResolver } from '@hookform/resolvers/zod';
import axios from 'axios/index';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

import { SERVER_URL } from '~/config.api';
import { usePremiumCodeRegistartion } from '~/events/hooks/usePremiumCodeRegistration';
import { getPremium } from '~/redux/reducers/user/thunks';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { IStatus } from '~/types/errors';
import { formDefault } from '~/validation/premiumCode/Default';
import { formSchema } from '~/validation/premiumCode/Schema';
import { TForm } from '~/validation/premiumCode/types';

export const usePremium = () => {
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState<boolean>(false);
  const [status, setStatus] = useState<IStatus | undefined>(undefined);
  const { token, premium } = useAppSelector((state) => state.user);
  //----EVENTS----
  const _fnPremiumCodeRegistration = usePremiumCodeRegistartion();
  // --X--EVENTS--X--
  const form = useForm<TForm>({
    resolver: zodResolver(formSchema),
    defaultValues: formDefault,
    mode: 'onSubmit',
    reValidateMode: 'onChange',
  });
  const onSubmit = (data: TForm) => {
    setLoading(true);
    axios({
      method: 'POST',
      url: `${SERVER_URL}/content/premium/`,
      headers: {
        Accept: 'application/json',
        Authorization: `Bearer ${token?.access}`,
      },
      data: {
        code: data.code,
      },
    })
      .then(() => {
        _fnPremiumCodeRegistration(premium);
        dispatch(getPremium({ token: token?.access! }));
        setStatus({
          status: 'success',
          message: 'Kod został zaakceptowany',
        });
      })
      .catch(() => {
        setStatus({
          status: 'error',
          message: 'Kod jest nieprawidłowy',
        });
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return {
    form,
    onSubmit,
    loading,
    status,
  };
};
