import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { CategoryTypeEnum, ShopTypeEnum } from '~/enums/shared';
import { useFetchByContentType } from '~/hooks/useFetchByContentType';
import {
  setSelectedShop as actionSetSelectedShop,
  setSelectedSort as actionSetSelectedSort,
  clearFilters,
  countSelected,
  removeSelectedCategories,
  removeSelectedShop,
  removeSelectedSort,
  selectAll,
  selectCategory,
  selectShop,
  selectSort,
  setSelectedCategories,
} from '~/redux/reducers/content/categories/actions';
import {
  getCategories,
  getCategoriesWithStoreTypeChange,
} from '~/redux/reducers/content/categories/thunks';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { ICategoryShop, ICategorySort, IChildrenCategory, IParentCategory } from '~/types/category';
import { ParamsOptions } from '~/types/shared';

export const useFilters = () => {
  const dispatch = useAppDispatch();
  const { city_name: cityName, city: cityId } = useAppSelector((state) => state.user.detail);
  const {
    selectedSort,
    selectedShop,
    selectedCategories,
    sortCollection,
    shopCollection,
    categoryCollection,
  } = useAppSelector((state) => state.categories);
  const { navigateByType } = useFetchByContentType();

  const fetchFilterCategories = () => {
    return new Promise((resolve, reject) => {
      dispatch(getCategories())
        .then((res) => {
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });
  };
  const handleSortCollection = (item: ICategorySort) => {
    dispatch(selectSort(item.id));
    dispatch(
      actionSetSelectedSort({
        id: item.id,
        name: item.name,
        categoryType: CategoryTypeEnum.SORT,
        objectType: item.type,
      }),
    );
  };
  const handleShopCollection = (screenType: ObjectTypeEnum, item: ICategoryShop) => {
    dispatch(selectShop(item.id));
    dispatch(
      actionSetSelectedShop({
        id: item.id,
        name: item.name,
        categoryType: CategoryTypeEnum.SHOP,
        objectType: item.type,
      }),
    );
    if (item.type === ShopTypeEnum.ALL) {
      dispatch(getCategoriesWithStoreTypeChange({ content_type: screenType! }));
    } else {
      dispatch(
        getCategoriesWithStoreTypeChange({ content_type: screenType!, store_type: item.type }),
      );
    }
  };
  const handleCategoryCollection = (item: IChildrenCategory) => {
    dispatch(selectCategory(item.id));
    dispatch(
      setSelectedCategories({
        id: item.id,
        name: item.name,
        categoryType: CategoryTypeEnum.CATEGORY,
      }),
    );
  };
  const handleAll = (item: IParentCategory) => {
    dispatch(selectAll(item.id));
  };
  const submit = (screenType: ObjectTypeEnum) => {
    const payload: ParamsOptions = {
      category_ids: selectedCategories.map((item) => item.id),
      store_type:
        selectedShop?.objectType === ShopTypeEnum.ALL ? undefined : selectedShop?.objectType,
      sort_by: selectedSort?.objectType,
      city_id: cityId,
    };
    navigateByType(screenType, payload, RouteEnum.FILTER_MODAL_SCREEN);
    // dispatch(countSelected());
  };
  const clear = () => {
    dispatch(clearFilters());
    dispatch(countSelected());
  };
  const removeAllFilters = () => {
    dispatch(clearFilters());
  };
  const removeSingleFilter = (id: number, categoryType: CategoryTypeEnum) => {
    switch (categoryType) {
      case CategoryTypeEnum.CATEGORY:
        dispatch(selectCategory(id));
        dispatch(removeSelectedCategories({ id }));
        break;
      case CategoryTypeEnum.SHOP:
        dispatch(selectShop(id));
        dispatch(removeSelectedShop());
        break;
      case CategoryTypeEnum.SORT:
        dispatch(selectSort(id));
        dispatch(removeSelectedSort());
        break;
      default:
        break;
    }
  };

  return {
    cityName,
    cityId,
    selectedCategories,
    selectedShop,
    selectedSort,
    sortCollection,
    shopCollection,
    categoryCollection,
    fetchFilterCategories,
    handleSortCollection,
    handleShopCollection,
    handleCategoryCollection,
    handleAll,
    submit,
    clear,
    dispatch,
    removeAllFilters,
    removeSingleFilter,
    selectCategory,
    removeSelectedCategories,
  };
};
