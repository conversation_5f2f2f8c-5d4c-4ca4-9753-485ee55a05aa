import { RouteProp, useRoute } from '@react-navigation/native';
import { useLayoutEffect } from 'react';

import { privacyDetailClear } from '~/redux/reducers/privacy/actions';
import { getConsents, getLegal } from '~/redux/reducers/privacy/thunks';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { RootStackParamList } from '~/routes/types';

export const usePolicy = () => {
  const dispatch = useAppDispatch();
  const { params } = useRoute<RouteProp<RootStackParamList, RouteEnum.POLICY>>();
  const consent = useAppSelector((state) => state.privacy).consents.find(
    (item) => item.code_name === params.type,
  );
  const { legal } = useAppSelector((state) => state.privacy);

  useLayoutEffect(() => {
    if (!consent?.legal_content) {
      dispatch(getConsents()).then((res) => {
        dispatch(
          getLegal({ id: res.find((item) => item.code_name === params.type)?.legal_content! }),
        ).catch(() => {});
      });
    } else {
      dispatch(getLegal({ id: consent.legal_content })).catch(() => {});
    }

    return () => {
      dispatch(privacyDetailClear());
    };
  }, []);

  return {
    legal,
    params,
  };
};
