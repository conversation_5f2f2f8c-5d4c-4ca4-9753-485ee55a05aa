import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

import { getTokenApi, postUserCreate } from './../redux/api/user';
import { useNavigate } from './useNavigate';

import { useUserAccountCreate } from '~/events/hooks/useUserAccountCreate';
import { useUserLoginStatus } from '~/events/hooks/useUserLoginStatus';
import { IStatus } from '~/pages/Auth/pages/Registration/types';
import { getConsents } from '~/redux/reducers/privacy/thunks';
import { userTokenSuccess } from '~/redux/reducers/user/actions';
import { getMe, getPremium, getProfile } from '~/redux/reducers/user/thunks';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { UserCreate } from '~/types/user';
import { formDefault } from '~/validation/registrationForm/Default';
import { formSchema } from '~/validation/registrationForm/Schema';
import { TForm } from '~/validation/registrationForm/types';

export const useRegistration = () => {
  //----EVENTS----
  const _fnUserAccountCreate = useUserAccountCreate();
  const _fnLoginStatus = useUserLoginStatus();
  //--X--EVENTS--X--
  const dispatch = useAppDispatch();
  const { navigation } = useNavigate();
  const { consents } = useAppSelector((state) => state.privacy);
  const [loader, setLoader] = useState<boolean>(false);
  const [status, setStatus] = useState<IStatus | undefined>();
  const [verificationCode] = useState<boolean>(false);
  const form = useForm<TForm>({
    resolver: zodResolver(formSchema),
    defaultValues: formDefault,
    mode: 'onSubmit',
    reValidateMode: 'onChange',
  });

  useEffect(() => {
    if (consents.length === 0) {
      dispatch(getConsents()).catch(() => {});
    }
  }, []);
  const handleRegistration = async (data: TForm) => {
    try {
      setStatus(undefined);
      setLoader(true);

      // Nie potrzebujemy już trim() tutaj, ponieważ jest wykonywany na poziomie walidacji
      const sendData: UserCreate = {
        email: data.email,
        password: data.password,
        consents: data.consents,
        profile: {
          first_name: data.name,
          phone_number: data.phone,
        },
      };

      await postUserCreate(sendData);
      const { data: token } = await getTokenApi({
        email: data.email,
        password: data.password,
      });
      setLoader(false);
      dispatch(userTokenSuccess({ access: token.access, refresh: token.refresh }));
      _fnUserAccountCreate(data.consents, sendData, token.access);
      _fnLoginStatus(token.access);
      dispatch(getProfile({ token: token.access })).catch(() => {});
      dispatch(getPremium({ token: token.access })).catch(() => {});
      dispatch(getMe({ token: token.access })).catch(() => {});
      navigation.navigate(RouteEnum.ONBOARDING);
    } catch (err: any) {
      if (err.response.status === 400) {
        setStatus({
          status: 'error',
          message: 'Konto o podanym adresie email już istnieje.',
        });
      } else {
        setStatus({
          status: 'error',
          message: 'Wystąpił błąd podczas rejestracji. Spróbuj ponownie później.',
        });
      }
    } finally {
      setLoader(false);
    }
    // TODO IF ADD VERIFICATION CODE
    // if (data.verification_code) {
    //   sendData.verification_code = data.verification_code;
    // }
  };

  const handleSubmit = (data: TForm) => {
    // Trim all fields before passing to handleRegistration
    const trimmedData = {
      ...data,
      email: data.email.toLowerCase().trim(),
      password: data.password.trim(),
      name: data.name.trim(),
      phone: data.phone.trim(),
      confirmPassword: data.confirmPassword.trim(),
    };

    handleRegistration(trimmedData).catch(() => {});
    // TODO IF ADD VERIFICATION CODE
    // handleGetOTC(data.email.toLowerCase().trim(), 'registration')
    //   .then(() => {
    //     setVerificationCode(true);
    //   })
    //   .catch(() => {
    //     setVerificationCode(false);
    //     setStatus({
    //       status: 'error',
    //       message:
    //         'Wystąpił błąd podczas wysyłania kodu weryfikacyjnego. Spróbuj ponownie później.',
    //     });
    //   });
  };

  return {
    form,
    loader,
    status,
    verificationCode,
    setStatus,
    handleSubmit,
    handleRegistration,
  };
};
