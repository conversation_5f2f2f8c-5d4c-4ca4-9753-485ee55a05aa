import { useNavigate } from './useNavigate';

import { getActiveSections } from '~/redux/reducers/info/activeSections/thunks';
import { getConsents } from '~/redux/reducers/privacy/thunks';
import { userSession } from '~/redux/reducers/states/actions';
import { SessionStates } from '~/redux/reducers/states/SessionStates';
import { userLogout } from '~/redux/reducers/user/actions';
import { postRefreshToken } from '~/redux/reducers/user/thunks';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { IToken } from '~/types/user';

export const useSplash = () => {
  const dispatch = useAppDispatch();
  const { token: oldToken } = useAppSelector((state) => state.user);
  const { isConnected } = useAppSelector((state) => state.network);
  const { navigation } = useNavigate();

  const navigateToLogin = () => {
    dispatch(getConsents())
      .then(() => {
        dispatch(getActiveSections());
      })
      .catch(() => {})
      .finally(() => {
        navigation.reset({
          index: 0,
          routes: [{ name: RouteEnum.LOGIN }],
        });
      });
  };

  const navigateToHome = () => {
    dispatch(getActiveSections()).finally(() => {
      navigation.reset({
        index: 0,
        routes: [{ name: RouteEnum.HOME }],
      });
    });
  };

  const handleTokenRefresh = (token: IToken | null): void => {
    if (token) {
      dispatch(postRefreshToken({ refresh: token.refresh }))
        .then(() => {
          navigateToHome();
          dispatch(userSession(SessionStates.AUTHENTICATED));
        })
        .catch((err) => {
          if (err.response.status === 401) {
            dispatch(userLogout());
            dispatch(userSession(SessionStates.UNAUTHENTICATED));
          }
          navigateToLogin();
          dispatch(userSession(SessionStates.EXPIRED));
        });
    } else {
      navigateToLogin();
    }
  };
  return { handleTokenRefresh, dispatch, navigation, oldToken, isConnected };
};
