import { zodResolver } from '@hookform/resolvers/zod';
import { useContext, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { Linking } from 'react-native';

import { SERVER_URL } from '~/config.api';
import { contactModalContext } from '~/context/contactModalContext';
import { useNavigate } from '~/hooks/useNavigate';
import { getInfo, getTopics } from '~/redux/reducers/info/contact/thunks';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { IStatus } from '~/types/errors';
import axios from '~/utils/axios.config';
import { formDefault } from '~/validation/contactForm/Default';
import { formSchema } from '~/validation/contactForm/Schema';
import { TForm } from '~/validation/contactForm/types';

export const useContact = () => {
  const dispatch = useAppDispatch();
  const { topics, info, loading } = useAppSelector((state) => state.contact);
  const [status, setStatus] = useState<IStatus | undefined>();
  const [button, setButton] = useState(false);
  const { navigation, navigateBack } = useNavigate();
  const { setShow } = useContext(contactModalContext);

  const form = useForm<TForm>({
    resolver: zodResolver(formSchema),
    defaultValues: formDefault,
    mode: 'onSubmit',
    reValidateMode: 'onChange',
  });

  const onSubmit = (data: TForm) => {
    axios({
      method: 'POST',
      url: `${SERVER_URL}/content/contact/`,
      headers: {
        Accept: 'application/json',
      },
      data: {
        email: data.email.toLowerCase(),
        topic: data.topic,
        message: data.message,
      },
    })
      .then(() => {
        setShow(true);
        navigateBack();
      })
      .catch(() => {
        setStatus({
          status: 'error',
          message: 'Nie udało się wysłać wiadomości',
        });
      });
  };

  const handlePhonePress = () => {
    if (info?.phone) {
      Linking.openURL(`tel:${info.phone}`).catch();
    }
  };
  const handleEmailPress = () => {
    if (info?.email) {
      Linking.openURL(`mailto:${info.email.toLowerCase()}`).catch();
    }
  };

  useEffect(() => {
    if (form.watch().email && form.watch().topic && form.watch().message) {
      setButton(true);
    } else {
      setButton(false);
    }
  }, [form.watch()]);

  useEffect(() => {
    if (info === null) {
      dispatch(getInfo());
    }
    if (topics.length === 0) {
      dispatch(getTopics());
    }
  }, []);

  return {
    form,
    onSubmit,
    status,
    button,
    topics,
    info,
    loading,
    navigation,
    handlePhonePress,
    handleEmailPress,
  };
};
