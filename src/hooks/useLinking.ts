import Bugsnag from '@bugsnag/react-native';
import { Linking, Platform } from 'react-native';

const PHONE_ERROR_MSG = 'Pojawił się problem z otworzeniem aplikacji do wykonywania połączeń.';
const MAP_ERROR_MSG = 'Pojawił się problem z otworzeniem aplikacji do wyświetlania mapy.';
const URL_ERROR_MSG = 'Pojawił się problem z otworzeniem strony internetowej.';

export const useLinking = () => {
  const validate = async (url: string): Promise<boolean> => {
    return Linking.canOpenURL(url)
      .then((supported) => {
        return supported;
      })
      .catch(() => {
        return false;
      });
  };

  const openPhone = async (phone: string): Promise<void> => {
    let phoneNumber: string;
    if (Platform.OS !== 'android') {
      phoneNumber = `tel:${phone}`.replace(/\s/g, '');
    } else {
      phoneNumber = `tel:${phone}`;
    }
    Linking.openURL(phoneNumber).catch(() => {
      Bugsnag.notify(new Error(PHONE_ERROR_MSG));
    });
  };
  const openEmail = async (email: string): Promise<void> => {
    const url = `mailto:${email.toLowerCase()}`;
    Linking.openURL(url).catch(() => {});
  };

  const openMap = async (latitude: number, longitude: number): Promise<void> => {
    const scheme = Platform.select({ ios: 'maps://0,0?q=', android: 'geo:0,0?q=' });
    const latLng = `${latitude},${longitude}`;
    const url = Platform.select({
      ios: `${scheme}@${latLng}`,
      android: `${scheme}${latLng}`,
    });
    if (url) {
      Linking.openURL(url).catch(() => {
        Bugsnag.notify(new Error(MAP_ERROR_MSG));
      });
    }
  };

  const openUrl = async (url: string | undefined | null): Promise<void> => {
    if (!url) return;
    Linking.openURL(url).catch(() => {
      Bugsnag.notify(new Error(URL_ERROR_MSG));
    });
  };

  return { validate, openPhone, openMap, openEmail, openUrl };
};
