import { zodResolver } from '@hookform/resolvers/zod';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

import { SERVER_URL } from '~/config.api';
import { usePasswordReset as usePasswordResetEvent } from '~/events/hooks/usePasswordReset';
import { handleGetOTC as getOTC } from '~/helpers/handleGetOTC';
import { IStatus } from '~/types/errors';
import axios from '~/utils/axios.config';
import { formDefault } from '~/validation/passwordResetForm/Default';
import { formSchema } from '~/validation/passwordResetForm/Schema';
import { TForm } from '~/validation/passwordResetForm/types';

export const usePasswordReset = () => {
  const [verificationCode, setVerificationCode] = useState<boolean>(false);
  const [status, setStatus] = useState<IStatus | undefined>();
  const event = usePasswordResetEvent();
  const form = useForm<TForm>({
    resolver: zod<PERSON><PERSON>olver(formSchema),
    defaultValues: formDefault,
    mode: 'onSubmit',
    reValidateMode: 'onChange',
  });

  const handleGetOTC = (data: TForm) => {
    getOTC(data.email.toLowerCase(), 'password_reset')
      .then(() => {
        setVerificationCode(true);
        setStatus(undefined);
      })
      .catch(() => {
        setStatus({
          status: 'error',
          message: 'Nie udało się wysłać kodu weryfikacyjnego. Sprawdź poprawność adresu email.',
        });
      });
  };

  const handlePasswordReset = async (data: TForm) => {
    axios({
      method: 'POST',
      url: `${SERVER_URL}/user/me/passreset/`,
      headers: {
        Accept: 'application/json',
      },
      data: {
        email: data.email.toLowerCase(),
        verification_code: data.verification_code,
      },
    })
      .then(() => {
        setStatus({
          status: 'success',
          message: 'Poprawnie zresetowano hasło. Sprawdź skrzynkę email.',
        });
        event.handlePasswordReset();
      })
      .catch(() => {
        setStatus({
          status: 'error',
          message: 'Nie udało się zresetować hasła. Sprawdź poprawność kodu weryfikacyjnego.',
        });
      });
  };

  return {
    form,
    verificationCode,
    status,
    setStatus,
    handleGetOTC,
    handlePasswordReset,
  };
};
