import Bugsnag from '@bugsnag/react-native';
import React, { ReactElement, useContext, useEffect, useState } from 'react';
import { View } from 'react-native';

import { ArticleCard } from '~/components/Cards/ArticleCard/ArticleCard';
import { BrandCard } from '~/components/Cards/BrandCard/BrandCard';
import { CouponCard } from '~/components/Cards/CouponCard/CouponCard';
import { DiscountCard } from '~/components/Cards/DiscountCard/DiscountCard';
import { NewspaperCard } from '~/components/Cards/NewspaperCard/NewspaperCard';
import { InspirationImage } from '~/components/Inspiration/components/InspirationImage/InspirationImage';
import { FilterContext } from '~/context/filterContext';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useFavoritesQuery } from '~/hooks/useFavorites';
import { useFetchByContentType } from '~/hooks/useFetchByContentType';
import { useNavigate } from '~/hooks/useNavigate';
import { ISearchItem } from '~/pages/Search/Search';
import { clearPopularSearchesValue } from '~/redux/reducers/content/search/actions';
import { getSearch } from '~/redux/reducers/content/search/thunks';
import { setUserSearches } from '~/redux/reducers/content/userSearch/actions';
import { useAppDispatch } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { IArticle } from '~/types/article';
import { IBrandDetail } from '~/types/brands';
import { ICoupon } from '~/types/coupon';
import { IDiscount } from '~/types/discount';
import { IInspiration } from '~/types/inspirations';
import { INewspaper } from '~/types/newspaper';
import { ISearchResultData } from '~/types/search';
import { ParamsOptions } from '~/types/shared';

export const useSearch = () => {
  const { navigateByType } = useFetchByContentType();
  const { navigation, navigateToBrandDetails, navigateToDiscountDetails, navigateToCouponDetails } =
    useNavigate();
  const { fetchByType, dynamicSelector } = useFetchByContentType();
  const dispatch = useAppDispatch();
  const [searchResult, setSearchResult] = useState<ISearchResultData[]>([]);

  const { postFavorite } = useFavoritesQuery();
  const _filterContext = useContext(FilterContext);
  const onNavigation = (type: ObjectTypeEnum, params: ParamsOptions, prevScreen: RouteEnum) => {
    navigateByType(type, params, prevScreen);
  };

  const renderContent = (
    screenType: ObjectTypeEnum,
    item: ISearchItem,
    index: number,
  ): ReactElement => {
    switch (screenType) {
      case ObjectTypeEnum.Coupon:
        const couponData = item as ICoupon;
        return (
          <CouponCard
            onFavoritePress={(objectId, isFavorite, categories, tags) => {
              postFavorite({
                isFavorite,
                params: {
                  payload: {
                    object_id: objectId,
                    object_type: ObjectTypeEnum.Coupon,
                  },
                  type: 'Coupon',
                },
                event: {
                  object_type: ObjectTypeEnum.Coupon,
                  categories_id_list: categories || [],
                  tags_id_list: tags || [],
                },
              });
            }}
            data={couponData}
            onPress={() => navigateToCouponDetails(couponData.id)}
          />
        );
      case ObjectTypeEnum.Card:
        const discountData = item as IDiscount;
        return (
          <DiscountCard
            onFavoritePress={(objectId, isFavorite, categories, tags) => {
              postFavorite({
                isFavorite,
                params: {
                  payload: {
                    object_id: objectId,
                    object_type: ObjectTypeEnum.Card,
                  },
                  type: 'Card',
                },
                event: {
                  object_type: ObjectTypeEnum.Card,
                  categories_id_list: categories || [],
                  tags_id_list: tags || [],
                },
              });
            }}
            key={index}
            data={discountData}
            onPress={() => navigateToDiscountDetails(discountData.id)}
          />
        );
      case ObjectTypeEnum.Article:
        const articleData = item as IArticle;
        return (
          <ArticleCard
            onFavoritePress={(objectId, isFavorite, categories, tags) => {
              postFavorite({
                isFavorite,
                params: {
                  payload: {
                    object_id: objectId,
                    object_type: ObjectTypeEnum.Article,
                  },
                  type: 'Article',
                },
                event: {
                  object_type: ObjectTypeEnum.Article,
                  categories_id_list: categories || [],
                  tags_id_list: tags || [],
                },
              });
            }}
            data={articleData}
            key={index}
            onPress={() => navigation.navigate(RouteEnum.ARTICLE_DETAIL, { id: articleData.id })}
          />
        );
      case ObjectTypeEnum.Leaflet:
        const leafletData = item as INewspaper;
        return (
          <NewspaperCard
            onFavoritePress={(objectId, isFavorite, categories, tags) => {
              postFavorite({
                isFavorite,
                params: {
                  payload: {
                    object_id: objectId,
                    object_type: ObjectTypeEnum.Leaflet,
                  },
                  type: 'Leaflet',
                },
                event: {
                  object_type: ObjectTypeEnum.Leaflet,
                  categories_id_list: categories || [],
                  tags_id_list: tags || [],
                },
              });
            }}
            data={leafletData}
            key={index}
            onPress={() => navigation.navigate(RouteEnum.NEWSPAPER_DETAIL, { id: leafletData.id })}
          />
        );
      case ObjectTypeEnum.Brand:
        const brandData = item as IBrandDetail;
        return (
          <BrandCard
            data={brandData}
            onFavoritePress={(objectId, isFavorite, categories, tags) => {
              postFavorite({
                isFavorite,
                params: {
                  payload: {
                    object_id: objectId,
                    object_type: ObjectTypeEnum.Brand,
                  },
                  type: 'Brand',
                },
                event: {
                  object_type: ObjectTypeEnum.Brand,
                  categories_id_list: categories || [],
                  tags_id_list: tags || [],
                },
              });
            }}
            onPress={() => navigateToBrandDetails(item.id, item.is_rec)}
          />
        );
      case ObjectTypeEnum.Inspiration:
        const inspirationData = item as IInspiration;
        return (
          <View style={{ width: 170, height: 132 }}>
            <InspirationImage
              onFavoritePress={(objectId, isFavorite, categories, tags) =>
                postFavorite({
                  isFavorite,
                  params: {
                    payload: {
                      object_id: objectId,
                      object_type: ObjectTypeEnum.Inspiration,
                    },
                    type: 'Inspiration',
                  },
                  event: {
                    object_type: ObjectTypeEnum.Inspiration,
                    categories_id_list: categories || [],
                    tags_id_list: tags || [],
                  },
                })
              }
              key={index}
              data={inspirationData}
              onPress={() =>
                navigation.navigate(RouteEnum.INSPIRATION_DETAIL, { id: inspirationData.id })
              }
            />
          </View>
        );
      default:
        return <></>;
    }
  };

  const categoryName = (screenType: ObjectTypeEnum) => {
    const obj: Record<ObjectTypeEnum, string> = {
      [ObjectTypeEnum.Coupon]: 'Okazji',
      [ObjectTypeEnum.Card]: 'Rabaty HomeProfit',
      [ObjectTypeEnum.Article]: 'Artykułów',
      [ObjectTypeEnum.Leaflet]: 'Gazetek',
      [ObjectTypeEnum.Brand]: 'Marek',
      [ObjectTypeEnum.Inspiration]: 'Inspiracji',
    };
    return obj[screenType];
  };

  const onChange = (val: string, screenType: ObjectTypeEnum) => {
    fetchSearchCollection(val, screenType)
      .then(() => {})
      .catch(() => {});
  };

  const onSubmit = (val: ISearchResultData | string, screenType: ObjectTypeEnum) => {
    if (typeof val === 'string') {
      fetchSearchCollection(val, screenType)
        .then((res) => {
          if (res) {
            fetchSubmit(res[0], screenType);
            dispatch(setUserSearches(res[0]));
          }
        })
        .catch(() => {});
    } else {
      fetchSubmit(val, screenType);
    }
  };

  const fetchSearchCollection = async (val: string, screenType: ObjectTypeEnum) => {
    if (val.length < 3 && val.length !== 0) return;
    if (val.length === 0) fetchByType(screenType);
    return await getSearch({ phrase: val, content_type: screenType })
      .then((res) => {
        setSearchResult(res);
        return res;
      })
      .catch((err) => {
        Bugsnag.notify(new Error(err));
      });
  };

  const fetchSubmit = (val: ISearchResultData, screenType: ObjectTypeEnum) => {
    _filterContext.handleSearchId(val.id);
    _filterContext.handleSearchString(val.name);
    navigateByType(screenType, { search_id: val.id, search_string: val.name }, RouteEnum.SEARCH);
  };

  /*
   * Clear popular searches value on unmount
   */
  useEffect(() => {
    return () => {
      dispatch(clearPopularSearchesValue());
    };
  }, []);

  return {
    searchResult,
    renderContent,
    categoryName,
    onChange,
    onSubmit,
    fetchSearchCollection,
    fetchSubmit,
    fetchByType,
    dynamicSelector,
    onNavigation,
  };
};
