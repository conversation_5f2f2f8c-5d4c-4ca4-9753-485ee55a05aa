// import { RouteProp, useRoute } from '@react-navigation/native';
// import { useState } from 'react';

// import { checkCollectionType } from '~/helpers/checkCollectionType';
// import { getPagingCollection } from '~/helpers/getPagingCollection';
// import { useNavigate } from '~/hooks/useNavigate';
// import {
//   discountCodeClear,
//   discountPage,
//   discountsMayInterestYou,
// } from '~/redux/reducers/content/discounts/actions';
// import {
//   getDiscountAddresses,
//   getDiscountCode,
//   getDiscountDetail,
//   getDiscounts,
//   getFilteredDiscounts,
//   getSimilarDiscounts,
// } from '~/redux/reducers/content/discounts/thunks';
// import { useAppDispatch, useAppSelector } from '~/redux/store';
// import { RouteEnum } from '~/routes/routes';
// import { RootStackParamList } from '~/routes/types';
// import { IDiscountCollections } from '~/types/discount';
// import { ParamsOptions } from '~/types/shared';

// export const useDiscounts = () => {
//   const dispatch = useAppDispatch();
//   const { isConnected } = useAppSelector((state) => state.network);
//   const [pagingLoading, setPagingLoading] = useState(false);
//   const { collection, loading, detail, similar, filteredDiscounts, mayInterestYou } =
//     useAppSelector((state) => state.discounts);
//   const [dataCollection, setDataCollection] = useState<IDiscountCollections>({
//     count: 0,
//     previous: null,
//     next: null,
//     results: [],
//   });

//   const { params: routeCollectionParams } =
//     useRoute<RouteProp<RootStackParamList, RouteEnum.DISCOUNT>>();
//   const { params: detailParams } =
//     useRoute<RouteProp<RootStackParamList, RouteEnum.DISCOUNT_DETAIL>>();
//   const { navigateToDiscountCollection, navigateToDiscountDetails, navigateToSearch } =
//     useNavigate();

//   const [collectionParams, setCollectionParams] = useState<
//     { params: ParamsOptions; prevScreen: RouteEnum } | undefined
//   >(routeCollectionParams);

//   const fetchDiscountCollection = (): Promise<IDiscountCollections> => {
//     return new Promise((resolve, reject) => {
//       dispatch(getDiscounts())
//         .then((res) => {
//           dispatch(discountsMayInterestYou(res.results.slice(0, 5)));
//           resolve(res);
//         })
//         .catch((error) => {
//           reject(error);
//         });
//     });
//   };
//   const fetchDiscountAdresses = async (id: number) => {
//     dispatch(getDiscountAddresses({ id: id }));
//   };
//   const fetchDiscountDetail = async (id: number) => {
//     return await dispatch(getDiscountDetail({ id: id }))
//       .then((res) => {
//         fetchSimilarDiscounts({ category_ids: res.categories });
//         res.discounts.forEach((discount) => {
//           dispatch(getDiscountCode({ cardId: id, discountId: discount.id }));
//         });
//         return res;
//       })
//       .catch((err) => {
//         throw err;
//       });
//   };
//   const fetchSimilarDiscounts = (params: ParamsOptions) => {
//     dispatch(getSimilarDiscounts(params))
//       .then((res) => {
//         setDataCollection(res);
//       })
//       .catch((err) => {
//         console.log(err);
//       });
//   };
//   const fetchByScreenType = async (screenType: RouteEnum, params: ParamsOptions) => {
//     const obj = {
//       [RouteEnum.COUPON_DETAIL]: async () => {
//         return await dispatch(getSimilarDiscounts(params));
//       },
//       [RouteEnum.DISCOUNT_DETAIL]: async () => {
//         return await dispatch(getSimilarDiscounts(params));
//       },
//       // TODO: hide search button
//       // [RouteEnum.SEARCH]: async () => {
//       //   return await dispatch(getFilteredDiscounts(params));
//       // },
//       [RouteEnum.FILTER_MODAL_SCREEN]: async () => {
//         return await dispatch(getFilteredDiscounts(params));
//       },
//     };
//     return await obj[screenType]();
//   };
//   const getByScreenType = (screenType: RouteEnum) => {
//     const obj = {
//       [RouteEnum.COUPON_DETAIL]: similar.discounts,
//       [RouteEnum.DISCOUNT_DETAIL]: similar.discounts,
//       // TODO: hide search button
//       // [RouteEnum.SEARCH]: filteredDiscounts,
//       [RouteEnum.FILTER_MODAL_SCREEN]: filteredDiscounts,
//     };
//     setDataCollection(obj[screenType]);
//   };

//   const handlePaging = (screenType?: RouteEnum) => {
//     if (isConnected && dataCollection.next && dataCollection.next !== null) {
//       setPagingLoading(true);
//       getPagingCollection<IDiscountCollections>({
//         url: dataCollection.next,
//         payload: collectionParams?.params,
//       })
//         .then((res) => {
//           dispatch(
//             discountPage({
//               listType: checkCollectionType(screenType),
//               collection: res.data,
//             }),
//           );
//           setDataCollection({
//             count: res.data.count,
//             next: res.data.next,
//             previous: res.data.previous,
//             results: [...dataCollection.results, ...res.data.results],
//           });
//           setPagingLoading(false);
//         })
//         .catch((err) => {
//           console.log(err);
//           setPagingLoading(false);
//         });
//     }
//   };

//   return {
//     loading,
//     filteredDiscounts,
//     similar,
//     pagingLoading,
//     routeCollectionParams,
//     collection: collectionParams ? filteredDiscounts : collection,
//     similarDiscounts: similar.discounts,
//     initialCollection: collection,
//     detail,
//     detailParams,
//     collectionParams,
//     dataCollection,
//     mayInterestYou,
//     fetchDiscountAdresses,
//     fetchByScreenType,
//     getByScreenType,
//     navigateToDiscountCollection,
//     navigateToDiscountDetails,
//     navigateToSearch,
//     fetchDiscountDetail,
//     fetchSimilarDiscounts,
//     fetchDiscountCollection,
//     setDataCollection,
//     dispatch,
//     handlePaging,
//     setCollectionParams,
//   };
// };
// export const useDiscountCode = () => {
//   const dispatch = useAppDispatch();
//   const discount = useAppSelector((state) => state.discounts);

//   return {
//     discountCode: discount.discountCode,
//     error: discount.error,
//     getDiscountCode: async (payload: { id: number }) => {
//       return dispatch(getDiscountCode({ discountId: payload.id }));
//     },
//     clearDiscountCode: () => {
//       dispatch(discountCodeClear());
//     },
//   };
// };
