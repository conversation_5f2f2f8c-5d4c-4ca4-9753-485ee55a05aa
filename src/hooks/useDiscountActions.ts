import { prepareDiscountPrice } from '~/components/Cards/helpers/prepareData';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { DiscountTypeEnum, IIntendedForEnum } from '~/enums/shared';
import { useNavigate } from '~/hooks/useNavigate';
import { sortDiscountButtons } from '~/pages/Discount/helpers/filterDiscounts';
import { useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { IDiscountDetail, ISingleDiscount } from '~/types/discount';

export const useDiscountActions = () => {
  const { navigation } = useNavigate();
  //const { clearDiscountCode, error } = useDiscountCode();
  const { token } = useAppSelector((state) => state.user);

  const filterDiscounts = (
    discounts: ISingleDiscount[],
    userType: IIntendedForEnum,
  ): ISingleDiscount[] => {
    return sortDiscountButtons(discounts, userType);
  };

  const handleOpenModal = async (
    payload: ISingleDiscount,
    card: IDiscountDetail,
    code: string,
  ): Promise<void> => {
    const data = prepareDiscountPrice(payload.discount_value_type!, payload.discount_value);
    if (token) {
      if (payload.discount_type === DiscountTypeEnum.Ins) {
        navigation.navigate(RouteEnum.DISCOUNT_MODAL_SCREEN, {
          code: code,
          data: {
            ...payload,
            discount_value: `${data.price}${data.unit}`,
            brand_id: card.brand.id,
            object_id: card.id,
            object_type: ObjectTypeEnum.Card,
            discount_id: payload.id,
            card_id: card.id,
          },
        });
      } else {
        if (payload.discount_type === DiscountTypeEnum.Cal) {
          navigation.navigate(RouteEnum.DISCOUNT_MODAL_SCREEN, {
            code: code,
            data: {
              ...payload,
              discount_value: `${data.price}${data.unit}`,
              brand_id: card.brand.id,
              object_id: card.id,
              object_type: ObjectTypeEnum.Card,
              discount_id: payload.id,
              card_id: card.id,
            },
          });
        } else {
          navigation.navigate(RouteEnum.DISCOUNT_MODAL_SCREEN, {
            code: code,
            data: {
              ...payload,
              discount_value: `${data.price}${data.unit}`,
              brand_id: card.brand.id,
              object_id: card.id,
              object_type: ObjectTypeEnum.Card,
              discount_id: payload.id,
              card_id: card.id,
            },
          });
        }
      }
    } else {
      navigation.navigate(RouteEnum.AUTH_MODAL);
    }
  };
  const handleCloseModal = (): void => {
    navigation.goBack();
    //clearDiscountCode();
  };

  return {
    //error,
    filterDiscounts,
    handleOpenModal,
    handleCloseModal,
  };
};
