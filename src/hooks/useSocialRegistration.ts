import { zodResolver } from '@hookform/resolvers/zod';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

import { SERVER_URL } from '~/config.api';
import { useNavigate } from '~/hooks/useNavigate';
import { getProfile } from '~/redux/reducers/user/thunks';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import axios from '~/utils/axios.config';
import { formSchema } from '~/validation/socialRegistration/Schema';
import { TForm } from '~/validation/socialRegistration/types';

export const useSocialRegistration = () => {
  const dispatch = useAppDispatch();
  const { navigation } = useNavigate();
  const { me, detail, token, onboarding } = useAppSelector((state) => state.user);
  const [loader, setLoader] = useState<boolean>(false);

  const form = useForm<TForm>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: detail?.first_name,
      email: me?.email.toLowerCase(),
      phone: detail?.phone_number,
      consents: false,
    },
    mode: 'onSubmit',
    reValidateMode: 'onChange',
  });

  const onSubmit = async (data: TForm) => {
    setLoader(true);

    // Trim input fields before sending
    const trimmedName = data.name.trim();
    const trimmedPhone = data.phone.trim();

    await axios<any>({
      method: 'PUT',
      url: `${SERVER_URL}/user/me/profile/`,
      headers: {
        Accept: 'application/json',
      },
      data: {
        first_name: trimmedName,
        phone_number: trimmedPhone,
      },
    })
      .then(async () => {
        await dispatch(getProfile({ token: token?.access! }));
      })
      .finally(() => {
        navigation.navigate(onboarding ? RouteEnum.ONBOARDING : RouteEnum.HOME);
      })
      .catch(() => {
        setLoader(false);
      });
  };

  return {
    form,
    onSubmit,
    loader,
  };
};
