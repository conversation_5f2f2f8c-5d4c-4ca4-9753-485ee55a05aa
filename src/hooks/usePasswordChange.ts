import { zodResolver } from '@hookform/resolvers/zod';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

import { SERVER_URL } from '~/config.api';
import { usePasswordChange as usePasswordChangeEvent } from '~/events/hooks/usePasswordChange';
import { useNavigate } from '~/hooks/useNavigate';
import { useAppSelector } from '~/redux/store';
import { IStatus } from '~/types/errors';
import { IPremium } from '~/types/user';
import axios from '~/utils/axios.config';
import { formDefault } from '~/validation/passwordChange/Default';
import { formSchema } from '~/validation/passwordChange/Schema';
import { TForm } from '~/validation/passwordChange/types';

export const usePasswordChange = () => {
  const { navigation } = useNavigate();
  const [loading, setLoading] = useState<boolean>(false);
  const [status, setStatus] = useState<IStatus | undefined>();
  const { token } = useAppSelector((state) => state.user);
  //EVENTS
  const { handlePasswordChange } = usePasswordChangeEvent();

  const form = useForm<TForm>({
    resolver: zodResolver(formSchema),
    defaultValues: formDefault,
    mode: 'onSubmit',
    reValidateMode: 'onChange',
  });

  const onSubmit = (data: TForm) => {
    setLoading(true);
    setStatus(undefined);
    axios<IPremium>({
      method: 'PATCH',
      url: `${SERVER_URL}/user/me/`,
      headers: {
        Accept: 'application/json',
        Authorization: `Bearer ${token?.access}`,
      },
      data: {
        password: data.newPassword,
        old_password: data.password,
      },
    })
      .then(() => {
        setStatus({
          status: 'success',
          message: 'Hasło zostało poprawnie zmienione',
        });
        setLoading(false);

        //EVENTS
        handlePasswordChange();
      })
      .catch(() => {
        setStatus({
          status: 'error',
          message: 'Wystąpił błąd podczas zmiany hasła. Upewnij się, że podałeś poprawne hasło.',
        });
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return {
    form,
    onSubmit,
    loading,
    status,
    navigation,
  };
};
