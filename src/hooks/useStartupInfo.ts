import { useEffect, useState } from 'react';
import { StorageEnum } from '~/enums/storage';
import { filterShowOnce } from '~/helpers/startupInfo';
import { getItem } from '~/helpers/storage';
import { useGetStartupInfoQuery } from '~/redux/api/startupInfo';
import { IStartupInfo } from '~/types/startupInfo';

export const useStartupInfo = () => {
  const { data: queryData, isLoading } = useGetStartupInfoQuery();
  const [data, setData] = useState<Array<IStartupInfo>>([]);

  const handleGetStartupInfo = async () => {
    const filteredData = await filterShowOnce(queryData || []);
    console.log('filteredData', filteredData);
    return filteredData;
  };

  useEffect(() => {
    console.log('queryData', queryData);
    getItem(StorageEnum.STARTUP_INFO_ITEMS).then((res) => {
      console.log('🚀 ~ getItem ~ res:', res);
    });

    handleGetStartupInfo().then((res) => {
      setData(res);
    });
  }, [queryData]);

  return {
    data,
    isLoading,
  };
};
