import { RouteProp, useRoute } from '@react-navigation/native';
import { useMemo, useState } from 'react';

import { usePagination } from './usePagination';

import { SERVER_URL } from '~/config.api';
import { useUserCityChange } from '~/events/hooks/useUserCityChange';
import { useNavigate } from '~/hooks/useNavigate';
import { useGetCitiesQuery } from '~/redux/api/localizations';
import { userDetailSuccess } from '~/redux/reducers/user/actions';
import { getProfile } from '~/redux/reducers/user/thunks';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { RootStackParamList } from '~/routes/types';
import { ILocalization } from '~/types/localization';
import axios from '~/utils/axios.config';

export const useLocalization = ({ onClose }: { onClose: (val: boolean) => void }) => {
  //----EVENTS----
  const _fnCityEvent = useUserCityChange();
  //--X--EVENTS--X--

  const { navigateFromLocalization } = useNavigate();

  const { params } = useRoute<RouteProp<RootStackParamList, RouteEnum.LOCALIZATION>>();
  const [value, setValue] = useState<string>('');
  const dispatch = useAppDispatch();
  const { token } = useAppSelector((state) => state.user);
  const { page, setPage, onEndReached } = usePagination();

  const queryPrams = useMemo(() => {
    return {
      ...(value !== '' ? { search: value } : {}),
      ...(page > 1 ? { page: page } : {}),
    };
  }, [value, page]);
  const { data, isFetching } = useGetCitiesQuery(queryPrams);

  const onChange = (text: string) => {
    setValue(text);
    setPage(1);
  };

  const onSelected = (city: ILocalization) => {
    if (token) {
      axios({
        method: 'PUT',
        url: `${SERVER_URL}/user/me/profile/`,
        headers: {
          Accept: 'application/json',
          Authorization: `Bearer ${token?.access}`,
        },
        data: {
          city: city.id,
        },
      })
        .then(() => {
          _fnCityEvent(city.name!);
          dispatch(getProfile({ token: token?.access! })).finally(() => {
            if (params && params.previousScreen) {
              navigateFromLocalization(params.previousScreen, params.params);
            } else {
              onClose(false);
            }
          });
        })
        .catch(() => {})
        .finally(() => {});
    } else {
      dispatch(
        userDetailSuccess({
          city: city.id,
          city_name: city.name,
          first_name: '',
          phone_number: '',
          last_name: '',
        }),
      );
      if (params && params.previousScreen) {
        navigateFromLocalization(params.previousScreen, params.params);
      } else {
        onClose(false);
      }
    }
  };

  return {
    data,
    value,
    isFetching,

    onChange,
    onSelected,
    onEndReached,
  };
};
