import { RouteProp, useRoute } from '@react-navigation/native';
import { useState } from 'react';

import { checkCollectionType } from '~/helpers/checkCollectionType';
import { getPagingCollection } from '~/helpers/getPagingCollection';
import { useNavigate } from '~/hooks/useNavigate';
import { getBrandAddresses } from '~/redux/reducers/content/brands/thunks';
import { couponsMayInterestYou, couponsPage } from '~/redux/reducers/content/coupon/actions';
import {
  getCouponDetail,
  getCoupons,
  getFilteredCoupons,
  getSimilarCoupons,
} from '~/redux/reducers/content/coupon/thunks';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { RootStackParamList } from '~/routes/types';
import { ICouponCollections } from '~/types/coupon';
import { ParamsOptions } from '~/types/shared';

export const useCoupons = () => {
  const dispatch = useAppDispatch();
  const { isConnected } = useAppSelector((state) => state.network);
  const [pagingLoading, setPagingLoading] = useState(false);
  const { collection, loading, detail, similar, filteredCoupons } = useAppSelector(
    (state) => state.coupons,
  );
  const { detail: userDetail } = useAppSelector((state) => state.user);
  const [dataCollection, setDataCollection] = useState<ICouponCollections>({
    count: 0,
    previous: null,
    next: null,
    results: [],
  });

  const { params: routeCollectionParams } =
    useRoute<RouteProp<RootStackParamList, RouteEnum.COUPON>>();
  const { params: detailParams } =
    useRoute<RouteProp<RootStackParamList, RouteEnum.COUPON_DETAIL>>();
  const { navigateToCouponCollection, navigateToCouponDetails, navigateToSearch } = useNavigate();

  const [collectionParams, setCollectionParams] = useState<
    { params: ParamsOptions; prevScreen: RouteEnum } | undefined
  >(routeCollectionParams);

  const fetchCouponCollection = (): Promise<ICouponCollections> => {
    return new Promise((resolve, reject) => {
      dispatch(getCoupons())
        .then((res) => {
          dispatch(couponsMayInterestYou(res.results.slice(0, 5)));
          resolve(res);
        })
        .catch((error) => {
          reject(error);
        });
    });
  };
  const fetchCouponAdresses = async (id: number) => {
    dispatch(getBrandAddresses({ id: id }));
  };
  const fetchCouponDetail = async (id: number) => {
    return await dispatch(getCouponDetail({ id: id }))
      .then((res) => {
        fetchSimilarCoupons({ category_ids: res.categories });
        return res;
      })
      .catch((err) => {
        throw err;
      });
  };
  const fetchSimilarCoupons = (params: ParamsOptions) => {
    dispatch(getSimilarCoupons(params))
      .then((res) => {
        setDataCollection(res);
      })
      .catch((err) => {
        console.log(err);
      });
  };
  const fetchByScreenType = async (screenType: RouteEnum, params: ParamsOptions) => {
    const obj = {
      [RouteEnum.DISCOUNT_DETAIL]: async () => {
        return await dispatch(getSimilarCoupons(params));
      },
      [RouteEnum.COUPON_DETAIL]: async () => {
        return await dispatch(getSimilarCoupons(params));
      },
      // TODO: hide search button
      // [RouteEnum.SEARCH]: async () => {
      //   return await dispatch(getFilteredCoupons(params));
      // },
      [RouteEnum.FILTER_MODAL_SCREEN]: async () => {
        return await dispatch(getFilteredCoupons(params));
      },
    };
    return await obj[screenType]();
  };
  const getByScreenType = (screenType: RouteEnum) => {
    const obj = {
      [RouteEnum.DISCOUNT_DETAIL]: similar.coupons,
      [RouteEnum.COUPON_DETAIL]: similar.coupons,
      // TODO: hide search button
      // [RouteEnum.SEARCH]: filteredCoupons,
      [RouteEnum.FILTER_MODAL_SCREEN]: filteredCoupons,
    };
    setDataCollection(obj[screenType]);
  };

  const handlePaging = (screenType?: RouteEnum) => {
    if (isConnected && dataCollection.next && dataCollection.next !== null) {
      setPagingLoading(true);
      getPagingCollection<ICouponCollections>({
        url: dataCollection.next,
        payload: collectionParams?.params,
      })
        .then((res) => {
          dispatch(
            couponsPage({
              listType: checkCollectionType(screenType),
              collection: res.data,
            }),
          );
          setDataCollection({
            count: res.data.count,
            next: res.data.next,
            previous: res.data.previous,
            results: [...dataCollection.results, ...res.data.results],
          });
          setPagingLoading(false);
        })
        .catch((err) => {
          console.log(err);
          setPagingLoading(false);
        });
    }
  };

  return {
    loading,
    filteredCoupons,
    similar,
    pagingLoading,
    collection: collectionParams ? filteredCoupons : collection,
    initialCollection: collection,
    similarCoupons: similar.coupons,
    detail,
    detailParams,
    collectionParams,
    dataCollection,
    userDetail,
    fetchCouponAdresses,
    fetchByScreenType,
    getByScreenType,
    navigateToCouponCollection,
    navigateToCouponDetails,
    navigateToSearch,
    fetchCouponDetail,
    fetchSimilarCoupons,
    fetchCouponCollection,
    setDataCollection,
    dispatch,
    routeCollectionParams,
    setCollectionParams,
    handlePaging,
  };
};
