import { useFocusEffect } from '@react-navigation/native';
import { useCallback, useEffect, useState } from 'react';

import { useFilters } from '~/hooks/useFilters';
import { useLazyGetArticlesQuery } from '~/redux/api/articles';
import { useLazyGetBrandsQuery } from '~/redux/api/brands';
import { useLazyGetCouponsQuery } from '~/redux/api/coupons';
import { useLazyGetCardsQuery } from '~/redux/api/discounts';
import { useLazyGetInspirationsQuery } from '~/redux/api/inspirations';
import { useLazyGetLeafletsQuery } from '~/redux/api/newspapers';
import { userLastRefresh } from '~/redux/reducers/user/actions';
import { getPremium } from '~/redux/reducers/user/thunks';
import { useAppSelector } from '~/redux/store';
import { useStartupInfo } from './useStartupInfo';
import { useNavigate } from './useNavigate';
import { RouteEnum } from '~/routes/routes';

export const useHome = () => {
  const [isLoading, setIsLoading] = useState(false);
  const { isConnected } = useAppSelector((state) => state.network);
  const { navigation } = useNavigate();
  const { data } = useStartupInfo();
  const [error, setError] = useState({
    isError: false,
    msg: '',
  });
  const [refreshing, setRefreshing] = useState(false);
  const { detail, last_refresh, token, see_startup_info } = useAppSelector((state) => state.user);
  const { dispatch, removeAllFilters } = useFilters();

  const [getBrands] = useLazyGetBrandsQuery();
  const [getCards] = useLazyGetCardsQuery();
  const [getCoupons] = useLazyGetCouponsQuery();
  const [getLeaflets] = useLazyGetLeafletsQuery();
  const [getArticles] = useLazyGetArticlesQuery();
  const [getInspirations] = useLazyGetInspirationsQuery();
  const { fetchFilterCategories } = useFilters();

  const getHome = async () => {
    setIsLoading(true);
    fetchFilterCategories();

    return Promise.all([
      getBrands({ city_id: detail.city, token: token?.access }),
      getCards({ city_id: detail.city, token: token?.access }),
      getCoupons({ city_id: detail.city, token: token?.access }),
      getLeaflets({ city_id: detail.city, token: token?.access }),
      getArticles({ city_id: detail.city, token: token?.access }),
      getInspirations({ city_id: detail.city, token: token?.access }),
    ])
      .then(() => {
        dispatch(userLastRefresh(Date.now().toString()));
      })
      .catch(() => {
        setIsLoading(false);
        setError({ msg: 'Unable to load home stack', isError: true });
      });
  };
  const handleRefresh = () => {
    setRefreshing(true);
    getHome()
      .catch(() => {})
      .finally(() => setRefreshing(false));
  };

  /*
   * refresh home on focus when user is longer than 1 hour
   */
  useFocusEffect(
    useCallback(() => {
      const oneHour = 60 * 40 * 1000;
      if (!last_refresh || Date.now() - parseInt(last_refresh, 10) >= oneHour) {
        if (token && token.access) dispatch(getPremium({ token: token.access })).catch(() => {});
        getHome().catch(() => {});
      }
    }, [last_refresh]),
  );

  /*
   * refresh home on localization change
   * get content on first home render
   */
  useEffect(() => {
    getHome()
      .catch(() => {})
      .finally(() => setRefreshing(false));
  }, [detail.city, isConnected]);

  useEffect(() => {
    console.log('HOME', data);
    if (data && data.length > 0 && !see_startup_info) {
      navigation.navigate(RouteEnum.STARTUP_INFO);
    }
  }, [data]);

  useFocusEffect(
    useCallback(() => {
      removeAllFilters();
    }, []),
  );

  return {
    refreshing,
    detail,
    isLoading,
    error,
    handleRefresh,
  };
};
