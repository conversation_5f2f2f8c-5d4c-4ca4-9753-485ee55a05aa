import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';

import { SERVER_URL } from '~/config.api';
import { useUserAccountChange } from '~/events/hooks/useUserAccountChange';
import { handleGetOTC as getOTC } from '~/helpers/handleGetOTC';
import { useNavigate } from '~/hooks/useNavigate';
import { getMeConsents } from '~/redux/api/user';
import { getMe, getProfile } from '~/redux/reducers/user/thunks';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { IStatus } from '~/types/errors';
import axios from '~/utils/axios.config';
import { formSchema } from '~/validation/userData/Schema';
import { TForm } from '~/validation/userData/types';

export interface IUseChangeUserData {
  setLoading: (loading: boolean) => void;
  setStatus: (loading: IStatus | undefined) => void;
  setShowEmailForm: (email: string) => void;
}

export const useChangeUserData = ({
  setLoading,
  setStatus,
  setShowEmailForm,
}: IUseChangeUserData) => {
  //----EVENTS----
  const _fnUserAccChange = useUserAccountChange();
  //--X--EVENTS--X--
  const dispatch = useAppDispatch();
  const { navigation } = useNavigate();
  const { detail, me, token } = useAppSelector((state) => state.user);

  const form = useForm<TForm>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: detail?.first_name,
      surname: detail?.last_name,
      email: me?.email.toLowerCase(),
      phone: detail?.phone_number,
    },
    mode: 'onSubmit',
    reValidateMode: 'onChange',
  });

  const onSubmit = (data: TForm) => {
    setLoading(true);
    setStatus(undefined);

    axios<any>({
      method: 'PUT',
      url: `${SERVER_URL}/user/me/profile/`,
      headers: {
        Accept: 'application/json',
        Authorization: `Bearer ${token?.access}`,
      },
      data: {
        first_name: data.name,
        last_name: data.surname,
        phone_number: data.phone,
      },
    })
      .then(async () => {
        const response = await getMeConsents({ token: token!.access });
        _fnUserAccChange(response.data, {
          first_name: data.name,
          last_name: data.surname,
          phone_number: data.phone,
        });
        if (data.email.toLowerCase() !== me?.email.toLowerCase()) {
          handleGetOTC(data.email.toLowerCase());
        } else {
          setStatus({
            status: 'success',
            message: 'Dane zostały zaktualizowane',
          });
        }
      })
      .catch(() => {
        setLoading(false);
        setStatus({
          status: 'error',
          message: 'Wystąpił błąd podczas aktualizacji danych',
        });
      })
      .finally(() => {
        if (data.email.toLowerCase() === me?.email.toLowerCase()) {
          dispatch(getProfile({ token: token?.access! }))
            .then(() => {
              dispatch(getMe({ token: token?.access! }))
                .then(() => {
                  navigation.goBack();
                })
                .catch(() => {});
            })
            .catch(() => {});
        }
      });
  };

  const handleGetOTC = (email: string) => {
    getOTC(email.toLowerCase(), 'email_update', token?.access!)
      .then(() => {
        setShowEmailForm(email);
      })
      .catch(() => {})
      .finally(() => {
        setLoading(false);
      });
  };

  return {
    form,
    onSubmit,
  };
};
