import { useState } from 'react';

import { SERVER_URL } from '~/config.api';
import { useNavigate } from '~/hooks/useNavigate';
import { userLogout } from '~/redux/reducers/user/actions';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { IStatus } from '~/types/errors';
import { IPremium } from '~/types/user';
import axios from '~/utils/axios.config';

export const useAccountDelete = () => {
  const { navigation } = useNavigate();
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState<IStatus | undefined>();
  const { token } = useAppSelector((state) => state.user);

  const handleLogout = async () => {
    await dispatch(userLogout());
    navigation.navigate(RouteEnum.LOGIN);
  };

  const handleDelete = () => {
    setLoading(true);
    axios<IPremium>({
      method: 'DELETE',
      url: `${SERVER_URL}/user/me/deactivate/`,
      headers: {
        Accept: 'application/json',
        Authorization: `Bearer ${token?.access}`,
      },
    })
      .then(() => {
        setStatus({
          status: 'success',
          message: 'Konto zostało usunięte',
        });
      })
      .catch(() => {
        setStatus({
          status: 'error',
          message: 'Wystąpił błąd podczas usuwania konta',
        });
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleBack = () => {
    if (status?.status !== 'success') {
      navigation.goBack();
    } else {
      handleLogout().catch();
    }
  };

  return {
    loading,
    status,
    handleLogout,
    handleDelete,
    handleBack,
    navigation,
  };
};
