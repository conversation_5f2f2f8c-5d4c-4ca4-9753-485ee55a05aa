import VersionCheck from 'react-native-version-check';

const APP_ID = '1388067675';
type Return = {
  checkVersion: () => Promise<{
    currentVersion: string;
    needUpdate: boolean;
    storeUrl: string;
  }>;
};

export const useVersionCheck = (): Return => {
  const checkVersion = async () => {
    const currVersion = VersionCheck.getCurrentVersion();
    const lastVersion = await VersionCheck.getLatestVersion();
    const { isNeeded } = await VersionCheck.needUpdate({
      currentVersion: currVersion,
      latestVersion: lastVersion,
    });
    const url = await VersionCheck.getStoreUrl({
      appID: APP_ID,
    });

    return {
      currentVersion: currVersion,
      needUpdate: isNeeded,
      storeUrl: url,
    };
  };
  return {
    checkVersion,
  };
};
