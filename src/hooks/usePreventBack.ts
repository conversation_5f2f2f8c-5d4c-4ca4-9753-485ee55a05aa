import { useFocusEffect } from '@react-navigation/native';
import React from 'react';
import { BackHandler } from 'react-native';

export const usePreventBack = () => {
  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        return true;
      };
      BackHandler.addEventListener('hardwareBackPress', onBackPress);
      return () => BackHandler.removeEventListener('hardwareBackPress', onBackPress);
    }, []),
  );
};
