import { useLinking } from '~/hooks/useLinking';
import { useNavigate } from '~/hooks/useNavigate';
import { RouteEnum } from '~/routes/routes';
import { IAddress } from '~/types/addresses';

export const useShopModalActions = () => {
  const { openMap, openPhone } = useLinking();
  const { navigateBack, navigateToLocalization } = useNavigate();

  const handleCloseModal = (): void => {
    navigateBack();
  };
  const sortAddressesByUserLocation = (
    addressesParam: IAddress[],
    userLocation?: string | null,
  ) => {
    const userLocationAddresses: IAddress[] = [];
    const otherAddresses: IAddress[] = [];

    addressesParam.forEach((address) => {
      if (address.city === userLocation) {
        userLocationAddresses.push(address);
      } else {
        otherAddresses.push(address);
      }
    });

    return [userLocationAddresses, otherAddresses];
  };

  const groupAddressesByCity = (userLocationAddresses: IAddress[], otherAddresses: IAddress[]) => {
    const groupedUserAddresses: { [key: string]: IAddress[] } = {};
    const groupedOtherAddresses: { [key: string]: IAddress[] } = {};
    userLocationAddresses.forEach((address) => {
      if (address.city) {
        if (!groupedUserAddresses[address.city]) {
          groupedUserAddresses[address.city] = [];
        }
        groupedUserAddresses[address.city].push(address);
      }
    });
    otherAddresses.forEach((address) => {
      if (address.city) {
        if (!groupedOtherAddresses[address.city]) {
          groupedOtherAddresses[address.city] = [];
        }
        groupedOtherAddresses[address.city].push(address);
      }
    });
    return [groupedUserAddresses, groupedOtherAddresses];
  };
  const prepareDataForShopModal = (addressesParam: IAddress[], userLocation?: string | null) => {
    const [a, b] = sortAddressesByUserLocation(addressesParam, userLocation);
    return groupAddressesByCity(a, b);
  };
  const onLocalizationPress = (data: Partial<IAddress>) => {
    if (data.location) {
      const [lat, long] = data.location.split(',');
      openMap(Number(lat), Number(long))
        .then()
        .catch(() => {});
    }
  };
  const onPhonePress = (data: Partial<IAddress>) => {
    if (data.phone_number1) {
      openPhone(data.phone_number1)
        .then()
        .catch(() => {});
    } else if (data.phone_number2) {
      openPhone(data.phone_number2)
        .then()
        .catch(() => {});
    }
  };
  const onUserLocalizationChangePress = (params: any) => {
    navigateToLocalization({ prevScreen: RouteEnum.FIND_SHOP_MODAL_SCREEN, params: params });
  };

  return {
    sortAddressesByUserLocation,
    groupAddressesByCity,
    prepareDataForShopModal,
    handleCloseModal,
    onLocalizationPress,
    onPhonePress,
    onUserLocalizationChangePress,
  };
};
