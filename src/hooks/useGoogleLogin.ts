import auth from '@react-native-firebase/auth';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { useContext, useEffect } from 'react';

import { useNavigate } from './useNavigate';

import { GOOGLE_WEB_CLIENT } from '~/config.api';
import { socialLoginContext } from '~/pages/Auth/context/socialLoginContext';
import { getMe, getPremium, getProfile, getSocialToken } from '~/redux/reducers/user/thunks';
import { useAppDispatch } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';

export const useGoogleLogin = () => {
  const { setLoading, setStatus } = useContext(socialLoginContext);
  const dispatch = useAppDispatch();
  const { navigation } = useNavigate();

  useEffect(() => {
    GoogleSignin.configure({
      webClientId: GOOGLE_WEB_CLIENT,
    });
  }, []);

  const googleLogin = async () => {
    let profileName: string | undefined;
    setLoading(true);
    try {
      await GoogleSignin.hasPlayServices({ showPlayServicesUpdateDialog: true });
      const userInfo = await GoogleSignin.signIn();
      const credential = auth.GoogleAuthProvider.credential(userInfo.idToken);
      await auth().signInWithCredential(credential);
      await auth()
        .currentUser?.getIdToken()
        .then(async (res) => {
          await dispatch(getSocialToken({ token: res })).then(async (tokenRes) => {
            await Promise.all([
              dispatch(getProfile({ token: tokenRes.tokens.access })).then((resProfile) => {
                profileName = resProfile.first_name;
              }),
              dispatch(getMe({ token: tokenRes.tokens.access })),
              dispatch(getPremium({ token: tokenRes.tokens.access })).catch(() => {}),
            ]);
          });
        });
    } catch (error: any) {
      setLoading(false);
      setStatus({
        status: 'error',
        message: 'Wystąpił błąd podczas logowania',
      });
      console.error('google signIn', error);
      return;
    }
    if (profileName !== undefined && profileName.length > 0) {
      navigation.navigate(RouteEnum.HOME);
    } else {
      navigation.navigate(RouteEnum.SOCIAL_REGISTRATION);
    }
  };

  return {
    googleLogin,
  };
};
