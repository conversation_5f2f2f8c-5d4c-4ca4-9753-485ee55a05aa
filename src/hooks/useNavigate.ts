import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useCallback } from 'react';

import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { RouteEnum } from '~/routes/routes';
import { RootStackParamList } from '~/routes/types.d';
import { ParamsOptions } from '~/types/shared';

export const useNavigate = () => {
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  /*
   *Brands
   */
  const navigateToBrandCollection = (data?: { params: ParamsOptions; prevScreen: RouteEnum }) => {
    navigation.navigate(RouteEnum.BRAND, data);
  };
  const navigateToBrandDetails = (id: number, is_rec?: boolean) => {
    navigation.navigate(RouteEnum.BRAND_DETAIL, { id: id, is_rec: is_rec });
  };
  /*
   *Inspiration
   */
  const navigateToInspirationCollection = (data?: {
    params: ParamsOptions;
    prevScreen: RouteEnum;
  }) => {
    navigation.navigate(RouteEnum.INSPIRATION, data);
  };
  const navigateToInspirationDetails = (id: number, is_rec?: boolean) => {
    navigation.navigate(RouteEnum.INSPIRATION_DETAIL, { id: id, is_rec: is_rec });
  };
  /*
   *Articles
   */
  const navigateToArticleCollection = (data?: { params: ParamsOptions; prevScreen: RouteEnum }) => {
    navigation.navigate(RouteEnum.ARTICLE, data);
  };
  const navigateToArticleDetails = (id: number, is_rec?: boolean) => {
    navigation.navigate(RouteEnum.ARTICLE_DETAIL, { id: id, is_rec: is_rec });
  };
  /*
   *Newspapers
   */
  const navigateToNewspaperCollection = (data?: {
    params: ParamsOptions;
    prevScreen: RouteEnum;
  }) => {
    navigation.navigate(RouteEnum.NEWSPAPER, data);
  };
  const navigateToNewspaperDetails = (id: number, is_rec?: boolean) => {
    navigation.navigate(RouteEnum.NEWSPAPER_DETAIL, { id: id, is_rec: is_rec });
  };
  /*
   *Discounts
   */
  const navigateToDiscountCollection = (data?: {
    params: ParamsOptions;
    prevScreen: RouteEnum;
  }) => {
    navigation.navigate(RouteEnum.DISCOUNT, data);
  };
  const navigateToDiscountDetails = (id: number, is_rec?: boolean) => {
    navigation.navigate(RouteEnum.DISCOUNT_DETAIL, { id: id, is_rec: is_rec });
  };
  /*
   *Coupons
   */
  const navigateToCouponCollection = (data?: { params: ParamsOptions; prevScreen: RouteEnum }) => {
    navigation.navigate(RouteEnum.COUPON, data);
  };
  const navigateToCouponDetails = (id: number, is_rec?: boolean) => {
    navigation.navigate(RouteEnum.COUPON_DETAIL, { id: id, is_rec: is_rec });
  };
  /*
   *Home
   */
  const navigateToHome = () => {
    navigation.navigate(RouteEnum.HOME);
  };
  /*
   *Back
   */
  const navigateBack = () => {
    navigation.goBack();
  };

  /*
   *Search
   */
  const navigateToSearch = (screenType: ObjectTypeEnum) => {
    navigation.navigate(RouteEnum.SEARCH, { screenType: screenType });
  };

  /*
   * Don't use below function if you dont need it!!!!!
   */
  const navigateFromLocalization = useCallback((screen: any, params: any) => {
    navigation.goBack();
    setTimeout(() => {
      navigation.navigate(screen, params);
    }, 100);
  }, []);

  const navigateToLocalization = useCallback((data: { prevScreen: RouteEnum; params: any }) => {
    navigation.goBack();
    setTimeout(() => {
      navigation.navigate(RouteEnum.LOCALIZATION, {
        previousScreen: data.prevScreen,
        params: data.params,
      });
    }, 100);
  }, []);
  return {
    navigation,
    // navigateFromLocalizationToFilter,
    // navigateFromFilterToLocalization,
    navigateFromLocalization,
    navigateToLocalization,
    navigateToArticleDetails,
    navigateToArticleCollection,
    navigateToNewspaperCollection,
    navigateToNewspaperDetails,
    navigateBack,
    navigateToHome,
    navigateToDiscountCollection,
    navigateToDiscountDetails,
    navigateToSearch,
    navigateToCouponCollection,
    navigateToCouponDetails,
    navigateToBrandCollection,
    navigateToBrandDetails,
    navigateToInspirationCollection,
    navigateToInspirationDetails,
  };
};
