import { useNetInfo } from '@react-native-community/netinfo';
import { useState } from 'react';

export const usePagination = () => {
  const { isConnected } = useNetInfo();
  const [page, setPage] = useState<number>(1);
  let isFetchingNextPage = false;

  const onEndReached = (isFetching: boolean, next?: string | null) => {
    if (!isConnected) return;
    if (next === null || isFetching || isFetchingNextPage) return;
    isFetchingNextPage = true;
    setPage(page + 1);
    setTimeout(() => {
      isFetchingNextPage = false;
    }, 500);
  };

  return { page, setPage, onEndReached };
};
