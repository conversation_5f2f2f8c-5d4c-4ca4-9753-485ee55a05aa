import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useNavigate } from '~/hooks/useNavigate';
import { getArticles } from '~/redux/reducers/content/articles/thunks';
import { getBrands } from '~/redux/reducers/content/brands/thunks';
import { getCoupons } from '~/redux/reducers/content/coupon/thunks';
import { getDiscounts } from '~/redux/reducers/content/discounts/thunks';
import { getInspirations } from '~/redux/reducers/content/inspirations/thunks';
import { getNewspapers } from '~/redux/reducers/content/newspapers/thunks';
import { RootState, useAppDispatch } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { ParamsOptions } from '~/types/shared';

export const useFetchByContentType = () => {
  const dispatch = useAppDispatch();
  const {
    navigateToDiscountCollection,
    navigateToCouponCollection,
    navigateToNewspaperCollection,
    navigateToBrandCollection,
    navigateToArticleCollection,
    navigateToInspirationCollection,
  } = useNavigate();

  const fetchByType = (type: ObjectTypeEnum, payload?: ParamsOptions) => {
    switch (type) {
      case ObjectTypeEnum.Card:
        dispatch(getDiscounts(payload)).catch(() => {});
        break;
      case ObjectTypeEnum.Coupon:
        dispatch(getCoupons(payload)).catch(() => {});
        break;
      case ObjectTypeEnum.Article:
        dispatch(getArticles(payload)).catch(() => {});
        break;
      case ObjectTypeEnum.Inspiration:
        dispatch(getInspirations(payload)).catch(() => {});
        break;
      case ObjectTypeEnum.Leaflet:
        dispatch(getNewspapers(payload)).catch(() => {});
        break;
      case ObjectTypeEnum.Brand:
        dispatch(getBrands(payload)).catch(() => {});
        break;
      default:
        break;
    }
  };

  const navigateByType = (type: ObjectTypeEnum, params: ParamsOptions, prevScreen: RouteEnum) => {
    switch (type) {
      case ObjectTypeEnum.Card:
        navigateToDiscountCollection({ params: params, prevScreen: prevScreen });
        break;
      case ObjectTypeEnum.Coupon:
        navigateToCouponCollection({ params: params, prevScreen: prevScreen });
        break;
      case ObjectTypeEnum.Article:
        navigateToArticleCollection({ params: params, prevScreen: prevScreen });
        break;
      case ObjectTypeEnum.Inspiration:
        navigateToInspirationCollection({ params: params, prevScreen: prevScreen });
        break;
      case ObjectTypeEnum.Leaflet:
        navigateToNewspaperCollection({ params: params, prevScreen: prevScreen });
        break;
      case ObjectTypeEnum.Brand:
        navigateToBrandCollection({ params: params, prevScreen: prevScreen });
        break;
      default:
        break;
    }
  };

  const selectContentType = {
    [ObjectTypeEnum.Card]: 'discounts',
    [ObjectTypeEnum.Coupon]: 'coupons',
    [ObjectTypeEnum.Article]: 'articles',
    [ObjectTypeEnum.Inspiration]: 'inspirations',
    [ObjectTypeEnum.Leaflet]: 'newspapers',
    [ObjectTypeEnum.Brand]: 'brands',
  };

  const dynamicSelector = (type: ObjectTypeEnum) => {
    return (state: RootState) => state[selectContentType[type]];
  };

  return { fetchByType, dynamicSelector, navigateByType };
};
