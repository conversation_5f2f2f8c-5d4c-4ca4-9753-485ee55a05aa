import { RouteProp, useRoute } from '@react-navigation/native';
import { useState } from 'react';

import { checkCollectionType } from '~/helpers/checkCollectionType';
import { getPagingCollection } from '~/helpers/getPagingCollection';
import { articlePage, articlesMayInterestYou } from '~/redux/reducers/content/articles/actions';
import {
  getArticleDetail,
  getArticles,
  getFilteredArticles,
  getSimilarArticles,
} from '~/redux/reducers/content/articles/thunks';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { RootStackParamList } from '~/routes/types';
import { IArticleCollections } from '~/types/article';
import { ParamsOptions } from '~/types/shared';

export const useArticles = () => {
  const dispatch = useAppDispatch();
  const { isConnected } = useAppSelector((state) => state.network);
  const [pagingLoading, setPagingLoading] = useState(false);
  const { collection, loading, detail, similar, filteredArticles } = useAppSelector(
    (state) => state.articles,
  );
  const [dataCollection, setDataCollection] = useState<IArticleCollections>({
    count: 0,
    previous: null,
    next: null,
    results: [],
  });

  const { params: routeCollectionParams } =
    useRoute<RouteProp<RootStackParamList, RouteEnum.ARTICLE>>();
  const { params: detailParams } =
    useRoute<RouteProp<RootStackParamList, RouteEnum.ARTICLE_DETAIL>>();
  const [collectionParams, setCollectionParams] = useState<
    { params: ParamsOptions; prevScreen: RouteEnum } | undefined
  >(routeCollectionParams);

  const fetchArticleCollection = (): Promise<IArticleCollections> => {
    return new Promise((resolve, reject) => {
      dispatch(getArticles())
        .then((res) => {
          dispatch(articlesMayInterestYou(res.results.slice(0, 5)));
          resolve(res);
        })
        .catch((error) => {
          reject(error);
        });
    });
  };
  const fetchArticleDetail = async (id: number) => {
    return await dispatch(getArticleDetail({ id: id }))
      .then((res) => {
        fetchSimilarArticles({ category_ids: res.categories });
        return res;
      })
      .catch((err) => {
        throw err;
      });
  };
  const fetchSimilarArticles = (params: ParamsOptions) => {
    dispatch(getSimilarArticles(params))
      .then((res) => {
        setDataCollection(res);
      })
      .catch((err) => {
        console.log(err);
      });
  };
  const fetchByScreenType = async (screenType: RouteEnum, params: ParamsOptions) => {
    const obj = {
      [RouteEnum.ARTICLE_DETAIL]: async () => {
        return await dispatch(getSimilarArticles(params));
      },
      // TODO: hide search button
      // [RouteEnum.SEARCH]: async () => {
      //   return await dispatch(getFilteredArticles(params));
      // },
      [RouteEnum.FILTER_MODAL_SCREEN]: async () => {
        return await dispatch(getFilteredArticles(params));
      },
    };
    return await obj[screenType]();
  };
  const getByScreenType = (screenType: RouteEnum) => {
    const obj = {
      [RouteEnum.ARTICLE_DETAIL]: similar.articles,
      // TODO: hide search button
      // [RouteEnum.SEARCH]: filteredArticles,
      [RouteEnum.FILTER_MODAL_SCREEN]: filteredArticles,
    };
    setDataCollection(obj[screenType]);
  };

  const handlePaging = (screenType?: RouteEnum) => {
    if (isConnected && dataCollection.next && dataCollection.next !== null) {
      setPagingLoading(true);
      getPagingCollection<IArticleCollections>({
        url: dataCollection.next,
        payload: collectionParams?.params,
      })
        .then((res) => {
          dispatch(
            articlePage({
              listType: checkCollectionType(screenType),
              collection: res.data,
            }),
          );
          setDataCollection({
            count: res.data.count,
            next: res.data.next,
            previous: res.data.previous,
            results: [...dataCollection.results, ...res.data.results],
          });
          setPagingLoading(false);
        })
        .catch((err) => {
          console.log(err);
          setPagingLoading(false);
        });
    }
  };

  return {
    loading,
    filteredArticles,
    similar,
    pagingLoading,
    collection: collectionParams ? filteredArticles : collection,
    initialCollection: collection,
    similarArticles: similar.articles,
    detail,
    detailParams,
    collectionParams,
    dataCollection,
    routeCollectionParams,
    fetchByScreenType,
    getByScreenType,
    fetchArticleDetail,
    fetchSimilarArticles,
    fetchArticleCollection,
    setDataCollection,
    dispatch,
    handlePaging,
    setCollectionParams,
  };
};
