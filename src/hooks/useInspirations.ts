import { RouteProp, useRoute } from '@react-navigation/native';
import { useState } from 'react';

import { checkCollectionType } from '~/helpers/checkCollectionType';
import { getPagingCollection } from '~/helpers/getPagingCollection';
import {
  inspirationPage,
  inspirationsMayInterestYou,
} from '~/redux/reducers/content/inspirations/actions';
import {
  getFilteredInspirations,
  getInspirationDetail,
  getInspirations,
  getSimilarInspirations,
} from '~/redux/reducers/content/inspirations/thunks';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { RootStackParamList } from '~/routes/types';
import { IInspiration, IInspirationCollections } from '~/types/inspirations';
import { ParamsOptions } from '~/types/shared';

// type InspirationState = {
//     count: number,
//     previous: string | null,
//     next: string | null,
//     results: IInspiration[][]
// }

export const useInspirations = () => {
  const dispatch = useAppDispatch();
  const { isConnected } = useAppSelector((state) => state.network);
  const [pagingLoading, setPagingLoading] = useState(false);
  const { collection, loading, detail, similar, filteredInspirations } = useAppSelector(
    (state) => state.inspirations,
  );
  const [dataCollection, setDataCollection] = useState<IInspirationCollections>({
    count: 0,
    previous: null,
    next: null,
    results: [],
  });

  const { params: routeCollectionParams } =
    useRoute<RouteProp<RootStackParamList, RouteEnum.INSPIRATION>>();
  const { params: detailParams } =
    useRoute<RouteProp<RootStackParamList, RouteEnum.INSPIRATION_DETAIL>>();
  const [collectionParams, setCollectionParams] = useState<
    { params: ParamsOptions; prevScreen: RouteEnum } | undefined
  >(routeCollectionParams);

  const chunkArray = (array: IInspiration[], chunkSize: number) => {
    const results = [];
    while (array.length) {
      results.push(array.splice(0, chunkSize));
    }
    return results;
  };

  const fetchInspirationCollection = (): Promise<IInspirationCollections> => {
    return new Promise((resolve, reject) => {
      dispatch(getInspirations())
        .then((res) => {
          dispatch(inspirationsMayInterestYou(res.results.slice(0, 5)));
          resolve(res);
        })
        .catch((error) => {
          reject(error);
        });
    });
  };
  const fetchInspirationDetail = async (id: number) => {
    return await dispatch(getInspirationDetail({ id: id }))
      .then((res) => {
        fetchSimilarInspirations({ category_ids: res.categories });
        return res;
      })
      .catch((err) => {
        throw err;
      });
  };
  const fetchSimilarInspirations = (params: ParamsOptions) => {
    dispatch(getSimilarInspirations(params))
      .then((res) => {
        setDataCollection(res);
      })
      .catch((err) => {
        console.log(err);
      });
  };
  const fetchByScreenType = async (screenType: RouteEnum, params: ParamsOptions) => {
    const obj = {
      [RouteEnum.BRAND_DETAIL]: async () => {
        return await dispatch(getSimilarInspirations(params));
      },
      [RouteEnum.INSPIRATION_DETAIL]: async () => {
        return await dispatch(getSimilarInspirations(params));
      },
      // TODO: hide search button
      // [RouteEnum.SEARCH]: async () => {
      //   return await dispatch(getFilteredInspirations(params));
      // },
      [RouteEnum.FILTER_MODAL_SCREEN]: async () => {
        return await dispatch(getFilteredInspirations(params));
      },
    };
    return await obj[screenType]();
  };
  const getByScreenType = (screenType: RouteEnum) => {
    const obj = {
      [RouteEnum.BRAND_DETAIL]: similar.inspirations,
      [RouteEnum.INSPIRATION_DETAIL]: similar.inspirations,
      // TODO: hide search button
      // [RouteEnum.SEARCH]: filteredInspirations,
      [RouteEnum.FILTER_MODAL_SCREEN]: filteredInspirations,
    };
    setDataCollection(obj[screenType]);
  };

  const handlePaging = (screenType?: RouteEnum) => {
    if (isConnected && dataCollection.next && dataCollection.next !== null) {
      setPagingLoading(true);
      getPagingCollection<IInspirationCollections>({
        url: dataCollection.next,
        payload: collectionParams?.params,
      })
        .then((res) => {
          dispatch(
            inspirationPage({
              listType: checkCollectionType(screenType),
              collection: res.data,
            }),
          );
          setDataCollection({
            count: res.data.count,
            next: res.data.next,
            previous: res.data.previous,
            results: [...dataCollection.results, ...res.data.results],
          });
          setPagingLoading(false);
        })
        .catch((err) => {
          console.log(err);
          setPagingLoading(false);
        });
    }
  };

  return {
    loading,
    filteredInspirations,
    similar,
    pagingLoading,
    collection: collectionParams ? filteredInspirations : collection,
    initialCollection: collection,
    similarInspirations: similar.inspirations,
    detail,
    detailParams,
    collectionParams,
    dataCollection,
    fetchByScreenType,
    getByScreenType,
    fetchInspirationDetail,
    fetchSimilarInspirations,
    fetchInspirationCollection,
    setDataCollection,
    dispatch,
    routeCollectionParams,
    setCollectionParams,
    handlePaging,
    chunkArray,
  };
};
