import { useEffect, useRef } from 'react';
import { Animated } from 'react-native';

export const useLofiAnimation = () => {
  const backgroundColor = useRef(new Animated.Value(0));

  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(backgroundColor.current, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: false,
        }),
        Animated.timing(backgroundColor.current, {
          toValue: 0,
          duration: 500,
          useNativeDriver: false,
        }),
      ]),
    ).start();
  }, []);

  const interpolation = backgroundColor.current.interpolate({
    inputRange: [0, 1],
    outputRange: ['#333333', '#A0A0A0'],
  });
  return { interpolation };
};
