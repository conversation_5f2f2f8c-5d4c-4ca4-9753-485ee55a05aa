import { useFocusEffect } from '@react-navigation/native';
import { useCallback, useRef } from 'react';
import { ScrollView } from 'react-native';

export const useScrollEffect = (fetch: (param: any) => void) => {
  const ref = useRef<ScrollView>(null);

  useFocusEffect(
    useCallback(() => {
      if (ref.current) {
        ref.current.scrollTo({ x: 0, y: 0, animated: true });
      }
    }, [fetch]),
  );
  return { ref };
};
