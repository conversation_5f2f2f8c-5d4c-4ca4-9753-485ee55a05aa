// INFO:
// Use this hook to animate height, width etc.
// Don't use this hook for animating opacity etc., use useAnimation instead

import { LayoutAnimation, Platform, UIManager } from 'react-native';

export interface IUseLayoutAnimationProps {
  duration: number;
  type: 'spring' | 'linear' | 'easeInEaseOut' | 'easeIn' | 'easeOut' | 'keyboard';
  delay: number;
  property?: 'opacity' | 'scaleX' | 'scaleY' | 'scaleXY';
  onAnimationDidEnd?: () => void;
  onAnimationDidFail?: () => void;
}

interface IUseLayoutAnimationReturn {
  animate: () => void;
  easeInEaseOut: () => void;
}

export const useLayoutAnimation = ({
  duration,
  type,
  property,
  delay,
  onAnimationDidEnd,
  onAnimationDidFail,
}: IUseLayoutAnimationProps): IUseLayoutAnimationReturn => {
  if (Platform.OS === 'android') {
    if (UIManager.setLayoutAnimationEnabledExperimental) {
      UIManager.setLayoutAnimationEnabledExperimental(true);
    }
  }

  function animate(): void {
    LayoutAnimation.configureNext(
      {
        duration: duration,
        create: {
          type: type,
          property: property,
          delay: delay,
          duration: duration,
        },
        update: {
          type: type,
          property: property,
          delay: delay,
          duration: duration,
        },
        delete: {
          type: type,
          property: property,
          delay: delay,
          duration: duration,
        },
      },
      onAnimationDidEnd,
      onAnimationDidFail,
    );
  }

  function easeInEaseOut(): void {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
  }

  return {
    animate,
    easeInEaseOut,
  };
};
