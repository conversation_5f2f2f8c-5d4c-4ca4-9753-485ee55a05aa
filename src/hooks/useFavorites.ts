import { RouteProp, useRoute } from '@react-navigation/native';
import { debounce } from 'lodash';
import { useContext, useState } from 'react';

import { IBookmark } from '~/components/Modals/FavoriteModal/types';
import { favoriteContext } from '~/context/favoriteContext';
import { BookmarkEnum } from '~/enums/bookmarkEnum';
import { ObjectReduxTypeActionEnum, ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useAddFavorites } from '~/events/hooks/useAddFavorites';
import { IAddFavorites } from '~/events/types/addFavorites';
import { useNavigate } from '~/hooks/useNavigate';
import {
  useDeleteArticleFavoriteMutation,
  useGetArticlesDetailsQuery,
  usePostArticleFavoriteMutation,
  usePutArticleFavoriteMutation,
} from '~/redux/api/articles';
import {
  useDeleteBrandFavoriteMutation,
  usePostBrandFavoriteMutation,
  usePutBrandFavoriteMutation,
} from '~/redux/api/brands';
import { TContentApiTagType, tagTypes } from '~/redux/api/contentApi';
import {
  useDeleteCouponFavoriteMutation,
  usePostCouponFavoriteMutation,
  usePutCouponFavoriteMutation,
} from '~/redux/api/coupons';
import {
  useDeleteCardFavoriteMutation,
  usePostCardFavoriteMutation,
  usePutCardFavoriteMutation,
} from '~/redux/api/discounts';
import {
  useDeleteInspirationFavoriteMutation,
  usePostInspirationFavoriteMutation,
  usePutInspirationFavoriteMutation,
} from '~/redux/api/inspirations';
import {
  useDeleteLeafletFavoriteMutation,
  usePostLeafletFavoriteMutation,
  usePutLeafletFavoriteMutation,
} from '~/redux/api/newspapers';
import { favoritesAllChange } from '~/redux/reducers/content/favorites/actions';
import {
  deleteFavorites,
  getFavoritesAll,
  postFavorites,
  putFavorites,
} from '~/redux/reducers/content/favorites/thunks';
import { useAppDispatch } from '~/redux/store';
import { POSTFavoriteParam } from '~/redux/types/common';
import { RouteEnum } from '~/routes/routes';
import { RootStackParamList } from '~/routes/types';

interface IUseFavorites {
  objectType: ObjectTypeEnum;
}

interface IHandleChangeState {
  bookmark: null | string;
  change?: boolean;
}

export interface IAddFavoriteMethod {
  objectId: number;
  isFavorite: boolean | null;
  type?: ObjectTypeEnum;
  modalHeaderTitle?: string;
  screenType?: ObjectTypeEnum;
  categoriesIdList?: number[];
  tagsIdList?: number[];
}

export interface IEventData {
  objectId: number;
  objectType: ObjectTypeEnum;
  categoriesIdList?: number[];
  tagsIdList?: number[];
}

//!NEW
export const useFavoritesQuery = () => {
  const [postCardFavorite] = usePostCardFavoriteMutation();
  const [postBrandFavorite] = usePostBrandFavoriteMutation();
  const [postLeafletFavorite] = usePostLeafletFavoriteMutation();
  const [postArticleFavorite] = usePostArticleFavoriteMutation();
  const [postCouponFavorite] = usePostCouponFavoriteMutation();
  const [postInspirationFavorite] = usePostInspirationFavoriteMutation();
  //const [] = usePostCouponFavoriteMutation();
  //.
  //.
  //ITP
  const { navigation } = useNavigate();
  const { handleSendEvent } = useAddFavorites();
  const { setShow, setData } = useContext(favoriteContext);

  const queries = {
    [tagTypes[0]]: postArticleFavorite,
    [tagTypes[1]]: postCardFavorite,
    [tagTypes[2]]: postCouponFavorite,
    [tagTypes[3]]: postInspirationFavorite,
    [tagTypes[4]]: postLeafletFavorite,
    [tagTypes[5]]: postBrandFavorite,
    [tagTypes[6]]: (params: POSTFavoriteParam) => {},
  };

  const postFavorite = ({
    isFavorite,
    params,
    event,
  }: {
    isFavorite: boolean | null;
    params: POSTFavoriteParam;
    event: Omit<IAddFavorites, 'user_id' | 'object_id'>;
  }) => {
    setData({
      bookmark: params.payload.bookmark,
      tag: params.type,
      isFavorite: isFavorite,
      objectId: params.payload.object_id,
      objectType: params.payload.object_type,
    });

    if (!isFavorite) {
      queries[params.type](params)
        .unwrap()
        .then(() => {
          handleSendEvent({
            ...event,
            object_id: params.payload.object_id,
          });
          setShow(true);
        });
    } else {
      navigation.navigate(RouteEnum.FAVORITE_MODAL_SCREEN);
    }
  };

  return { postFavorite };
};

export const useFavoritesModalQuery = () => {
  const { navigation } = useNavigate();
  const { data } = useContext(favoriteContext);
  const [active, setActive] = useState<string | null | undefined>(data?.bookmark);
  const [putCardFavorite] = usePutCardFavoriteMutation();
  const [putBrandFavorite] = usePutBrandFavoriteMutation();
  const [putLeafletFavorite] = usePutLeafletFavoriteMutation();
  const [putArticleFavorite] = usePutArticleFavoriteMutation();
  const [putCouponFavorite] = usePutCouponFavoriteMutation();
  const [putInspirationFavorite] = usePutInspirationFavoriteMutation();
  //.
  //.
  //ITP
  const [deleteCardFavorite] = useDeleteCardFavoriteMutation();
  const [deleteBrandFavorite] = useDeleteBrandFavoriteMutation();
  const [deleteLeafletFavorite] = useDeleteLeafletFavoriteMutation();
  const [deleteArticleFavorite] = useDeleteArticleFavoriteMutation();
  const [deleteCouponFavorite] = useDeleteCouponFavoriteMutation();
  const [deleteInspirationFavorite] = useDeleteInspirationFavoriteMutation();

  console.log('🚀 ~ useFavoritesModalQuery ~ data', data?.bookmark);

  const queries = {
    [tagTypes[0]]: putArticleFavorite,
    [tagTypes[1]]: putCardFavorite,
    [tagTypes[2]]: putCouponFavorite,
    [tagTypes[3]]: putInspirationFavorite,
    [tagTypes[4]]: putLeafletFavorite,
    [tagTypes[5]]: putBrandFavorite,
    // [tagTypes[6]]: (params: POSTFavoriteParam): Promise<void> => {return Promise.resolve()},
  };

  const queriesDelete = {
    [tagTypes[0]]: deleteArticleFavorite,
    [tagTypes[1]]: deleteCardFavorite,
    [tagTypes[2]]: deleteCouponFavorite,
    [tagTypes[3]]: deleteInspirationFavorite,
    [tagTypes[4]]: deleteLeafletFavorite,
    [tagTypes[5]]: deleteBrandFavorite,
    // [tagTypes[6]]: (params: POSTFavoriteParam): Promise<void> => {return Promise.resolve()},
  };

  const handlePutFavorite = (bookmark?: string) => {
    setActive(bookmark);
    queries[data?.tag!]({
      payload: {
        object_id: data?.objectId!,
        object_type: data?.objectType as ObjectTypeEnum,
        bookmark: bookmark,
      },
      type: data?.tag,
    })
      .unwrap()
      .finally(() => {
        navigation.goBack();
      });
  };

  const handleDeleteFavorite = () => {
    queriesDelete[data?.tag!]({
      payload: {
        object_id: data?.objectId!,
        object_type: data?.objectType as ObjectTypeEnum,
      },
      type: data?.tag,
    })
      .unwrap()
      .finally(() => {
        navigation.goBack();
      });
  };

  return {
    data,
    navigation,
    active,
    handlePutFavorite,
    handleDeleteFavorite,
  };
};

///! NIE POTRZEBA
export const useFavorites = ({ objectType }: IUseFavorites) => {
  const { navigation } = useNavigate();
  //const dispatch = useAppDispatch();
  const { handleSendEvent } = useAddFavorites();
  const { setShow, setData } = useContext(favoriteContext);

  //const [putFavorite] = usePutFavoriteMutation();

  const handleChangeState = ({
    objectId,
    isFavorite,
    categoriesIdList,
    tagsIdList,
  }: IAddFavoriteMethod) => {
    // dispatch(
    //   favoritesAllChange({
    //     objectId: objectId,
    //     isFavorite: !isFavorite,
    //     bookmark: BookmarkEnum.All,
    //     objectType: objectType,
    //   }),
    // );
    //EVENTS ADD FAVORITE
    handleSendEvent({
      object_id: objectId,
      object_type: objectType,
      categories_id_list: categoriesIdList ? categoriesIdList : [],
      tags_id_list: tagsIdList ? tagsIdList : [],
    });

    //const action = ObjectReduxTypeActionEnum[objectType];
    //dispatch(action({ objectId: objectId, isFavorite: !isFavorite }));
  };

  const handleAdd = async (item: IAddFavoriteMethod, tagType: TContentApiTagType) => {
    if (item.isFavorite) {
      // putFavorite({
      //   payload: {
      //     object_id: item.objectId,
      //     object_type: objectType,
      //   },
      //   type: tagType,
      // });
    } else {
      // postFavorite({
      //   payload: {
      //     object_id: item.objectId,
      //     object_type: objectType,
      //   },
      //   type: tagType,
      // });
    }
  };

  const addFavorite = debounce((itemData: IAddFavoriteMethod, tagType: TContentApiTagType) => {
    if (!itemData.isFavorite) {
      // setData({
      //   ...itemData,
      //   isFavorite: !itemData.isFavorite,
      //   objectType: itemData.type ? itemData.type : objectType,
      // });
      //handleAdd(itemData, tagType).then();
      setShow(true);
    } else {
      // navigation.navigate(RouteEnum.FAVORITE_MODAL_SCREEN, {
      //   ...itemData,
      //   objectType: itemData.type ? itemData.type : objectType,
      // });
    }
  }, 300);

  return { addFavorite };
};

//!MODAL
/*
export const useFavoritesModal = () => {
  const { navigation } = useNavigate();
  const dispatch = useAppDispatch();
  //EVENTS ADD FAVORITE
  const { handleSendEvent } = useAddFavorites();
  const { params } = useRoute<RouteProp<RootStackParamList, RouteEnum.FAVORITE_MODAL_SCREEN>>();
  const [active, setActive] = useState<string | null | undefined>();

  const handleChangeState = ({ bookmark, change = false }: IHandleChangeState) => {
    dispatch(
      favoritesAllChange({
        objectId: params.objectId,
        isFavorite: change ? params.isFavorite : !params.isFavorite,
        bookmark: bookmark,
        objectType: params.objectType,
      }),
    );

    if (!change) {
      //EVENTS ADD FAVORITE
      handleSendEvent({
        object_id: params.objectId,
        object_type: params.objectType,
        categories_id_list: params.categoriesIdList ? params.categoriesIdList : [],
        tags_id_list: params.tagsIdList ? params.tagsIdList : [],
      });

      const action = ObjectReduxTypeActionEnum[params.objectType];
      dispatch(action({ objectId: params.objectId, isFavorite: !params.isFavorite }));
    }
  };

  const handleBack = (screenType?: ObjectTypeEnum) => {
    if (screenType) {
      // TODO: hide search button
      // navigation.navigate(RouteEnum.SEARCH, { screenType: screenType });
    } else {
      navigation.goBack();
    }
  };

  const handleAdd = (item: Pick<IBookmark, 'code' | 'name'>, screenType?: ObjectTypeEnum) => {
    setActive(item.code);
    const body =
      item.code !== BookmarkEnum.All
        ? {
            object_id: params.objectId,
            object_type: params.objectType,
            bookmark: item.code as string,
          }
        : {
            object_id: params.objectId,
            object_type: params.objectType,
          };

    if (params.isFavorite) {
      dispatch(putFavorites(body))
        .then(() => {
          handleChangeState({
            bookmark: item.code,
            change: true,
          });
        })
        .catch((err) => {
          console.log(err);
        });
    } else {
      dispatch(postFavorites(body))
        .then(() => {
          dispatch(getFavoritesAll());
          handleChangeState({
            bookmark: item.code,
          });
        })
        .catch((err) => {
          console.log(err);
        });
    }

    setTimeout(() => {
      if (screenType) {
        // TODO: hide search button
        // navigation.navigate(RouteEnum.SEARCH, { screenType: screenType });
      } else {
        navigation.goBack();
      }
    }, 500);
  };

  const handleDelete = (screenType?: ObjectTypeEnum) => {
    dispatch(deleteFavorites({ object_id: params.objectId, object_type: params.objectType }))
      .then(() => {
        handleChangeState({
          bookmark: null,
        });
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        if (screenType) {
          // TODO: hide search button
          // navigation.navigate(RouteEnum.SEARCH, { screenType: screenType });
        } else {
          navigation.goBack();
        }
      });
  };

  return {
    handleAdd,
    handleDelete,
    handleBack,
    active,
    params,
    navigation,
  };
};
*/
