import auth from '@react-native-firebase/auth';
import { appleAuth } from '@invertase/react-native-apple-authentication';
import { useAppDispatch } from '~/redux/store';
import { useContext } from 'react';
import { socialLoginContext } from '~/pages/Auth/context/socialLoginContext';
import { useNavigate } from './useNavigate';
import { getMe, getPremium, getProfile, getSocialToken } from '~/redux/reducers/user/thunks';
import { RouteEnum } from '~/routes/routes';
export const useAppleLogin = () => {
  const { setLoading, setStatus } = useContext(socialLoginContext);
  const dispatch = useAppDispatch();
  const { navigation } = useNavigate();

  const getAppleCredentials = async () => {
    const appleAuthRequestResponse = await appleAuth.performRequest({
      requestedOperation: appleAuth.Operation.LOGIN,
      requestedScopes: [appleAuth.Scope.FULL_NAME, appleAuth.Scope.EMAIL],
    });

    if (!appleAuthRequestResponse.identityToken) {
      throw new Error('Apple Sign-In failed - no identify token returned');
    }

    const { identityToken, nonce } = appleAuthRequestResponse;
    const appleCredential = auth.AppleAuthProvider.credential(identityToken, nonce);

    return auth().signInWithCredential(appleCredential);
  };

  const appleLogin = async () => {
    let profileName: string | undefined;
    setLoading(true);
    try {
      await getAppleCredentials();
      await auth()
        .currentUser?.getIdToken()
        .then(async (res) => {
          await dispatch(getSocialToken({ token: res })).then(async (tokenRes) => {
            await Promise.all([
              dispatch(getProfile({ token: tokenRes.tokens.access })).then((resProfile) => {
                profileName = resProfile.first_name;
              }),
              dispatch(getMe({ token: tokenRes.tokens.access })),
              dispatch(getPremium({ token: tokenRes.tokens.access })).catch(() => {}),
            ]);
          });
        });
    } catch (error: any) {
      setLoading(false);
      setStatus({
        status: 'error',
        message: 'Wystąpił błąd podczas logowania',
      });
      console.error('apple signIn', error);
      return;
    }
    if (profileName !== undefined && profileName.length > 0) {
      navigation.navigate(RouteEnum.HOME);
    } else {
      navigation.navigate(RouteEnum.SOCIAL_REGISTRATION);
    }
  };

  return {
    appleLogin,
  };
};
