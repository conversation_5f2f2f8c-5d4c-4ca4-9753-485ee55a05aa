import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';

import { SERVER_URL } from '~/config.api';
import { getMe, getProfile } from '~/redux/reducers/user/thunks';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { IStatus } from '~/types/errors';
import { IPremium } from '~/types/user';
import axios from '~/utils/axios.config';
import { formSchema } from '~/validation/emailChange/Schema';
import { TForm } from '~/validation/emailChange/types';

export interface IUseChangeUserEmail {
  setLoading: (loading: boolean) => void;
  setStatus: (loading: IStatus | undefined) => void;
  email: string;
}

export const useChangeUserEmail = ({ email, setStatus, setLoading }: IUseChangeUserEmail) => {
  const dispatch = useAppDispatch();
  const { token } = useAppSelector((state) => state.user);
  const form = useForm<TForm>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: email.toLowerCase(),
      password: '',
      verification_code: '',
    },
    mode: 'onSubmit',
    reValidateMode: 'onChange',
  });

  const onSubmit = (data: TForm) => {
    setLoading(true);
    setStatus(undefined);
    axios<IPremium>({
      method: 'PATCH',
      url: `${SERVER_URL}/user/me/`,
      headers: {
        Accept: 'application/json',
        Authorization: `Bearer ${token?.access}`,
      },
      data: {
        email: data.email.toLowerCase(),
        old_password: data.password,
        verification_code: data.verification_code,
      },
    })
      .then(() => {
        setStatus({
          status: 'success',
          message: 'Dane zostały zaktualizowane',
        });
      })
      .catch(() => {
        setStatus({
          status: 'error',
          message: 'Wystąpił błąd podczas aktualizacji danych',
        });
      })
      .finally(() => {
        dispatch(getProfile({ token: token?.access! }))
          .then(() => {
            dispatch(getMe({ token: token?.access! }))
              .then(() => {
                setLoading(false);
              })
              .catch(() => {});
          })
          .catch(() => {});
      });
  };

  return {
    form,
    onSubmit,
  };
};
