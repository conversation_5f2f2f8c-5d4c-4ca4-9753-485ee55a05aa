import ReactNativeBlobUtil from 'react-native-blob-util';
import Share, { ShareOptions } from 'react-native-share';

import { getSharingInfo } from '~/redux/reducers/info/sharingInfo/thunks';
import { useAppDispatch } from '~/redux/store';

export const useShare = () => {
  const dispatch = useAppDispatch();

  const fetchImageAsBase64 = async (url: string) => {
    const res = await ReactNativeBlobUtil.fetch('GET', url);
    return res.base64();
  };

  const share = async () => {
    const response = await dispatch(getSharingInfo());
    const imageBase64 = await fetchImageAsBase64(response.image);
    const shareOptions: ShareOptions = {
      title: response.title,
      message: response.text,
      type: 'image/jpeg',
      filename: 'image',
      url: `data:image/jpeg;base64,${imageBase64}`,
    };

    try {
      await Share.open(shareOptions);
    } catch (error) {
      console.log('Error =>', error);
    }
  };
  return { share };
};
