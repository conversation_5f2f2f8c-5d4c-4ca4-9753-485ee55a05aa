import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { IArticleCollections } from '~/types/article';
import { IBrandCollections } from '~/types/brands';
import { ICouponCollections } from '~/types/coupon';
import { IDiscountCollections, ISingleDiscount } from '~/types/discount';
import { IInspirationCollections } from '~/types/inspirations';
import { INewspaperCollections } from '~/types/newspaper';

export interface IDetail {
  id: number;
  is_rec?: boolean;
}

export interface IPolicy {
  type: 'policy' | 'terms';
}

export type TDiscountModal = Omit<ISingleDiscount, 'discount_value_type'> & {
  brand_id: number;
  discount_id?: number;
  card_id?: number;
  object_id?: number;
  object_type?: ObjectTypeEnum;
};

export type ISearch =
  | ICouponCollections
  | IBrandCollections
  | IInspirationCollections
  | INewspaperCollections
  | IArticleCollections
  | IDiscountCollections;
