import AsyncStorage from '@react-native-async-storage/async-storage';
import { NavigationContainer, NavigationContainerRef } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import React, { useEffect, useRef, useState } from 'react';
import { AppState, AppStateStatus } from 'react-native';

import { pages } from './constants';

import { ContactModal } from '~/components/Modals/ContactModal/ContactModal';
import { FavoriteChangeModal } from '~/components/Modals/FavoriteChangeModal/FavoriteChangeModal';
import { AppStateContextProvider } from '~/context/appStateContext';
import { EventsContextProvider } from '~/context/eventsContext';
import { NotificationContextProvider } from '~/context/notificationContext';
import { initNotification } from '~/helpers/notification';
import { SessionStates } from '~/redux/reducers/states/SessionStates';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { RootStackParamList } from '~/routes/types';
import { userSeeStartupInfo } from '~/redux/reducers/user/actions';

export let navigatorRef: any;

export const setNavigator = (nav: NavigationContainerRef<ReactNavigation.RootParamList> | null) => {
  navigatorRef = nav;
};

const PERSISTENCE_KEY = 'NAVIGATION_STATE_V1';

export const Router = () => {
  const Stack = createNativeStackNavigator<RootStackParamList>();
  const previousScreens = useRef('');
  const { session } = useAppSelector((state) => state.session);
  const dispatch = useAppDispatch();
  const [active, setActive] = useState<string>('');
  const [channelId, setChannelId] = useState<string>('');

  useEffect(() => {
    dispatch(userSeeStartupInfo(false));

    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState.match(/inactive|background/)) {
        const state = navigatorRef.getRootState();
        AsyncStorage.setItem(PERSISTENCE_KEY, JSON.stringify(state));
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      subscription.remove();
    };
  }, []);

  useEffect(() => {
    initNotification()
      .then((res) => {
        setChannelId(res);
      })
      .catch(() => {});
  }, []);

  useEffect(() => {
    if (session === SessionStates.EXPIRED) {
      navigatorRef.navigate(RouteEnum.LOGIN);
    }
  }, [session]);

  return (
    <NavigationContainer
      ref={(ref) => setNavigator(ref)}
      onStateChange={() => {
        previousScreens.current = navigatorRef.getCurrentRoute()?.name || '';
        setActive(navigatorRef.getCurrentRoute()?.name || '');
      }}>
      <EventsContextProvider routeName={previousScreens} activeRoute={active}>
        <NotificationContextProvider providerValues={{ channelId: channelId }}>
          <AppStateContextProvider>
            <Stack.Navigator
              screenOptions={{
                headerShown: false,
                animation: 'none',
                orientation: 'portrait',
              }}
              initialRouteName={RouteEnum.SPLASH}>
              {pages.map((page, index) => (
                <Stack.Screen
                  key={index}
                  name={page.name as RouteEnum}
                  component={page.component}
                  options={page.options}
                />
              ))}
            </Stack.Navigator>
            {/*ADDITIONAL MODALS*/}
            <FavoriteChangeModal />
            <ContactModal />
            {/*END ADDITIONAL MODALS*/}
          </AppStateContextProvider>
        </NotificationContextProvider>
      </EventsContextProvider>
    </NavigationContainer>
  );
};
