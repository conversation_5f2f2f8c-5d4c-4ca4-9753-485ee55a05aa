import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { IDetail, IPolicy, TDiscountModal } from '~/routes/pageTypes';
import { RouteEnum } from '~/routes/routes';
import { INotification } from '~/types/notification';
import { ParamsOptions } from '~/types/shared';

export type RootStackParamList = {
  [RouteEnum.HOME]: undefined;
  [RouteEnum.PREMIUM]: undefined;
  [RouteEnum.PROFILE]: undefined;
  [RouteEnum.SETTINGS]: undefined;
  [RouteEnum.MY_DATA]: undefined;
  [RouteEnum.NOTIFICATION_SETTING]: undefined;
  [RouteEnum.SEARCH]: { screenType: ObjectTypeEnum };
  [RouteEnum.CONTACT]: undefined;
  //ITEMS
  [RouteEnum.COUPON]: { params: ParamsOptions; prevScreen: RouteEnum } | undefined;
  [RouteEnum.COUPON_DETAIL]: IDetail;
  [RouteEnum.BRAND]: { params: ParamsOptions; prevScreen: RouteEnum } | undefined;
  [RouteEnum.BRAND_DETAIL]: IDetail;
  [RouteEnum.INSPIRATION]: { params: ParamsOptions; prevScreen: RouteEnum } | undefined;
  [RouteEnum.INSPIRATION_DETAIL]: IDetail;
  [RouteEnum.NEWSPAPER]: { params: ParamsOptions; prevScreen: RouteEnum } | undefined;
  [RouteEnum.NEWSPAPER_DETAIL]: IDetail;
  [RouteEnum.ARTICLE]: { params: ParamsOptions; prevScreen: RouteEnum } | undefined;
  [RouteEnum.ARTICLE_DETAIL]: IDetail;
  [RouteEnum.DISCOUNT]: { params: ParamsOptions; prevScreen: RouteEnum } | undefined;
  [RouteEnum.DISCOUNT_DETAIL]: IDetail;

  [RouteEnum.TEST]: IDetail;
  //AUTH
  [RouteEnum.SPLASH]: undefined;
  [RouteEnum.LOGIN]: undefined;
  [RouteEnum.AUTH_MODAL]: undefined;
  [RouteEnum.REGISTRATION]: undefined;
  // [RouteEnum.PASSWORD_RESET]: undefined;
  [RouteEnum.POLICY]: IPolicy;
  //----MODALS----
  [RouteEnum.FILTER_MODAL_SCREEN]: { type: ObjectTypeEnum };
  [RouteEnum.DISCOUNT_MODAL_SCREEN]: { code: string; data: TDiscountModal };
  [RouteEnum.FIND_SHOP_MODAL_SCREEN]: {
    screenType: ObjectTypeEnum.Brand | ObjectTypeEnum.Card;
    shopId: number;
    objectType?: ObjectTypeEnum;
    objectId?: number;
  };
  [RouteEnum.LOCALIZATION]: { previousScreen?: RouteEnum; params: any } | undefined;
  //--X--MODALS--X--
  //USER
  [RouteEnum.SOCIAL_REGISTRATION]: undefined;
  [RouteEnum.PREMIUM_MODAL]: undefined;
  [RouteEnum.PREMIUM_MODAL_SCREEN]: undefined;
  [RouteEnum.PLAN_INFO]: undefined;
  [RouteEnum.PASSWORD_CHANGE]: undefined;
  [RouteEnum.ACCOUNT_DELETE]: undefined;
  [RouteEnum.EDIT_PROFILE]: undefined;
  [RouteEnum.PREMIUM_DELETE]: undefined;
  [RouteEnum.NOTIFICATIONS]: undefined;
  [RouteEnum.NOTIFICATION_DETAIL]: INotification;
  [RouteEnum.ONBOARDING]: undefined;
  [RouteEnum.STARTUP_INFO]: undefined;
  [RouteEnum.FAVORITE_MODAL_SCREEN]: undefined;
  // [RouteEnum.FAVORITE_MODAL_SCREEN]: {
  //   objectId: number;
  //   objectType: ObjectTypeEnum;
  //   categoriesIdList?: number[];
  //   tagsIdList?: number[];
  //   isFavorite: boolean | null;
  //   modalHeaderTitle?: string;
  //   screenType?: ObjectTypeEnum;
  // };
  // PASSWORD RESET
  [RouteEnum.PASSWORD_RESET_EMAIL]: undefined;
  [RouteEnum.PASSWORD_RESET_OTC]: { email: string };
  [RouteEnum.NEW_PASSWORD]: {
    email: string;
    otc: string;
  };

  [RouteEnum.PASSWORD_RESET_SUMMARY]: undefined;
  // APP UPDATE SCREEN
  [RouteEnum.APP_UPDATE_SCREEN]: { url: string };
};
