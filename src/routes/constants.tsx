import { EventListenerCallback, RouteProp } from '@react-navigation/native';
import {
  NativeStackNavigationEventMap,
  NativeStackNavigationOptions,
} from '@react-navigation/native-stack';
import { FC } from 'react';

import { AppUpdateScreen } from '~/pages/AppUpdateScreen/AppUpdateScreen';
import { Article } from '~/pages/Article/Article';
import { ArticlesDetail } from '~/pages/Article/pages/ArticlesDetail';
import { AuthModal } from '~/pages/Auth/pages/AuthModal/AuthModal';
import { Login } from '~/pages/Auth/pages/Login/Login';
import { EmailScreen } from '~/pages/Auth/pages/PasswordReset/Email';
import { NewPassword } from '~/pages/Auth/pages/PasswordReset/NewPassword';
import { OtcScreen } from '~/pages/Auth/pages/PasswordReset/OtcReset';
import { Summary } from '~/pages/Auth/pages/PasswordReset/Summary';
// import { PasswordReset } from '~/pages/Auth/pages/PasswordReset/PasswordReset';
import { Registration } from '~/pages/Auth/pages/Registration/Registration';
import { SocialRegistration } from '~/pages/Auth/pages/SocialRegistration/SocialRegistration';
import { Brands } from '~/pages/Brand/Brands';
import { BrandDetail } from '~/pages/Brand/pages/BrandDetail';
import { Contact } from '~/pages/Contact/Contact';
import { Coupon } from '~/pages/Coupon/Coupon';
import { CouponDetail } from '~/pages/Coupon/pages/CouponDetail';
import { Discount } from '~/pages/Discount/Discount';
import { DiscountDetail } from '~/pages/Discount/pages/DiscountDetail';
import { DiscountModalScreen } from '~/pages/DiscountModalScreen/DiscountModalScreen';
import { Favorite } from '~/pages/Favorite/Fovorite';
import { FilterModalScreen } from '~/pages/FilterModalScreen/FilterModalScreen';
import { FindShopModalScreen } from '~/pages/FindShopModalScreen/FindShopModalScreen';
import { Home } from '~/pages/Home/Home';
import { Inspiration } from '~/pages/Inspiration/Inspiration';
import { InspirationDetail } from '~/pages/Inspiration/pages/InspirationDetail';
import { Localization } from '~/pages/Localization/Localization';
import { Newspaper } from '~/pages/Newspaper/Newspaper';
import { NewspaperDetail } from '~/pages/Newspaper/pages/NewspaperDetail';
import { Notifications } from '~/pages/Notifications/Notifications';
import { NotificationDetail } from '~/pages/Notifications/pages/NotificationDetail';
import { Onboarding } from '~/pages/Onboarding/Onboarding';
import { Policy } from '~/pages/Policy/Policy';
import { Premium } from '~/pages/Premium/Premium';
import { PremiumModal } from '~/pages/Profile/pages/PremiumModal';
import { Profile } from '~/pages/Profile/Profile';
import { Search } from '~/pages/Search/Search';
// TODO: hide search button
import { AccountDelete } from '~/pages/Settings/pages/AccountDelete';
import { EditData } from '~/pages/Settings/pages/EditData/EditData';
import { MyData } from '~/pages/Settings/pages/MyData/MyData';
import { Notification } from '~/pages/Settings/pages/Notification';
import { PasswordChange } from '~/pages/Settings/pages/PasswordChange';
import { PlanInfo } from '~/pages/Settings/pages/PlanInfo/PlanInfo';
import { PremiumDelete } from '~/pages/Settings/pages/PremiumDelete';
import { Settings } from '~/pages/Settings/Settings';
import { Splash } from '~/pages/Splash/Splash';
import { StartupInfo } from '~/pages/StartInfoScreen/StartInfoScreen';
import { RouteEnum } from '~/routes/routes';
import { RootStackParamList } from '~/routes/types.d';
type NavigationListener = Record<
  keyof NativeStackNavigationEventMap,
  EventListenerCallback<NativeStackNavigationEventMap, keyof NativeStackNavigationEventMap>
>;

interface IRoute {
  name: keyof RootStackParamList;
  component: FC;
  options?:
    | NativeStackNavigationOptions
    | ((props: {
        route: RouteProp<RootStackParamList, RouteEnum>;
        navigation: any;
      }) => NativeStackNavigationOptions)
    | undefined;
  listeners?: NavigationListener;
}

export const pages: IRoute[] = [
  {
    name: RouteEnum.SOCIAL_REGISTRATION,
    component: SocialRegistration,
  },

  {
    name: RouteEnum.HOME,
    component: Home,
  },
  {
    name: RouteEnum.PREMIUM,
    component: Premium,
  },
  {
    name: RouteEnum.PROFILE,
    component: Profile,
  },
  {
    name: RouteEnum.SETTINGS,
    component: Settings,
  },
  {
    name: RouteEnum.MY_DATA,
    component: MyData,
  },
  // TODO: hide search button
  {
    name: RouteEnum.SEARCH,
    component: Search,
  },
  {
    name: RouteEnum.NOTIFICATION_SETTING,
    component: Notification,
  },
  {
    name: RouteEnum.COUPON,
    component: Coupon,
  },
  {
    name: RouteEnum.COUPON_DETAIL,
    component: CouponDetail,
  },
  {
    name: RouteEnum.BRAND,
    component: Brands,
  },
  {
    name: RouteEnum.BRAND_DETAIL,
    component: BrandDetail,
  },
  {
    name: RouteEnum.INSPIRATION,
    component: Inspiration,
  },
  {
    name: RouteEnum.INSPIRATION_DETAIL,
    component: InspirationDetail,
  },
  {
    name: RouteEnum.NEWSPAPER,
    component: Newspaper,
  },
  {
    name: RouteEnum.NEWSPAPER_DETAIL,
    component: NewspaperDetail,
  },
  {
    name: RouteEnum.ARTICLE,
    component: Article,
  },
  {
    name: RouteEnum.ARTICLE_DETAIL,
    component: ArticlesDetail,
  },
  {
    name: RouteEnum.DISCOUNT,
    component: Discount,
  },
  {
    name: RouteEnum.DISCOUNT_DETAIL,
    component: DiscountDetail,
  },
  {
    name: RouteEnum.REGISTRATION,
    component: Registration,
  },
  {
    name: RouteEnum.LOGIN,
    component: Login,
  },
  {
    name: RouteEnum.AUTH_MODAL,
    component: AuthModal,
    options: {
      presentation: 'transparentModal',
    },
  },
  {
    name: RouteEnum.POLICY,
    component: Policy,
  },
  {
    name: RouteEnum.SPLASH,
    component: Splash,
  },
  //----MODALS----
  {
    name: RouteEnum.FILTER_MODAL_SCREEN,
    component: FilterModalScreen,
    options: {
      presentation: 'transparentModal',
    },
  },
  {
    name: RouteEnum.FIND_SHOP_MODAL_SCREEN,
    component: FindShopModalScreen,
    options: {
      presentation: 'transparentModal',
    },
  },
  {
    name: RouteEnum.DISCOUNT_MODAL_SCREEN,
    component: DiscountModalScreen,
    options: {
      presentation: 'transparentModal',
    },
  },
  //--X--MODALS--X--

  {
    name: RouteEnum.PREMIUM_MODAL,
    component: PremiumModal,
    options: {
      presentation: 'transparentModal',
    },
  },
  {
    name: RouteEnum.PLAN_INFO,
    component: PlanInfo,
  },
  {
    name: RouteEnum.PASSWORD_CHANGE,
    component: PasswordChange,
    options: {
      presentation: 'transparentModal',
    },
  },
  {
    name: RouteEnum.ACCOUNT_DELETE,
    component: AccountDelete,
    options: {
      presentation: 'transparentModal',
    },
  },
  {
    name: RouteEnum.EDIT_PROFILE,
    component: EditData,
    options: {
      presentation: 'transparentModal',
    },
  },
  {
    name: RouteEnum.CONTACT,
    component: Contact,
    options: {
      presentation: 'transparentModal',
    },
  },
  {
    name: RouteEnum.LOCALIZATION,
    component: Localization,
    options: {
      presentation: 'transparentModal',
    },
  },
  {
    name: RouteEnum.PREMIUM_DELETE,
    component: PremiumDelete,
    options: {
      presentation: 'transparentModal',
    },
  },
  {
    name: RouteEnum.NOTIFICATIONS,
    component: Notifications,
  },
  {
    name: RouteEnum.NOTIFICATION_DETAIL,
    component: NotificationDetail,
    options: {
      presentation: 'transparentModal',
    },
  },
  {
    name: RouteEnum.FAVORITE_MODAL_SCREEN,
    component: Favorite,
    options: {
      presentation: 'transparentModal',
    },
  },
  {
    name: RouteEnum.ONBOARDING,
    component: Onboarding,
  },
  {
    name: RouteEnum.STARTUP_INFO,
    component: StartupInfo,
  },
  // PASSWORD_RESET
  {
    name: RouteEnum.PASSWORD_RESET_EMAIL,
    component: EmailScreen,
  },
  {
    name: RouteEnum.PASSWORD_RESET_OTC,
    component: OtcScreen,
  },
  {
    name: RouteEnum.NEW_PASSWORD,
    component: NewPassword,
  },
  {
    name: RouteEnum.PASSWORD_RESET_SUMMARY,
    component: Summary,
  },
  // APP_UDAPTE_SCREEN
  {
    name: RouteEnum.APP_UPDATE_SCREEN,
    component: AppUpdateScreen,
  },
];
