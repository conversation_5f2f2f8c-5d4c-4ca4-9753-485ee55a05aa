const AnimatedHpGif = require('~/assets/gif/animated_hp.gif');
import AppleLogo from '~/assets/icons/apple_logo.svg';
import ArrowDown from '~/assets/icons/arrow_down.svg';
import ArrowLeft from '~/assets/icons/arrow_left.svg';
import ArrowRight from '~/assets/icons/arrow_right.svg';
import ArrowUp from '~/assets/icons/arrow_up.svg';
import BookMarkmodal from '~/assets/icons/book_mark_modal.svg';
import Bookmark from '~/assets/icons/bookmark.svg';
import BookmarkRotate from '~/assets/icons/bookmark_rotate.svg';
import Checkbox from '~/assets/icons/checkbox.svg';
import Contact from '~/assets/icons/contact.svg';
import Copy from '~/assets/icons/copy.svg';
import Cross from '~/assets/icons/cross.svg';
import CrossCircle from '~/assets/icons/cross_circle.svg';
import CrossFilter from '~/assets/icons/cross_filter.svg';
import CrossWithoutbackground from '~/assets/icons/cross_without_background.svg';
import Edit from '~/assets/icons/edit.svg';
import EditData from '~/assets/icons/edit_data.svg';
import Email from '~/assets/icons/email.svg';
import EyePasswordicon from '~/assets/icons/eye_password_icon.svg';
import Facebook from '~/assets/icons/facebook.svg';
import FilterModal from '~/assets/icons/filter_modal.svg';
import Filters from '~/assets/icons/filters.svg';
import Google from '~/assets/icons/google.svg';
import Heart from '~/assets/icons/heart.svg';
import HomeProfitnav from '~/assets/icons/home_profit_nav.svg';
import HomeProfittext from '~/assets/icons/home_profit_text.svg';
import HpLogo from '~/assets/icons/hp_logo.svg';
import Image from '~/assets/icons/image.svg';
import KlepsydrySzary from '~/assets/icons/klepsydry_szary.svg';
import Line from '~/assets/icons/line.svg';
import LineActive from '~/assets/icons/line_active.svg';
import ListSwitchcol from '~/assets/icons/list_switch_col.svg';
import ListSwitchrow from '~/assets/icons/list_switch_row.svg';
import Lock from '~/assets/icons/lock.svg';
import Logo from '~/assets/icons/logo.svg';
import LogoNavmy_Profit from '~/assets/icons/logo_nav_my_profit.svg';
import Map from '~/assets/icons/map.svg';
import MapPin from '~/assets/icons/map_pin.svg';
import MapPinblack from '~/assets/icons/map_pin_black.svg';
import MenuArticles from '~/assets/icons/menu_articles.svg';
import MenuBrands from '~/assets/icons/menu_brands.svg';
import MenuCoupons from '~/assets/icons/menu_coupons.svg';
import MenuHomeprofit from '~/assets/icons/menu_homeprofit.svg';
import MenuInspirations from '~/assets/icons/menu_inspirations.svg';
import MenuNewspapers from '~/assets/icons/menu_newspapers.svg';
import ModalStar from '~/assets/icons/modal_star.svg';
import More from '~/assets/icons/more.svg';
import MoreTile from '~/assets/icons/more_tile.svg';
import Navigation from '~/assets/icons/navigation.svg';
import Notification from '~/assets/icons/notification.svg';
import NotificationModal from '~/assets/icons/notification_modal.svg';
import PasswordEye from '~/assets/icons/password_eye.svg';
import Percent from '~/assets/icons/percent.svg';
import PercentFill from '~/assets/icons/percent_fill.svg';
import PhoneCall from '~/assets/icons/phone_call.svg';
import PhoneIcon from '~/assets/icons/phone_icon.svg';
import PickCheckbox from '~/assets/icons/pick_checkbox.svg';
import PopupLine from '~/assets/icons/popup_line.svg';
import Premium from '~/assets/icons/premium.svg';
import Refresh from '~/assets/icons/refresh.svg';
import Search from '~/assets/icons/search.svg';
import Settings from '~/assets/icons/settings.svg';
import Share from '~/assets/icons/share.svg';
import Shop from '~/assets/icons/shop.svg';
import Star from '~/assets/icons/star.svg';
import Start from '~/assets/icons/start.svg';
import Success from '~/assets/icons/success.svg';
import User from '~/assets/icons/user.svg';
import WifiOff from '~/assets/icons/wifi_off.svg';
import Barcode from '~/assets/images/barcode.svg';
const DiscountPng = require('~/assets/images/discount.png');
const FavoriteEmptyPng = require('~/assets/images/favorite_empty.png');
const GalleryPng = require('~/assets/images/gallery.png');
const HomeandyouPng = require('~/assets/images/homeandyou.png');
const JyskPng = require('~/assets/images/jysk.png');
const LeroymerlinPng = require('~/assets/images/leroymerlin.png');
import LogoSplash from '~/assets/images/logo_splash.svg';
const MyDataPng = require('~/assets/images/my_data.png');
const NewsletterPng = require('~/assets/images/newsletter.png');
import Qrcode from '~/assets/images/qrcode.svg';
const SplashPng = require('~/assets/images/splash.png');

export const assets = {
  gif: { animated_hp_gif: AnimatedHpGif },
  icons: {
    apple_logo_svg: AppleLogo,
    arrow_down_svg: ArrowDown,
    arrow_left_svg: ArrowLeft,
    arrow_right_svg: ArrowRight,
    arrow_up_svg: ArrowUp,
    book_mark_modal_svg: BookMarkmodal,
    bookmark_svg: Bookmark,
    bookmark_rotate_svg: BookmarkRotate,
    checkbox_svg: Checkbox,
    contact_svg: Contact,
    copy_svg: Copy,
    cross_svg: Cross,
    cross_circle_svg: CrossCircle,
    cross_filter_svg: CrossFilter,
    cross_without_background_svg: CrossWithoutbackground,
    edit_svg: Edit,
    edit_data_svg: EditData,
    email_svg: Email,
    eye_password_icon_svg: EyePasswordicon,
    facebook_svg: Facebook,
    filter_modal_svg: FilterModal,
    filters_svg: Filters,
    google_svg: Google,
    heart_svg: Heart,
    home_profit_nav_svg: HomeProfitnav,
    home_profit_text_svg: HomeProfittext,
    hp_logo_svg: HpLogo,
    image_svg: Image,
    klepsydry_szary_svg: KlepsydrySzary,
    line_svg: Line,
    line_active_svg: LineActive,
    list_switch_col_svg: ListSwitchcol,
    list_switch_row_svg: ListSwitchrow,
    lock_svg: Lock,
    logo_svg: Logo,
    logo_nav_my_profit_svg: LogoNavmy_Profit,
    map_svg: Map,
    map_pin_svg: MapPin,
    map_pin_black_svg: MapPinblack,
    menu_articles_svg: MenuArticles,
    menu_brands_svg: MenuBrands,
    menu_coupons_svg: MenuCoupons,
    menu_homeprofit_svg: MenuHomeprofit,
    menu_inspirations_svg: MenuInspirations,
    menu_newspapers_svg: MenuNewspapers,
    modal_star_svg: ModalStar,
    more_svg: More,
    more_tile_svg: MoreTile,
    navigation_svg: Navigation,
    notification_svg: Notification,
    notification_modal_svg: NotificationModal,
    password_eye_svg: PasswordEye,
    percent_svg: Percent,
    percent_fill_svg: PercentFill,
    phone_call_svg: PhoneCall,
    phone_icon_svg: PhoneIcon,
    pick_checkbox_svg: PickCheckbox,
    popup_line_svg: PopupLine,
    premium_svg: Premium,
    refresh_svg: Refresh,
    search_svg: Search,
    settings_svg: Settings,
    share_svg: Share,
    shop_svg: Shop,
    star_svg: Star,
    start_svg: Start,
    success_svg: Success,
    user_svg: User,
    wifi_off_svg: WifiOff,
  },
  images: {
    barcode_svg: Barcode,
    discount_png: DiscountPng,
    favorite_empty_png: FavoriteEmptyPng,
    gallery_png: GalleryPng,
    homeandyou_png: HomeandyouPng,
    jysk_png: JyskPng,
    leroymerlin_png: LeroymerlinPng,
    logo_splash_svg: LogoSplash,
    my_data_png: MyDataPng,
    newsletter_png: NewsletterPng,
    qrcode_svg: Qrcode,
    splash_png: SplashPng,
  },
};
