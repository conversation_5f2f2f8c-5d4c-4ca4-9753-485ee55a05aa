import { css } from '@emotion/native';

export const shadow = {
  gray: {
    100: css({
      backgroundColor: '#fff',
      shadowColor: 'rgba(0, 0, 0, 0.5)',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowRadius: 8,
      shadowOpacity: 0.2,
      elevation: 6, // Dodaj tę linię, aby dodać cień na Androidzie
    }),
    200: css({
      backgroundColor: '#fff',
      shadowColor: 'rgba(0, 0, 0, 0.5)',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowRadius: 8,
      shadowOpacity: 0.3,
      elevation: 8, // Dodaj tę linię, aby dodać cień na Androidzie
    }),
    400: css({
      backgroundColor: '#fff',
      shadowColor: 'rgba(0, 0, 0, 0.5)',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowRadius: 8,
      shadowOpacity: 0.4,
      elevation: 10, // Dodaj tę linię, aby dodać cień na Androidzie
    }),
  },
};
