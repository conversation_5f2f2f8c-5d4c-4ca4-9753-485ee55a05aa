import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { IButtonVariantsStyles, ITypographyStyles } from '~/components/Button/types.d';
import theme from '~/theme/index';

export const buttonStyle: ITypographyStyles = {
  [ButtonVariant.PRIMARY]: {
    color: theme.colors.white['100'],
    fontSize: 14,
    fontWeight: '600',
  },
  [ButtonVariant.SECONDARY]: {
    color: theme.colors.black['200'],
    fontSize: 14,
    fontWeight: '600',
  },
  [ButtonVariant.TERTIARY]: {
    color: theme.colors.black['100'],
    fontSize: 14,
    fontWeight: '400',
  },
  [ButtonVariant.FOURTH]: {
    color: theme.colors.purple['100'],
    fontSize: 14,
    fontWeight: '600',
  },
  [ButtonVariant.FIFTH]: {
    color: theme.colors.white['100'],
    fontSize: 14,
    fontWeight: '600',
  },
};

export const buttonSizesStyles = {
  [ButtonSize.SMALL]: {
    padding: '8px',
    height: '28px',
  },
  [ButtonSize.XSMALL]: {
    padding: '8px',
    height: '35px',
  },
  [ButtonSize.MEDIUM]: {
    padding: '10px 15px',
    height: '50px',
  },
  [ButtonSize.LARGE]: {
    padding: '22px 45px',
    height: '50px',
  },
};

export const buttonVariantsStyles: IButtonVariantsStyles = {
  [ButtonVariant.PRIMARY]: {
    backgroundColor: theme.colors.black['300'],
    height: '200px',
    borderRadius: '10px',
  },
  [ButtonVariant.SECONDARY]: {
    backgroundColor: theme.colors.gray['500'],
    height: '200px',
    borderRadius: '64px',
    border: 'none',
  },
  [ButtonVariant.TERTIARY]: {
    backgroundColor: 'transparent',
    height: '200px',
    border: `1px solid ${theme.colors.black['100']}`,
  },
  [ButtonVariant.FOURTH]: {
    backgroundColor: theme.colors.purple['200'],
    height: '200px',
    borderRadius: '64px',
    border: 'none',
  },
  [ButtonVariant.FIFTH]: {
    backgroundColor: theme.colors.purple['100'],
    height: '200px',
    borderRadius: '64px',
    border: 'none',
  },
};
