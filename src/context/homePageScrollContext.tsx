import { createContext, ReactNode, RefObject } from 'react';
import { ScrollView } from 'react-native';

interface IHomePageScrollContext {
  ref: RefObject<ScrollView>;
}

interface IHomePageScrollContextProvider {
  providerValue: IHomePageScrollContext;
  children: ReactNode;
}

const defaultContext: IHomePageScrollContext = {
  ref: { current: null },
};

export const homePageScrollContext = createContext<IHomePageScrollContext>(defaultContext);

export const HomePageScrollContextProvider = ({
  children,
  providerValue,
}: IHomePageScrollContextProvider) => {
  return (
    <homePageScrollContext.Provider value={providerValue}>
      {children}
    </homePageScrollContext.Provider>
  );
};
