import notifee, { EventType } from '@notifee/react-native';
import { createContext, ReactNode, useEffect } from 'react';

import { cancelNotification } from '~/helpers/notification';
import { useNavigate } from '~/hooks/useNavigate';
import { RouteEnum } from '~/routes/routes';

interface INotificationContext {
  channelId: string;
}

interface INotificationContextProvider {
  providerValues: INotificationContext;
  children: ReactNode;
}

const defaultContext: INotificationContext = {
  channelId: '',
};

export const notificationContext = createContext<INotificationContext>(defaultContext);

export const NotificationContextProvider = ({
  providerValues,
  children,
}: INotificationContextProvider) => {
  const { navigation } = useNavigate();

  useEffect(() => {
    return notifee.onForegroundEvent(({ type, detail }) => {
      switch (type) {
        case EventType.DISMISSED:
          console.log('User dismissed notification', detail.notification);
          break;
        case EventType.PRESS:
          navigation.navigate(RouteEnum.NOTIFICATIONS);
          cancelNotification(detail.notification?.id);
          break;
      }
    });
  }, []);

  return (
    <notificationContext.Provider value={providerValues}>{children}</notificationContext.Provider>
  );
};
