import { createContext, MutableRefObject, ReactNode, useEffect, useState } from 'react';

import { Events } from '~/events/Events';
import { useAppSelector } from '~/redux/store';

interface IEventsContext {
  events: Events | undefined;
  sessionStart: boolean;
  setSessionStart: (value: boolean) => void;
  routeName: MutableRefObject<string>;
  activeRoute: string;
}

interface IEventsContextProvider {
  routeName: MutableRefObject<string>;
  activeRoute: string;
  children: ReactNode;
}

const defaultContext: IEventsContext = {
  events: undefined,
  sessionStart: true,
  setSessionStart: () => {},
  routeName: { current: '' },
  activeRoute: '',
};

export const eventsContext = createContext<IEventsContext>(defaultContext);

export const EventsContextProvider = ({
  children,
  routeName,
  activeRoute,
}: IEventsContextProvider) => {
  const { token } = useAppSelector((state) => state.user);
  const [session, setSession] = useState<boolean>(true);
  const event = new Events(token?.access);

  useEffect(() => {
    event.setToken(token?.access);
  }, [token]);

  return (
    <eventsContext.Provider
      value={{
        events: event,
        sessionStart: session,
        setSessionStart: setSession,
        routeName: routeName,
        activeRoute: activeRoute,
      }}>
      {children}
    </eventsContext.Provider>
  );
};
