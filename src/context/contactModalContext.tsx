import { createContext, ReactNode, useState } from 'react';

interface IContactModalContext {
  show: boolean;
  setShow: (show: boolean) => void;
}

interface IContactModalContextProvider {
  children: ReactNode;
}

const defaultContext: IContactModalContext = {
  show: false,
  setShow: () => {},
};

export const contactModalContext = createContext<IContactModalContext>(defaultContext);

export const ContactModalContextProvider = ({ children }: IContactModalContextProvider) => {
  const [show, setShow] = useState<boolean>(false);

  return (
    <contactModalContext.Provider
      value={{
        show,
        setShow,
      }}>
      {children}
    </contactModalContext.Provider>
  );
};
