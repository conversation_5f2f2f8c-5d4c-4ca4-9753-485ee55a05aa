import { createContext, ReactNode, useMemo, useState } from 'react';

import { CategoryTypeEnum } from '~/enums/shared';
import { ICategoryShop, ICategorySort, IChildrenCategory, IParentCategory } from '~/types/category';
import { ParamsOptions } from '~/types/shared';

interface IFilterContext {
  selectedCategoryFilter: IParentCategory[] | undefined;
  selectedShopFilter: ICategoryShop | null;
  selectedSortFilter: ICategorySort | null;
  selectedSimilarCategoryFilter: number[];
  selectedBrandFilter: number | undefined;

  /**
   * Search
   */
  searchString: string | null;
  searchId: number | null;
  handleSearchString: (value: string) => void;
  handleSearchId: (value: number) => void;
  clearSearch: () => void;

  setSelectedCategoryFilter: (categories: IParentCategory[] | undefined) => void;
  setSelectedShopFilter: (shop: ICategoryShop) => void;
  setSelectedSortFilter: (sort: ICategorySort) => void;
  setSelectedSimilarCategoryFilter: (ids: number[]) => void;
  setSelectedBrandFilter: (id: number) => void;

  /**
   * Handlers:
   */
  handleSortCollection: (item: ICategorySort) => void;
  handleShopCollection: (item: ICategoryShop) => void;
  handleSimilarCategoryCollection: (ids: number[] | undefined) => void;
  handleFilterByBrandId: (id: number) => void;
  handleCategoryCollection: (item: IChildrenCategory) => void;
  handleAll: (item: IParentCategory) => void;
  buildFilterParams: () => ParamsOptions;
  clear: () => void;
  removeSingleFilter: (id: number, categoryType: CategoryTypeEnum) => void;

  /**
   * Helpers:
   */
  tiles: {
    id: number;
    name: string;
    categoryType: CategoryTypeEnum;
  }[];
}

interface IFilterContextProvider {
  children: ReactNode;
}

const defaultContext: IFilterContext = {
  selectedCategoryFilter: undefined,
  selectedShopFilter: null,
  selectedSortFilter: null,
  selectedSimilarCategoryFilter: [],
  selectedBrandFilter: undefined,

  /**
   * Search
   */
  searchString: null,
  searchId: null,
  handleSearchString: () => {},
  handleSearchId: () => {},
  clearSearch: () => {},

  setSelectedCategoryFilter: () => {},
  setSelectedShopFilter: () => {},
  setSelectedSortFilter: () => {},
  setSelectedSimilarCategoryFilter: () => [],
  setSelectedBrandFilter: () => {},

  /**
   * Handlers:
   */
  handleSortCollection: () => {},
  handleShopCollection: () => {},
  handleSimilarCategoryCollection: () => {},
  handleCategoryCollection: () => {},
  handleFilterByBrandId: () => {},
  handleAll: () => {},
  buildFilterParams: () => ({}),
  clear: () => {},
  removeSingleFilter: () => {},

  /**
   * Helpers:
   */
  tiles: [],
};

export const FilterContext = createContext<IFilterContext>(defaultContext);

export const FilterContextProvider = ({ children }: IFilterContextProvider) => {
  const [selectedCategoryFilter, setSelectedCategoryFilter] = useState<IParentCategory[]>();
  const [selectedShopFilter, setSelectedShopFilter] = useState<ICategoryShop | null>(null);
  const [selectedSortFilter, setSelectedSortFilter] = useState<ICategorySort | null>(null);
  const [selectedSimilarCategoryFilter, setSelectedSimilarCategoryFilter] = useState<number[]>([]);
  const [selectedBrandFilter, setSelectedBrandFilter] = useState<number>();

  const [searchString, setSearchString] = useState<string | null>(null);
  const [searchId, setSearchId] = useState<number | null>(null);

  const handleSearchString = (value: string) => {
    setSearchString(value);
  };

  const handleSearchId = (value: number) => {
    setSearchId(value);
  };

  const clearSearch = () => {
    setSearchId(null);
    setSearchString(null);
  };

  const handleSortCollection = (item: ICategorySort) => {
    setSelectedSimilarCategoryFilter([]);
    setSelectedBrandFilter(undefined);
    clearSearch();
    if (selectedSortFilter?.id === item.id) {
      setSelectedSortFilter(null);
      return;
    }
    setSelectedSortFilter(item);
  };

  const handleShopCollection = (item: ICategoryShop) => {
    setSelectedSimilarCategoryFilter([]);
    setSelectedBrandFilter(undefined);
    clearSearch();
    if (selectedShopFilter?.id === item.id) {
      setSelectedShopFilter(null);
      return;
    }
    setSelectedShopFilter(item);
  };

  const handleSimilarCategoryCollection = (ids: number[] | undefined) => {
    if (!ids) return;
    setSelectedSimilarCategoryFilter(ids);
    setSelectedBrandFilter(undefined);
    clearSearch();
  };

  const handleCategoryCollection = (item: IChildrenCategory) => {
    setSelectedSimilarCategoryFilter([]);
    setSelectedBrandFilter(undefined);
    clearSearch();
    setSelectedCategoryFilter((prev) => {
      const newCategories = prev?.map((parent) => {
        if (parent.id === item.id) {
          return { ...parent, selected: !parent.selected };
        }
        const newChildren = parent.children.map((child) => {
          if (child.id === item.id) {
            return { ...child, selected: !child.selected };
          }
          return child;
        });
        return { ...parent, children: newChildren };
      });
      return newCategories;
    });
  };

  const handleFilterByBrandId = (id: number) => {
    setSelectedSimilarCategoryFilter([]);
    setSelectedBrandFilter(id);
  };

  const handleAll = (item: IParentCategory) => {
    setSelectedSimilarCategoryFilter([]);
    clearSearch();
    setSelectedBrandFilter(undefined);
    setSelectedCategoryFilter((prev) => {
      const newCategories = prev?.map((parent) => {
        if (parent.id === item.id) {
          parent = { ...parent, selected: !parent.selected };
          parent.children = parent.children.map((child) => {
            if (parent.selected) {
              return { ...child, selected: true };
            }
            return { ...child, selected: false };
          });
        }
        return parent;
      });
      return newCategories;
    });
  };

  const buildFilterParams = () => {
    const categoryIds = selectedCategoryFilter?.flatMap((parent) =>
      parent.children.filter((child) => child.selected).map((selectedChild) => selectedChild.id),
    );

    const payload: ParamsOptions = {
      category_ids: selectedSimilarCategoryFilter.concat(categoryIds || []),
      store_type: selectedShopFilter?.type === 2 ? undefined : selectedShopFilter?.type,
      sort_by: selectedSortFilter?.type,
      brand_id: selectedBrandFilter,
      search: searchString || undefined,
      search_id: searchId || undefined,
    };

    return payload;
  };

  const clear = () => {
    setSelectedSimilarCategoryFilter([]);
    setSelectedSortFilter(null);
    setSelectedShopFilter(null);
    setSelectedBrandFilter(undefined);
    clearSearch();
    setSelectedCategoryFilter((prev) =>
      prev?.map((parent) => {
        parent = { ...parent, selected: false };
        parent.children = parent.children.map((child) => {
          return { ...child, selected: false };
        });
        return parent;
      }),
    );
  };

  const removeSingleFilter = (id: number, categoryType: CategoryTypeEnum) => {
    switch (categoryType) {
      case CategoryTypeEnum.CATEGORY:
        setSelectedCategoryFilter((prev) =>
          prev?.map((parent) => {
            parent = { ...parent, selected: false };
            parent.children = parent.children.map((child) => {
              if (child.id === id) {
                return { ...child, selected: false };
              }
              return child;
            });
            return parent;
          }),
        );
        break;
      case CategoryTypeEnum.SHOP:
        setSelectedShopFilter(null);
        break;
      case CategoryTypeEnum.SORT:
        setSelectedSortFilter(null);
        break;
      default:
        break;
    }
  };

  const filterTiles = useMemo(() => {
    return [
      ...(selectedSortFilter
        ? [
            {
              id: selectedSortFilter.id,
              name: selectedSortFilter.name,
              categoryType: CategoryTypeEnum.SORT,
            },
          ]
        : []),
      ...(selectedShopFilter
        ? [
            {
              id: selectedShopFilter.id,
              name: selectedShopFilter.name,
              categoryType: CategoryTypeEnum.SHOP,
            },
          ]
        : []),
      ...(selectedCategoryFilter
        ? selectedCategoryFilter.flatMap((parent) =>
            parent.children
              .filter((child) => child.selected)
              .map((selectedChild) => {
                return {
                  id: selectedChild.id,
                  name: selectedChild.name,
                  categoryType: CategoryTypeEnum.CATEGORY,
                };
              }),
          )
        : []),
    ];
  }, [selectedCategoryFilter, selectedShopFilter, selectedSortFilter]);

  return (
    <FilterContext.Provider
      value={{
        selectedCategoryFilter,
        selectedShopFilter,
        selectedSortFilter,
        selectedSimilarCategoryFilter,
        selectedBrandFilter,
        setSelectedCategoryFilter,
        setSelectedShopFilter,
        setSelectedSortFilter,
        setSelectedSimilarCategoryFilter,
        setSelectedBrandFilter,

        /**
         * Search
         */
        searchId,
        searchString,
        handleSearchString,
        handleSearchId,
        clearSearch,

        /**
         * Handlers:
         */
        handleSortCollection,
        handleShopCollection,
        handleSimilarCategoryCollection,
        handleCategoryCollection,
        handleFilterByBrandId,
        handleAll,
        buildFilterParams,
        clear,
        removeSingleFilter,

        /**
         * Helpers:
         */
        tiles: filterTiles,
      }}>
      {children}
    </FilterContext.Provider>
  );
};
