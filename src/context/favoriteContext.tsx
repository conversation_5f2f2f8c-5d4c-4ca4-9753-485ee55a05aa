import { createContext, ReactNode, useState } from 'react';

import { BookmarkEnum } from '~/enums/bookmarkEnum';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { IAddFavoriteMethod } from '~/hooks/useFavorites';
import { TContentApiTagType } from '~/redux/api/contentApi';

// interface IFavoriteContextModal extends IAddFavoriteMethod {
//   objectType: ObjectTypeEnum;
// }

interface IFavoriteContextModal {
  tag: TContentApiTagType;
  bookmark?: string;
  isFavorite: boolean | null;
  objectId: number;
  objectType: ObjectTypeEnum;
}

interface IFavoriteContext {
  show: boolean;
  setShow: (show: boolean) => void;
  data?: IFavoriteContextModal;
  setData: (data: IFavoriteContextModal) => void;
}

interface IFavoriteContextProvider {
  children: ReactNode;
}

const defaultContext: IFavoriteContext = {
  show: false,
  setShow: () => {},
  setData: () => {},
};

export const favoriteContext = createContext<IFavoriteContext>(defaultContext);

export const FavoriteContextProvider = ({ children }: IFavoriteContextProvider) => {
  const [show, setShow] = useState<boolean>(false);
  const [data, setData] = useState<IFavoriteContextModal | undefined>();

  return (
    <favoriteContext.Provider
      value={{
        show,
        setShow,
        setData,
        data,
      }}>
      {children}
    </favoriteContext.Provider>
  );
};
