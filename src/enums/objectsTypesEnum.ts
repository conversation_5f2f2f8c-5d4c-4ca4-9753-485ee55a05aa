import { articleChangeFavorite } from '~/redux/reducers/content/articles/actions';
import { brandChangeFavorite } from '~/redux/reducers/content/brands/actions';
import { couponChangeFavorite } from '~/redux/reducers/content/coupon/actions';
import { discountChangeFavorite } from '~/redux/reducers/content/discounts/actions';
import { inspirationChangeFavorite } from '~/redux/reducers/content/inspirations/actions';
import { newspaperChangeFavorite } from '~/redux/reducers/content/newspapers/actions';

export enum ObjectTypeEnum {
  Leaflet = 'leaflet',
  Inspiration = 'inspiration',
  Card = 'card',
  Coupon = 'coupon',
  Article = 'article',
  Brand = 'brand',
}

export const ObjectReduxTypeActionEnum = {
  [ObjectTypeEnum.Article]: articleChangeFavorite,
  [ObjectTypeEnum.Card]: discountChangeFavorite,
  [ObjectTypeEnum.Leaflet]: newspaperChangeFavorite,
  [ObjectTypeEnum.Inspiration]: inspirationChangeFavorite,
  [ObjectTypeEnum.Coupon]: couponChangeFavorite,
  [ObjectTypeEnum.Brand]: brandChangeFavorite,
};
