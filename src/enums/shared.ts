export enum ValueTypeEnum {
  OTHER = 0,
  PERCENTAGE_UP_TO = 1,
  PERCENTAGE_FROM = 2,
  PLN_UP_TO = 3,
  PLN_FROM = 4,
  PERCENTAGE_EQUAL = 5,
  PLN_EQUAL = 6,
}

export enum DiscountValueTypeEnum {
  PERCENTAGE = 0,
  PLN = 1,
}

// 0: 'Other'
// 1: 'Percentage (up to)'
// 2: 'Percentage (from)'
// 3: 'PLN (up to)'
// 4: 'PLN (from)'
// 5: 'Percentage (equal)'
// 6: 'PLN (equal)'
export enum DiscountTypeEnum {
  Txt = 'txt',
  Bar = 'bar',
  Qrc = 'qrc',
  Cal = 'cal',
  Ins = 'ins',
}

export enum ShopTypeEnum {
  STATIONARY = 0,
  ONLINE = 1,
  ALL = 2,
}

export const ShopTypeEnumShow = {
  0: 'stacjonarny',
  1: 'internetowy',
  2: 'stacjonarny / internetowy',
};

export enum CategoryTypeEnum {
  CATEGORY = 'category',
  SHOP = 'shop',
  SORT = 'sort',
}

export enum SortByEnum {
  ALPHABETICAL = 'alphabetical',
  BIGGEST_DISCOUNT = 'biggest_discount',
  EXPIRING = 'expiring',
  NEWEST = 'newest',
}

export enum IIntendedForEnum {
  All = 'all',
  Bsc = 'bsc',
  Prm = 'prm',
}
