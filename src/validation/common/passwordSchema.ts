import * as z from 'zod';

import { Message } from '~/validation/passwordChange/Message';

export const passwordSchema = z
  .string()
  .trim() // Usunięcie spacji przed i po tekście
  .min(8, { message: Message.password.min })
  .refine(
    (value) => {
      const hasDigit = /\d/.test(value);
      const hasLower = /[a-z]/.test(value);
      const hasUpper = /[A-Z]/.test(value);

      return hasDigit && hasLower && hasUpper;
    },
    { message: Message.password.min },
  );
