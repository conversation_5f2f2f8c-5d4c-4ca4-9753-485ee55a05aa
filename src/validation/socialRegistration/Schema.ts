import * as z from 'zod';

import { emailSchema } from '~/validation/common/emailSchema';
import { Message } from '~/validation/socialRegistration/Message';

export const phoneSchema = z
  .string()
  .trim() // Usunięcie spacji przed i po tekście
  .optional()
  .refine(
    (value) => {
      if (!value) return true;
      return /^\+\d{1,3}[0-9]{9}$/.test(value) || /^[0-9]{9}$/.test(value);
    },
    { message: Message.phone },
  );

export const formSchema = z.object({
  name: z
    .string()
    .trim()
    .nonempty({ message: Message.name.required })
    .min(2, { message: Message.min }),
  email: emailSchema,
  phone: phoneSchema,
  consents: z.boolean().refine((value) => value === true, { message: Message.consents }),
});
