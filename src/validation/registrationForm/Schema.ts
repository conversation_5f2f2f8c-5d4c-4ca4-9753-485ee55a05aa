import * as z from 'zod';

import { store } from '~/redux/store';
import { emailSchema } from '~/validation/common/emailSchema';
import { passwordSchema } from '~/validation/common/passwordSchema';
import { phoneSchema } from '~/validation/common/phoneSchema';
import { Message } from '~/validation/registrationForm/Message';
import { IConsents } from '~/validation/registrationForm/types';

const common = z
  .string()
  .trim()
  .nonempty({ message: Message.required })
  .min(3, { message: Message.min });

export const formSchema = z
  .object({
    name: common,
    email: emailSchema,
    phone: phoneSchema,
    password: passwordSchema,
    confirmPassword: z.string().trim(),
    verification_code: z.string().trim().optional(),
    consents: z.array(z.object({ code_name: z.string() })).refine(
      (consents) => {
        //TODO get required code names from API /user/consents
        const privacy = store.getState().privacy;
        const requiredCodeNames = privacy.consents
          .filter((obj) => obj.is_required === true)
          .map((obj) => obj.code_name);

        return requiredCodeNames.every((codeName) =>
          consents.some(
            (obj: IConsents) => obj.hasOwnProperty('code_name') && obj.code_name === codeName,
          ),
        );
      },
      { message: 'Brak wymaganych zgód' },
    ),
  })
  .superRefine(({ confirmPassword, password }, ctx) => {
    if (confirmPassword !== password) {
      ctx.addIssue({
        code: 'custom',
        message: 'Hasła nie są takie same',
        path: ['confirmPassword'],
      });
    }
  });
