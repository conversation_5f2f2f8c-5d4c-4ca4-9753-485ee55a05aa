import * as z from 'zod';

import { emailSchema } from '~/validation/common/emailSchema';
import { Message } from '~/validation/registrationForm/Message';

export const phoneSchema = z
  .string()
  .optional()
  .refine(
    (value) => {
      if (!value) return true;
      return /^\+\d{1,3}[0-9]{9}$/.test(value) || /^[0-9]{9}$/.test(value);
    },
    { message: Message.phone.format },
  );

export const formSchema = z.object({
  name: z.string(),
  surname: z.string(),
  email: emailSchema,
  phone: phoneSchema,
});
