import * as z from 'zod';

import { passwordSchema } from '~/validation/common/passwordSchema';
import { Message } from '~/validation/passwordChange/Message';

export const formSchema = z
  .object({
    password: z.string().min(3, { message: Message.password.min }),
    newPassword: passwordSchema,
    confirmPassword: z.string(),
  })
  .superRefine(({ confirmPassword, newPassword }, ctx) => {
    if (confirmPassword !== newPassword) {
      ctx.addIssue({
        code: 'custom',
        message: '<PERSON><PERSON> nie są takie same',
        path: ['confirmPassword'],
      });
    }
  });
