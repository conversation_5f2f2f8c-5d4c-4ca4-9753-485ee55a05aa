import { IItemProps } from '~/components/Item/types';
import { assets } from '~/theme/assets';

export const newspaperMock: IItemProps = {
  id: 1,
  itemContentProps: {
    text: 'Nazwa kuponu lorem ipsum dolor',
    durationText: 'Do odwołania',
  },
  itemHeaderProps: {
    bgImg: assets.images.newsletter_png,
    aspectRatio: 0.77,
    icon: true,
    imgLabelText: 'Jysk',
    imgLabel: assets.images.jysk_png,
  },
};

export const newspaperMockArray: IItemProps[] = [
  newspaperMock,
  newspaperMock,
  newspaperMock,
  newspaperMock,
  newspaperMock,
  newspaperMock,
  newspaperMock,
];
