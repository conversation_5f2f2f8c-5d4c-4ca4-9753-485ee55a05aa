import { IItemProps } from '~/components/Item/types';
import { assets } from '~/theme/assets';

export const couponMock: IItemProps = {
  id: 1,
  itemContentProps: {
    text: 'Nazwa kuponu lorem ipsum dolor',
    durationText: 'Do odwołania',
  },
  itemHeaderProps: {
    icon: true,
    bgLabel: '-20%',
    imgLabelText: 'Jysk',
    imgLabel: assets.images.jysk_png,
  },
};

export const couponMock2: IItemProps = {
  id: 2,
  itemContentProps: {
    text: 'Nazwa kuponu lorem ipsum dolor',
    durationText: 'Do odwołania',
  },
  itemHeaderProps: {
    icon: true,
    bgLabel: '-30%',
    imgLabelText: 'Leory Merlin',
    imgLabel: assets.images.leroymerlin_png,
  },
};

export const couponsMockArray: IItemProps[] = [
  couponMock,
  couponMock2,
  couponMock,
  couponMock2,
  couponMock,
  couponMock2,
  couponMock,
  couponMock2,
];
