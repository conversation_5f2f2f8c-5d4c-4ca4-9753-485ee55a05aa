import { IItemProps } from '~/components/Item/types';
import { assets } from '~/theme/assets';

export const brandMock: IItemProps = {
  id: 1,
  itemContentProps: {
    text: 'Home & You',
    durationText: 'lorem ipsum lorem',
  },
  itemHeaderProps: {
    bgImg: assets.images.homeandyou_png,
    aspectRatio: 1,
  },
};

export const brandMock2: IItemProps = {
  id: 1,
  itemContentProps: {
    text: 'Leroy Merlin',
    durationText: 'lorem ipsum lorem',
  },
  itemHeaderProps: {
    bgImg: assets.images.leroymerlin_png,
    aspectRatio: 1,
  },
};

export const brandsMockArray: IItemProps[] = [
  brandMock,
  brandMock2,
  brandMock,
  brandMock2,
  brandMock,
  brandMock2,
  brandMock,
];
