import { LanguageEnum } from '~/enums/legalEnum';

export interface ILegalContentVersion {
  language: LanguageEnum;
  content: string;
}

export interface IConsent {
  code_name: string;
}

export interface IConsentDetails extends IConsent {
  full_name: string;
  description?: string;
  is_required?: boolean;
  legal_content?: number | null;
}

export interface ILegal {
  name: string;
  updated_at: string;
  versions: ILegalContentVersion[];
  consents: IConsent[];
}
