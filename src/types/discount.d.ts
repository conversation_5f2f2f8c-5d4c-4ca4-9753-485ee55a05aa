import {
  DiscountTypeEnum,
  DiscountValueTypeEnum,
  IIntendedForEnum,
  ShopTypeEnum,
  ValueTypeEnum,
} from '~/enums/shared';
import { IBaseBrand } from '~/types/brands';

export interface IDiscount {
  id: number;
  name: string;
  valid_from?: string;
  valid_to?: string | null;
  bg_color: string | null;
  logo?: string | null;
  bg_image?: string | null;
  brand: IBaseBrand;
  custom_brand_name?: string | null;
  categories?: number[];
  value_type?: ValueTypeEnum;
  discount_value: string;
  is_favorite: boolean | null;
  tags: number[];
  is_rec?: boolean;
}

export interface ISingleDiscount {
  id: number;
  info?: string;
  discount_value_type: DiscountValueTypeEnum;
  discount_value: string;
  discount_type: DiscountTypeEnum;
  intended_for: IIntendedForEnum;
  instruction_title?: string | null;
  instruction?: string | null;
  button_text?: string | null;
  button_url?: string | null;
  phone_number?: string | null;
  image1?: string | null;
  image2?: string | null;
  affiliate_link?: string;
  is_rec?: boolean;
  code: string | null;
}

export interface IDiscountDetail extends IDiscount {
  type: ShopTypeEnum;
  discounts: ISingleDiscount[];
  description: string;
  site?: string;
}

export interface IDiscountCollections {
  count: number;
  next: string | null;
  previous: string | null;
  results: IDiscount[];
}

export interface IDiscountCode {
  code: string;
}
export interface IDiscountCodeAction {
  cardId?: number;
  discountId: number;
  response: {
    code: string;
  };
}
