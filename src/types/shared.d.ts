import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { ShopTypeEnum, SortByEnum } from '~/enums/shared';

export interface ParamsOptions {
  category_ids?: number[];
  is_favorite?: boolean;
  page?: number;
  page_size?: number;
  search?: string;
  sort_by?: SortByEnum;
  search_id?: number;
  search_string?: string;
  store_type?: ShopTypeEnum;
  brand_id?: number;
  content_type?: ObjectTypeEnum;
  object_type?: ObjectTypeEnum;
  object_id?: number;
  phrase?: string;
  city_id?: number | null;
  bookmark?: string | null;
  refresh?: boolean;
}
