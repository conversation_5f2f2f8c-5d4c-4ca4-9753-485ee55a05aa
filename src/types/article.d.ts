import { <PERSON>Base<PERSON>rand } from '~/types/brands';
import { IGallery } from '~/types/gallery';
import { IProduct } from '~/types/product';

export interface IArticle {
  id: number;
  title: string;
  brand: IBaseBrand;
  first_image: string;
  publish_date?: string;
  categories?: number[];
  is_favorite: boolean | null;
  tags: number[];
  is_rec?: boolean;
}

export interface IArticleDetails {
  id: number;
  title: string;
  brand: IBaseBrand;
  first_image: string;
  publish_date?: string;
  categories?: number[];
  tags?: number[];
  is_favorite: boolean | null;
  images: IGallery[];
  subtitle?: string;
  text?: string;
  products: IProduct[];
}

export interface IArticleCollections {
  count: number;
  next: string | null;
  previous: string | null;
  results: IArticle[];
}
