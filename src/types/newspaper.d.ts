import { IBaseBrand } from '~/types/brands';

export interface INewspaper {
  id: number;
  title: string;
  valid_from: string;
  valid_to?: string | null;
  brand: IBaseBrand;
  categories?: number[];
  tags?: number[];
  image?: string | null;
  is_favorite: boolean | null;
  is_rec?: boolean;
}

export interface INewspaperDetail extends INewspaper {
  file?: string | null;
}

export interface INewspaperCollections {
  count: number;
  next: string | null;
  previous: string | null;
  results: Array<INewspaper>;
}
