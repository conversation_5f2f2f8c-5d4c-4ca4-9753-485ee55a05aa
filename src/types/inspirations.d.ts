import { IBase<PERSON>rand } from '~/types/brands';
import { IGallery } from '~/types/gallery';
import { IProduct } from '~/types/product';

export interface IInspiration {
  id: number;
  brand: IBaseBrand;
  first_image: string;
  categories?: number[];
  tags?: number[];
  is_favorite: boolean | null;
  is_rec?: boolean;
}

export interface IInspirationDetails extends IInspiration {
  images: IGallery[];
  title: string;
  subtitle?: string;
  description?: string;
  products: IProduct[];
}

export interface IInspirationCollections {
  count: number;
  next: string | null;
  previous: string | null;
  results: Array<IInspiration>;
}
