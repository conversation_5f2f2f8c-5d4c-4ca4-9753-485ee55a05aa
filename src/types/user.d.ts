import { NotifyTypes } from '~/enums/notifyTypes';
import { IConsents } from '~/validation/registrationForm/types';

export interface IToken {
  access: string;
  refresh: string;
}

export interface IDetail {
  first_name: string;
  last_name: string;
  phone_number: string;
  city: number | null;
  city_name: string | null;
}

export interface IPremium {
  code: string;
  valid_to: string;
  partner_logo?: string;
}

export interface IMe {
  email: string;
  is_verified: boolean;
  auth_provider: string;
}

export interface IMeConsents {
  consents: [
    {
      code_name: string;
      legal_content: number;
    },
  ];
}

export interface INotifySettings {
  type: NotifyTypes;
  status: boolean;
}

export type UserCreate = {
  email: string;
  password: string;
  consents: Array<IConsents>;
  profile: {
    first_name: string;
    last_name?: string;
    phone_number?: string;
  };
  verification_code?: string;
};

export interface IUserSocialResponse {
  email: string;
  tokens: {
    access: string;
    refresh: string;
  };
}
