import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { ShopTypeEnum, SortByEnum } from '~/enums/shared';

export interface ICategorySort {
  id: number;
  name: string;
  selected: boolean;
  type: SortByEnum;
  displayFor: ObjectTypeEnum[];
}

export interface ICategoryShop {
  id: number;
  name: string;
  type: ShopTypeEnum;
  selected: boolean;
  displayFor: ObjectTypeEnum[];
}

export interface IChildrenCategory {
  id: number;
  name: string;
  count: number | null;
  selected: boolean | null;
}

export interface IParentCategory {
  id: number;
  name: string;
  selected: boolean;
  children: IChildrenCategory[];
}
