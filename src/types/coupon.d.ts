import { ValueTypeEnum } from '~/enums/shared';
import { IBaseBrand } from '~/types/brands';

export interface ICoupon {
  id: number;
  title: string;
  start_date: string;
  end_date?: string | null;
  brand: IBaseBrand;
  custom_brand_name?: string | null;
  discount_value: string;
  categories?: number[];
  value_type?: ValueTypeEnum;
  bg_color: string | null;
  logo?: string | null;
  bg_image: string | null;
  is_favorite: boolean | null;
  tags: number[];
  is_rec?: boolean;
}

export interface ICouponDetail extends ICoupon {
  title_description?: string;
  short_description?: string;
  affiliate_link: string;
  coupon_string?: string | null;
  shop_type?: number;
}

export interface ICouponCollections {
  count: number;
  next: string | null;
  previous: string | null;
  results: ICoupon[];
}
