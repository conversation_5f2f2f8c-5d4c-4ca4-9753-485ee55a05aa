import { ShopTypeEnum } from '~/enums/shared';

export interface IBaseBrand {
  id: number;
  name: string;
  logo?: string | null;
  bg_color?: string;
  is_favorite: boolean | null;
  categories?: number[];
  tags?: number[];
  is_rec?: boolean;
}

export interface IBrandDetail extends IBaseBrand {
  image?: string | null;
  description?: string;
  site?: string;
  shop_type?: ShopTypeEnum | null;
  site_btn_text?: string | null;
}

export interface IBrandCollections {
  count: number;
  next: string | null;
  previous: string | null;
  results: Array<IBaseBrand>;
}
