export interface IEventEnum {
  path: string;
  auth: boolean;
}

export const EventsEnum = {
  INTERACTION: {
    path: 'interaction',
    auth: true,
  },
  ADD_FAVORITES: {
    path: 'addfavorites',
    auth: true,
  },
  CONTACT_ACTION: {
    path: 'contactaction',
    auth: false,
  },
  COPY_CODE_CLIPBOARD: {
    path: 'copycodeclipboard',
    auth: false,
  },
  DISPLAY_CARD: {
    path: 'displaycard',
    auth: false,
  },
  DISPLAY_CONTENT_TYPE: {
    path: 'displaycontenttype',
    auth: false,
  },
  FILTER_CATEGORIES_CHANGE: {
    path: 'filtercategorieschange',
    auth: false,
  },
  FILTER_SHOP_CHANGE: {
    path: 'filtershopchange',
    auth: false,
  },
  FILTER_STATUS: {
    path: 'filterstatus',
    auth: false,
  },
  NOTIFICATION_CHANGE: {
    path: 'notificationchange',
    auth: true,
  },
  NOTIFICATION_VIEW: {
    path: 'notificationview',
    auth: true,
  },
  PASSWORD_CHANGE_LOG: {
    path: 'passwordchangelog',
    auth: true,
  },
  PASSWORD_RESET_LOG: {
    path: 'passwordresetlog',
    auth: false,
  },
  PREMIUM_CODE_REGISTRATION: {
    path: 'premiumcoderegistration',
    auth: true,
  },
  REDIRECTION_CALL: {
    path: 'redirectioncall',
    auth: false,
  },
  REDIRECTION_NAVI: {
    path: 'redirectionnavi',
    auth: false,
  },
  REDIRECTION_ONLINESHOP: {
    path: 'redirectiononlineshop',
    auth: false,
  },
  SEARCH_STRING: {
    path: 'searchstring',
    auth: false,
  },
  SESSION_START: {
    path: 'sessionstart',
    auth: false,
  },
  SESSION_STOP: {
    path: 'sessionstop',
    auth: false,
  },
  USE_DISCOUNT: {
    path: 'usediscount',
    auth: true,
  },
  USER_ACCOUNT_CHANGE: {
    path: 'useraccountchange',
    auth: true,
  },
  USER_ACCOUNT_CREATE: {
    path: 'useraccountcreate',
    auth: true,
  },
  USER_CITY_CHANGE: {
    path: 'usercitychange',
    auth: true,
  },
  USER_LOGIN_STATUS: {
    path: 'userloginstatus',
    auth: true,
  },
};
