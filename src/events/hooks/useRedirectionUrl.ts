import { useContext } from 'react';

import { eventsContext } from '~/context/eventsContext';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { EventsEnum } from '~/events/EventsEnum';

export const useRedirectionUrl = (fn: (data: any) => void) => {
  const { events } = useContext(eventsContext);

  const wrappedFn = (
    brandId: number | undefined,
    objectId: number | undefined,
    objectType: ObjectTypeEnum | undefined,
    url: string | undefined,
  ) => {
    if (!url) return;
    events?.redirectionOnlineShop(
      {
        shop_id: brandId || -1,
        object_id: objectId || -1,
        object_type: objectType || 'null',
        url: url,
      },
      EventsEnum.REDIRECTION_ONLINESHOP,
    );

    return fn(url);
  };

  return wrappedFn;
};
