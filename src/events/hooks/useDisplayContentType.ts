import { useFocusEffect } from '@react-navigation/native';
import { useCallback, useContext } from 'react';

import { eventsContext } from '~/context/eventsContext';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { EventsEnum } from '~/events/EventsEnum';

interface IUseDisplayContentType {
  type: ObjectTypeEnum;
  collection: any;
}

export const useDisplayContentType = ({ type, collection }: IUseDisplayContentType) => {
  const { events } = useContext(eventsContext);
  let categories: Array<number> = [];
  let tags: Array<number> = [];

  const handleGetCategories = async () => {
    collection.results.map(async (item: any) => {
      categories = [...categories, ...item.categories];
    });
  };

  const handleGetTags = async () => {
    collection.results.map(async (item: any) => {
      tags = [...tags, ...item.tags];
    });
  };

  const handleSendEvent = () => {
    if (collection) {
      events?.displayContentType(
        {
          object_type: type,
          categories_id_list: categories,
          tags_id_list: tags,
        },
        EventsEnum.DISPLAY_CONTENT_TYPE,
      );
    }
  };

  useFocusEffect(
    useCallback(() => {
      if (collection) {
        Promise.all([handleGetCategories(), handleGetTags()]).then(() => {
          handleSendEvent();
        });
      }

      return () => {
        categories = [];
        tags = [];
      };
    }, [collection]),
  );
};
