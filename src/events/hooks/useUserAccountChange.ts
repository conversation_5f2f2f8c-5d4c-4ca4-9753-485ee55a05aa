import { useContext } from 'react';

import { eventsContext } from '~/context/eventsContext';
import { EventsEnum } from '~/events/EventsEnum';
import { useAppSelector } from '~/redux/store';
import { IMeConsents } from '~/types/user';

export const useUserAccountChange = () => {
  const { premium } = useAppSelector((state) => state.user);
  const { events } = useContext(eventsContext);

  const fn = async (
    consents: IMeConsents,
    user_parameters: { first_name: string; last_name: string; phone_number: string },
  ) => {
    events?.userAccountChange(
      {
        accept_marketing: consents.consents.some((consent) => consent.code_name === 'marketing'),
        user_parameters: user_parameters,
        account_state: premium ? 'premium' : 'basic',
      },
      EventsEnum.USER_ACCOUNT_CHANGE,
    );
  };

  return fn;
};
