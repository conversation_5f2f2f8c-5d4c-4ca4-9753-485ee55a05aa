import AsyncStorage from '@react-native-async-storage/async-storage';
import { useEffect, useRef } from 'react';
import { AppState, AppStateStatus, Platform } from 'react-native';

import { useSessionStart } from '~/events/hooks/useSessionStart';
import { useSessionStop } from '~/events/hooks/useSessionStop';
import { useNavigate } from '~/hooks/useNavigate';

export const useAppState = () => {
  const appState = useRef(AppState.currentState);
  const { navigateToHome } = useNavigate();
  const { handleSendStart } = useSessionStart();
  const { handleSendStop } = useSessionStop();

  const checkTimeAndHandleStart = async () => {
    const lastTime = await AsyncStorage.getItem('lastSessionStop');
    const currentTime = new Date().getTime();

    if (lastTime) {
      const timeDiff = currentTime - new Date(lastTime).getTime();

      if (timeDiff > 12 * 3600000) {
        navigateToHome();
      }
    }
    handleSendStart();
  };

  const handleSessionStop = async () => {
    await AsyncStorage.setItem('lastSessionStop', new Date().toISOString());
    handleSendStop();
  };

  useEffect(() => {
    const handleAppStateChange = async (nextAppState: AppStateStatus) => {
      if (appState.current.match(/inactive|background/) && nextAppState === 'active') {
        await checkTimeAndHandleStart();
      } else if (
        appState.current.match(/active/) &&
        (nextAppState === 'background' || nextAppState === 'inactive')
      ) {
        if (Platform.OS === 'ios') {
          if (nextAppState === 'inactive') {
            await handleSessionStop();
          }
        } else {
          await handleSessionStop();
        }
      }

      appState.current = nextAppState;
    };
    checkTimeAndHandleStart();
    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      subscription.remove();
    };
  }, []);
};
