import { useContext } from 'react';

import { eventsContext } from '~/context/eventsContext';
import { EventsEnum } from '~/events/EventsEnum';

export const useUserLoginStatus = () => {
  const { events } = useContext(eventsContext);

  const fn = async (token?: string, logout?: boolean) => {
    events?.setToken(token);
    await events?.userLoginStatus(
      {
        login_state: !logout,
        login_type: 0,
      },
      EventsEnum.USER_LOGIN_STATUS,
    );
  };

  return fn;
};
