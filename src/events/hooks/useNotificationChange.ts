import { useContext, useEffect, useRef } from 'react';

import { eventsContext } from '~/context/eventsContext';
import { EventsEnum } from '~/events/EventsEnum';
import { useAppSelector } from '~/redux/store';

export const useNotificationChange = () => {
  const { events } = useContext(eventsContext);
  const { notification_settings } = useAppSelector((state) => state.user);
  const redux = useRef(notification_settings);
  const prevReducerStateRef = useRef<any>(null);

  useEffect(() => {
    prevReducerStateRef.current = redux.current;
    return () => {
      if (redux.current !== prevReducerStateRef.current) {
        events?.notificationChange(
          {
            notification_status: JSON.stringify(redux.current),
          },
          EventsEnum.NOTIFICATION_CHANGE,
        );
      }
    };
  }, []);

  useEffect(() => {
    redux.current = notification_settings;
  }, [notification_settings]);
};
