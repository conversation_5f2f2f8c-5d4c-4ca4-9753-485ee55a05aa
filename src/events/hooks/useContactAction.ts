import { useContext, useEffect } from 'react';

import { eventsContext } from '~/context/eventsContext';
import { userTypesEnum } from '~/events/enums/userTypesEnum';
import { EventsEnum } from '~/events/EventsEnum';
import { useAppSelector } from '~/redux/store';

export const useContactAction = () => {
  const { events } = useContext(eventsContext);
  const { me } = useAppSelector((state) => state.user);
  useEffect(() => {
    events?.contactAction(
      {
        login_type: me?.auth_provider ? userTypesEnum[me?.auth_provider] : -1,
      },
      EventsEnum.CONTACT_ACTION,
    );
  }, []);
};
