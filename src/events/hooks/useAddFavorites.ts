import { useContext } from 'react';

import { eventsContext } from '~/context/eventsContext';
import { EventsEnum } from '~/events/EventsEnum';
import { IAddFavorites } from '~/events/types/addFavorites';

export const useAddFavorites = () => {
  const { events } = useContext(eventsContext);

  const handleSendEvent = (content: Omit<IAddFavorites, 'user_id'>) => {
    events?.addFavorite(content, EventsEnum.ADD_FAVORITES);
  };

  return {
    handleSendEvent,
  };
};
