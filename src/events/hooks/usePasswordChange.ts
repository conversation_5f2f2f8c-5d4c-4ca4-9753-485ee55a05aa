import { useContext } from 'react';

import { eventsContext } from '~/context/eventsContext';
import { userTypesEnum } from '~/events/enums/userTypesEnum';
import { EventsEnum } from '~/events/EventsEnum';
import { useAppSelector } from '~/redux/store';

export const usePasswordChange = () => {
  const { me } = useAppSelector((state) => state.user);
  const { events } = useContext(eventsContext);

  const handlePasswordChange = () => {
    events?.passwordChange(
      {
        login_type: me?.auth_provider ? userTypesEnum[me?.auth_provider] : -1,
      },
      EventsEnum.PASSWORD_CHANGE_LOG,
    );
  };

  return {
    handlePasswordChange,
  };
};
