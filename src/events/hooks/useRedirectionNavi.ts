import { useContext } from 'react';

import { eventsContext } from '~/context/eventsContext';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { EventsEnum } from '~/events/EventsEnum';

export const useRedirectionNavi = (fn: (data: any) => void) => {
  const { events } = useContext(eventsContext);

  const wrappedFn = (
    data: any,
    shopId: number,
    objectId: number | undefined,
    objectType: ObjectTypeEnum | undefined,
  ) => {
    if (!objectId || !objectType) return fn(data);
    events?.redirectionNavi(
      {
        shop_id: shopId,
        object_id: objectId,
        object_type: objectType,
      },
      EventsEnum.REDIRECTION_NAVI,
    );
    return fn(data);
  };
  return wrappedFn;
};
