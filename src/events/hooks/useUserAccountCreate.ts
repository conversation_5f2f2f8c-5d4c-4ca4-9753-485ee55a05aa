import { useContext } from 'react';

import { eventsContext } from '~/context/eventsContext';
import { EventsEnum } from '~/events/EventsEnum';
import { UserCreate } from '~/types/user';
import { IConsents } from '~/validation/registrationForm/types';

export const useUserAccountCreate = () => {
  const { events } = useContext(eventsContext);

  const fn = async (consents: IConsents[], user_parameters: UserCreate, token: string) => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...rest } = user_parameters;
    events?.setToken(token);
    await events?.userAccountCreate(
      {
        accept_marketing: consents.some((consent) => consent.code_name === 'marketing'),
        user_parameters: rest,
      },
      EventsEnum.USER_ACCOUNT_CREATE,
    );
  };

  return fn;
};
