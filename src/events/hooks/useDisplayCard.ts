import { useContext } from 'react';

import { eventsContext } from '~/context/eventsContext';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { EventsEnum } from '~/events/EventsEnum';

interface IUseDisplayCard {
  type: ObjectTypeEnum;
  detail: any;
  is_rec: boolean;
}

export const useDisplayCard = ({ type }: Pick<IUseDisplayCard, 'type'>) => {
  const { events } = useContext(eventsContext);

  const handleDisplayCard = ({ detail, is_rec }: Omit<IUseDisplayCard, 'type'>) => {
    events?.displayCard(
      {
        object_type: type,
        object_id: detail.id,
        categories_id_list: detail.categories || [],
        tags_id_list: detail.tags || [],
        was_predicted: is_rec,
      },
      EventsEnum.DISPLAY_CARD,
    );
  };

  return {
    handleDisplayCard,
  };
};
