import { useContext } from 'react';

import { eventsContext } from '~/context/eventsContext';
import { EventsEnum } from '~/events/EventsEnum';
import { IPremium } from '~/types/user';

export const usePremiumCodeRegistartion = () => {
  const { events } = useContext(eventsContext);

  const fn = (premium: IPremium | null) => {
    events?.premiumCodeRegistration(
      {
        account_state_before: premium ? 'premium' : 'basic',
      },
      EventsEnum.PREMIUM_CODE_REGISTRATION,
    );
  };

  return fn;
};
