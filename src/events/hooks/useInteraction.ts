import { useFocusEffect } from '@react-navigation/native';
import { useCallback, useContext } from 'react';

import { eventsContext } from '~/context/eventsContext';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { EventsEnum } from '~/events/EventsEnum';

interface IUseInteraction {
  object_type: ObjectTypeEnum;
  object_id: number;
}

export const useInteraction = ({ object_type, object_id }: IUseInteraction) => {
  const { events, routeName } = useContext(eventsContext);

  const handleDepp = () => {
    events?.interaction(
      {
        object_type: object_type,
        object_id: object_id,
        deep_interaction: true,
      },
      EventsEnum.INTERACTION,
    );
  };

  useFocusEffect(
    useCallback(() => {
      /**
       * Zabezpieczenie sytuacji w której ktoś cofa się z modalu z kodem
       */
      if (routeName.current !== 'DiscountCode') {
        events?.interaction(
          {
            object_type: object_type,
            object_id: object_id,
            deep_interaction: false,
          },
          EventsEnum.INTERACTION,
        );
      }
    }, []),
  );

  return {
    handleDepp,
  };
};
