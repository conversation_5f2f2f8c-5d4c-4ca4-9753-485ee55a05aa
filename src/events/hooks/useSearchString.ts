import { useContext } from 'react';

import { eventsContext } from '~/context/eventsContext';
import { EventsEnum } from '~/events/EventsEnum';

export const useSearchString = () => {
  const { events } = useContext(eventsContext);

  const _fnEvent = (text_string: string, search_count: number) => {
    events?.searchString(
      {
        text_string: text_string,
        search_count: search_count,
      },
      EventsEnum.SEARCH_STRING,
    );
  };
  return { _fnEvent };
};
