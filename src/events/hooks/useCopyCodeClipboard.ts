import { useContext } from 'react';

import { eventsContext } from '~/context/eventsContext';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { EventsEnum } from '~/events/EventsEnum';

export const useCopyCodeClipboard = () => {
  const { events } = useContext(eventsContext);

  const wrappedFn = (
    brandId: number | undefined,
    objectId: number | undefined,
    objectType: ObjectTypeEnum | undefined,
  ) => {
    events?.copyCodeClipboard(
      {
        shop_id: brandId || 0,
        object_id: objectId || -1,
        object_type: objectType || 'null',
      },
      EventsEnum.COPY_CODE_CLIPBOARD,
    );
  };

  return wrappedFn;
};
