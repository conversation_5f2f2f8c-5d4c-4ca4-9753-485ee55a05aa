import { useContext } from 'react';

import { eventsContext } from '~/context/eventsContext';
import { EventsEnum } from '~/events/EventsEnum';

export const useSessionStop = () => {
  const { events } = useContext(eventsContext);

  const handleSendStop = () => {
    events?.sessionStop(
      {
        login_type: 0,
      },
      EventsEnum.SESSION_STOP,
    );
  };

  return {
    handleSendStop,
  };
};
