import { useContext } from 'react';

import { eventsContext } from '~/context/eventsContext';
import { EventsEnum } from '~/events/EventsEnum';

export const useUserCityChange = () => {
  const { events } = useContext(eventsContext);

  const fn = (city_geo: string | null) => {
    events?.userCityChange(
      {
        city_geo: city_geo || 'Cała Polska',
      },
      EventsEnum.USER_CITY_CHANGE,
    );
  };

  return fn;
};
