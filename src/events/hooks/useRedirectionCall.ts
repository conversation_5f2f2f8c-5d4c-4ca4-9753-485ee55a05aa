import { useContext } from 'react';

import { eventsContext } from '~/context/eventsContext';
import { EventsEnum } from '~/events/EventsEnum';

export const useRedirectionCall = (fn: (data: any) => void) => {
  const { events } = useContext(eventsContext);

  const wrappedFn = (data: any, shopId: number) => {
    events?.redirectionCall(
      {
        shop_id: shopId,
      },
      EventsEnum.REDIRECTION_CALL,
    );
    return fn(data);
  };

  return wrappedFn;
};
