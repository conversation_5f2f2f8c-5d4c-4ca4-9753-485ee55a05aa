import { useContext, useEffect, useRef } from 'react';

import { eventsContext } from '~/context/eventsContext';
import { EventsEnum } from '~/events/EventsEnum';

export const useFilterCategoriesChange = (ids: number[]) => {
  const { events } = useContext(eventsContext);
  const prevIdsRef = useRef<number[]>([]);

  useEffect(() => {
    const prevIds = prevIdsRef.current;
    if (JSON.stringify(prevIds) !== JSON.stringify(ids)) {
      prevIdsRef.current = ids;
      events?.filterCategoriesChange(
        {
          categories_id_list: ids,
        },
        EventsEnum.FILTER_CATEGORIES_CHANGE,
      );
    }
  }, [ids]);
};
