import { useContext, useEffect } from 'react';

import { eventsContext } from '~/context/eventsContext';
import { CategoryTypeEnum, ShopTypeEnum } from '~/enums/shared';
import { EventsEnum } from '~/events/EventsEnum';

export const useFilterShopChange = (type?: ShopTypeEnum) => {
  const { events } = useContext(eventsContext);

  useEffect(() => {
    if (type) {
      events?.filterShopChange(
        {
          shop_state: type,
        },
        EventsEnum.FILTER_SHOP_CHANGE,
      );
    }
  }, [type]);
};
