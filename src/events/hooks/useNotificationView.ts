import { useFocusEffect } from '@react-navigation/native';
import { useCallback, useContext } from 'react';

import { eventsContext } from '~/context/eventsContext';
import { EventsEnum } from '~/events/EventsEnum';

export const useNotificationView = () => {
  const { events } = useContext(eventsContext);

  useFocusEffect(
    useCallback(() => {
      events?.notificationView(EventsEnum.NOTIFICATION_VIEW);
    }, []),
  );
};
