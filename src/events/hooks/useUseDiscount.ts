import { useContext, useEffect } from 'react';

import { eventsContext } from '~/context/eventsContext';
import { EventsEnum } from '~/events/EventsEnum';

type Props = {
  shop_id: number;
  card_id: number | undefined;
  discount_id: number | undefined;
};
export const useUseDiscount = ({ shop_id, card_id, discount_id }: Props) => {
  const { events } = useContext(eventsContext);

  useEffect(() => {
    if (card_id && discount_id) {
      events?.useDiscount(
        {
          shop_id: shop_id,
          card_id: card_id,
          discount_id: discount_id,
        },
        EventsEnum.USE_DISCOUNT,
      );
    }
  }, []);
};
