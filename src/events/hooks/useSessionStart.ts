import { useContext } from 'react';

import { eventsContext } from '~/context/eventsContext';
import { EventsEnum } from '~/events/EventsEnum';

export const useSessionStart = () => {
  const { events } = useContext(eventsContext);

  const handleSendStart = () => {
    events?.sessionStart(
      {
        login_type: 0,
      },
      EventsEnum.SESSION_START,
    );
  };

  return {
    handleSendStart,
  };
};
