import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

import { IContactAction } from './types/contactAction';
import { ICopyCodeClipboard } from './types/copyCodeClipboard';
import { IFiltersCategoriesChange } from './types/filtersCategoriesChange';
import { IFilterShopChange } from './types/filterShopChange';
import { IPremiumCodeRegistration } from './types/premiumCodeRegistration';
import { IRedirectionCall } from './types/redirectionCall';
import { IRedirectionNavi } from './types/redirectionNavi';
import { IRedirectionOnlineShop } from './types/redirectionOnLineshop';
import { ISearchString } from './types/searchString';
import { IUseDiscount } from './types/useDiscount';
import { IUserAccountChange } from './types/userAccountChange';
import { IUserAccountCreate } from './types/userAccountCreate';
import { IUserCityChange } from './types/userCityChange';
import { IUserLoginStatus } from './types/userLoginStatus';

import { SERVER_URL } from '~/config.api';
import { IEventEnum } from '~/events/EventsEnum';
import { IAddFavorites } from '~/events/types/addFavorites';
import { IDisplayCard } from '~/events/types/displayCard';
import { IDisplayContentType } from '~/events/types/displayContentType';
import { IInteraction } from '~/events/types/interaction';
import { INotificationChange } from '~/events/types/notificationChange';
import { INotificationView } from '~/events/types/notificationView';
import { IPasswordChangeLog } from '~/events/types/passwordChangeLog';
import { IPasswordResetLog } from '~/events/types/passwordResetLog';
import { ISessionStart } from '~/events/types/sessionStart';
import { ISessionStop } from '~/events/types/sessionStop';
import { generateAnonimHash } from '~/helpers/hash';
import axios from '~/utils/axios.config';

export interface ISendEvent {
  path: IEventEnum;
  body: any;
}

export class Events {
  //TODO USER ID CONDITIONAL
  protected userId?: string;
  protected device: string;
  protected geo: string;
  protected token?: string;

  constructor(token?: string) {
    if (!token) {
      this.setUserId();
    } else {
      this.token = token;
    }
    this.device = Platform.OS;
    this.geo = 'poland';
  }

  sendEvent({ path, body }: ISendEvent) {
    if (path.auth && !this.token) return null;
    else if (this.token) {
      axios({
        method: 'POST',
        url: `${SERVER_URL}/interactions/${path.path}/`,
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        data: body,
      }).catch(() => {});
    } else {
      if (!this.userId) {
        this.setUserId().then(() => {
          axios({
            method: 'POST',
            url: `${SERVER_URL}/interactions/${path.path}/`,
            headers: {
              'Content-Type': 'application/json',
              Accept: 'application/json',
            },
            data: {
              ...body,
              user_id: this.userId,
            },
          }).catch(() => {});
        });
      } else {
        axios({
          method: 'POST',
          url: `${SERVER_URL}/interactions/${path.path}/`,
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
          },
          data: {
            ...body,
            user_id: this.userId,
          },
        }).catch(() => {});
      }
    }
  }

  async promiseSendEvent({ path, body }: ISendEvent) {
    //TODO ADD USER OPTIONAL TO BODY
    if (path.auth && !this.token) return null;
    else if (this.token) {
      await axios({
        method: 'POST',
        url: `${SERVER_URL}/interactions/${path.path}/`,
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        data: body,
      }).catch(() => {});
    } else {
      await axios({
        method: 'POST',
        url: `${SERVER_URL}/interactions/${path.path}/`,
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        data: {
          ...body,
          user_id: this.userId,
        },
      }).catch(() => {});
    }
  }

  interaction(content: IInteraction, path: IEventEnum) {
    this.sendEvent({
      path: path,
      body: content,
    });
  }

  displayCard(content: Omit<IDisplayCard, 'user_id' | 'device' | 'geo'>, path: IEventEnum) {
    const body: IDisplayCard = {
      ...content,
      geo: this.geo,
      device: this.device,
    };
    this.sendEvent({
      path: path,
      body: body,
    });
  }

  displayContentType(content: IDisplayContentType, path: IEventEnum) {
    const body: IDisplayContentType = {
      ...content,
    };
    this.sendEvent({
      path: path,
      body: body,
    });
  }

  notificationView(path: IEventEnum) {
    //TODO USER ID
    const body: INotificationView = {};

    this.sendEvent({
      path: path,
      body: body,
    });
  }

  notificationChange(content: Pick<INotificationChange, 'notification_status'>, path: IEventEnum) {
    const body: INotificationChange = {
      ...content,
      device: this.device,
      geo: this.geo,
    };

    this.sendEvent({
      path: path,
      body: body,
    });
  }

  sessionStart(content: Pick<ISessionStart, 'login_type'>, path: IEventEnum) {
    const body: ISessionStart = {
      ...content,
      device: this.device,
      geo: this.geo,
    };

    this.sendEvent({
      path: path,
      body: body,
    });
  }

  sessionStop(content: Pick<ISessionStart, 'login_type'>, path: IEventEnum) {
    const body: ISessionStop = {
      ...content,
      device: this.device,
      geo: this.geo,
    };

    this.sendEvent({
      path: path,
      body: body,
    });
  }

  filterCategoriesChange(content: Omit<IFiltersCategoriesChange, 'login_state'>, path: IEventEnum) {
    const body: IFiltersCategoriesChange = {
      ...content,
      geo: this.geo,
      login_state: !!this.token,
    };

    this.sendEvent({
      path: path,
      body: body,
    });
  }

  filterShopChange(content: Omit<IFilterShopChange, 'login_state'>, path: IEventEnum) {
    const body: IFilterShopChange = {
      ...content,
      geo: this.geo,
      login_state: !!this.token,
    };

    this.sendEvent({
      path: path,
      body: body,
    });
  }

  searchString(content: Omit<ISearchString, 'login_state' | 'device'>, path: IEventEnum) {
    const body: ISearchString = {
      ...content,
      geo: this.geo,
      device: this.device,
      login_state: !!this.token,
    };

    this.sendEvent({
      path: path,
      body: body,
    });
  }

  filterStatus(path: IEventEnum) {
    this.sendEvent({
      body: {},
      path: path,
    });
  }

  addFavorite(content: Omit<IAddFavorites, 'user_id'>, path: IEventEnum) {
    this.sendEvent({
      path: path,
      body: content,
    });
  }

  passwordReset(path: IEventEnum) {
    const body: IPasswordResetLog = {
      device: this.device,
      geo: this.geo,
      login_type: 1,
    };

    this.sendEvent({
      path: path,
      body: body,
    });
  }

  redirectionCall(content: Omit<IRedirectionCall, 'user_id'>, path: IEventEnum) {
    this.sendEvent({
      path: path,
      body: content,
    });
  }

  passwordChange(content: Pick<IPasswordChangeLog, 'login_type'>, path: IEventEnum) {
    const body = {
      ...content,
      device: this.device,
      geo: this.geo,
    };

    this.sendEvent({
      path: path,
      body: body,
    });
  }

  redirectionNavi(content: Omit<IRedirectionNavi, 'user_id'>, path: IEventEnum) {
    this.sendEvent({
      path: path,
      body: content,
    });
  }

  redirectionOnlineShop(content: Omit<IRedirectionOnlineShop, 'user_id'>, path: IEventEnum) {
    this.sendEvent({
      path: path,
      body: content,
    });
  }

  copyCodeClipboard(content: Omit<ICopyCodeClipboard, 'user_id'>, path: IEventEnum) {
    this.sendEvent({
      path: path,
      body: content,
    });
  }

  useDiscount(content: Omit<IUseDiscount, 'user_id' | 'login_state'>, path: IEventEnum) {
    const body: IUseDiscount = {
      ...content,
      login_state: !!this.token,
    };

    this.sendEvent({
      path: path,
      body: body,
    });
  }

  contactAction(
    content: Omit<IContactAction, 'user_id' | 'login_state' | 'device'>,
    path: IEventEnum,
  ) {
    const body: IContactAction = {
      ...content,
      device: this.device,
      geo: this.geo,
      login_state: !!this.token,
    };

    this.sendEvent({
      path: path,
      body: body,
    });
  }

  async userLoginStatus(content: Omit<IUserLoginStatus, 'user_id' | 'device'>, path: IEventEnum) {
    const body: IUserLoginStatus = {
      ...content,
      device: this.device,
      geo: this.geo,
    };

    await this.promiseSendEvent({
      path: path,
      body: body,
    });
  }

  userCityChange(content: IUserCityChange, path: IEventEnum) {
    const body: IUserCityChange = {
      ...content,
      geo: this.geo,
    };

    this.sendEvent({
      path: path,
      body: body,
    });
  }

  async userAccountCreate(content: Omit<IUserAccountCreate, 'geo' | 'device'>, path: IEventEnum) {
    const body: IUserAccountCreate = {
      ...content,
      geo: this.geo,
      device: this.device,
    };

    await this.promiseSendEvent({
      path: path,
      body: body,
    });
  }

  premiumCodeRegistration(content: IPremiumCodeRegistration, path: IEventEnum) {
    this.sendEvent({
      path: path,
      body: content,
    });
  }

  userAccountChange(content: IUserAccountChange, path: IEventEnum) {
    this.sendEvent({
      path: path,
      body: content,
    });
  }

  /*
   *  Setters
   */
  async setUserId() {
    let hash = await AsyncStorage.getItem('anonimHash');
    if (!hash) {
      hash = generateAnonimHash();
      await AsyncStorage.setItem('anonimHash', hash);
    }
    this.userId = hash;
  }

  setToken(token?: string) {
    this.token = token;
  }
}
