import { defaultValues } from '~/redux/reducers/content/favorites/initialState';
import {
  IFavoriteItemAll,
  IFavoritesChange,
  IFavoritesCollection,
} from '~/redux/reducers/content/favorites/types';

export interface IFavoriteHelper {
  state: any | null;
  objectId: number;
  isFavorite: boolean | null;
  bookmark: string | null;
}

export interface IFavoriteHelperDelete {
  objectId: number;
  objectType: string;
  collection: IFavoritesCollection;
}

export interface IFavoriteGetAllHelper {
  results: Array<IFavoriteItemAll>;
}

export interface IFavoriteChangeAllHelper extends IFavoritesChange {
  collection: IFavoritesCollection;
}

export const favoriteHelper = ({
  state,
  objectId,
  isFavorite,
}: Omit<IFavoriteHelper, 'bookmark'>) => {
  return state.map((item: any) => {
    if (item.id === objectId) {
      return { ...item, is_favorite: isFavorite };
    }
    return item;
  });
};

export const favoriteSingleHelper = ({
  state,
  objectId,
  isFavorite,
}: Omit<IFavoriteHelper, 'bookmark'>) => {
  if (state !== null) {
    if (state.id) {
      if (state.id === objectId) {
        return { ...state, is_favorite: isFavorite };
      }
    }
  }
  return state;
};

export const favoriteCacheHelper = ({
  state,
  objectId,
  isFavorite,
}: Omit<IFavoriteHelper, 'bookmark'>) => {
  if (state[objectId] !== undefined) {
    return {
      ...state,
      [objectId]: {
        data: {
          ...state[objectId].data,
          isFavorite: isFavorite,
        },
      },
    };
  } else {
    return state;
  }
};

export const favoriteDeleteHelper = ({
  objectId,
  objectType,
  collection,
}: IFavoriteHelperDelete) => {
  const newCollection = JSON.parse(JSON.stringify(collection));
  newCollection[objectType].results = newCollection[objectType].results.filter(
    (item: any) => item.object_id !== objectId,
  );

  return newCollection;
};

export const favoriteChangeAllHelper = ({
  collection,
  isFavorite,
  bookmark,
  objectType,
  objectId,
}: IFavoriteChangeAllHelper) => {
  const newCollection = JSON.parse(JSON.stringify(collection));
  newCollection[objectType].results = newCollection[objectType].results.map((item: any) => {
    if (item.object_id === objectId) {
      return {
        ...item,
        bookmark: bookmark,
        object: {
          ...item.object,
          is_favorite: isFavorite,
        },
      };
    }
    return item;
  });
  return newCollection;
};

export const favoriteGetAllHelper = ({ results }: IFavoriteGetAllHelper) => {
  const collection: IFavoritesCollection = {
    article: defaultValues,
    brand: defaultValues,
    card: defaultValues,
    coupon: defaultValues,
    inspiration: defaultValues,
    leaflet: defaultValues,
  };
  const newCollection = JSON.parse(JSON.stringify(collection));

  results.forEach((item) => {
    newCollection[item.object_type].results.push(item);
  });

  return newCollection;
};
