import { createReducer } from '@reduxjs/toolkit';

import {
  onboardingFailure,
  onboardingInit,
  onboardingSuccess,
} from '~/redux/reducers/info/onboarding/actions';
import { IOnboardingReducer } from '~/redux/reducers/info/onboarding/types';

const initialState: IOnboardingReducer = {
  error: { message: '', status: false },
  loading: false,
  collection: null,
};

export const reducer = createReducer(initialState, (builder) => {
  builder.addCase(onboardingInit, (state) => {
    state.loading = true;
  });
  builder.addCase(onboardingSuccess, (state, action) => {
    state.loading = false;
    state.error = { message: '', status: false };
    state.collection = action.payload;
  });
  builder.addCase(onboardingFailure, (state, action) => {
    state.loading = false;
    state.error = action.payload;
  });
});
