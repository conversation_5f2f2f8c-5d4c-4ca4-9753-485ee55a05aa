import { getOnboardingApi } from '~/redux/api/onboarding';
import {
  onboardingFailure,
  onboardingInit,
  onboardingSuccess,
} from '~/redux/reducers/info/onboarding/actions';
import { AppThunk } from '~/redux/store';
import { IOnboarding } from '~/types/onboarding';

export const getOnboarding = () => (dispatch: AppThunk) => {
  dispatch(onboardingInit());
  return new Promise((resolve: (value: IOnboarding) => void, reject) => {
    getOnboardingApi()
      .then((response) => {
        dispatch(onboardingSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(onboardingFailure({ message: error, status: true }));
        reject(error);
      });
  });
};
