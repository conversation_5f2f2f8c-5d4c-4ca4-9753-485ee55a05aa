import { getPzInfoApi } from '~/redux/api/pzInfo';
import { pzInfoFailure, pzInfoInit, pzInfoSuccess } from '~/redux/reducers/info/pzInfo/actions';
import { AppThunk } from '~/redux/store';
import { IPzInfo } from '~/types/pzInfo';

export const getPzInfo = () => (dispatch: AppThunk) => {
  dispatch(pzInfoInit());
  return new Promise((resolve: (value: IPzInfo) => void, reject) => {
    getPzInfoApi()
      .then((response) => {
        dispatch(pzInfoSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(pzInfoFailure({ message: error, status: true }));
        reject(error);
      });
  });
};
