import { getPremiumPlanApi } from '~/redux/api/premiumPlan';
import {
  planInfoFailure,
  planInfoInit,
  planInfoSuccess,
} from '~/redux/reducers/info/premiumPlan/actions';
import { IPremiumPlan } from '~/redux/reducers/info/premiumPlan/types';
import { AppThunk } from '~/redux/store';

export const getPlanInfo = () => (dispatch: AppThunk) => {
  dispatch(planInfoInit());
  return new Promise((resolve: (value: IPremiumPlan) => void, reject) => {
    getPremiumPlanApi()
      .then((response) => {
        dispatch(planInfoSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(planInfoFailure({ message: error, status: true }));
        reject(error);
      });
  });
};
