import { createReducer } from '@reduxjs/toolkit';

import {
  planInfoFailure,
  planInfoInit,
  planInfoSuccess,
} from '~/redux/reducers/info/premiumPlan/actions';
import { IPremiumPlanReducer } from '~/redux/reducers/info/premiumPlan/types';

const initialState: IPremiumPlanReducer = {
  error: { message: '', status: false },
  loading: false,
  detail: {
    value: '',
  },
};

export const reducer = createReducer(initialState, (builder) => {
  builder.addCase(planInfoInit, (state) => {
    state.loading = true;
  });
  builder.addCase(planInfoSuccess, (state, action) => {
    state.loading = false;
    state.error = { message: '', status: false };
    state.detail = action.payload;
  });
  builder.addCase(planInfoFailure, (state, action) => {
    state.loading = false;
    state.error = action.payload;
  });
});
