import { createAction } from '@reduxjs/toolkit';

import { ITopicAction } from '~/redux/reducers/info/contact/types';
import { IError } from '~/redux/types/common.d';

export const contactInfoInit = createAction('contact/info/init');
export const contactInfoSuccess = createAction<any>('contact/info/success');
export const contactInfoFailure = createAction<IError>('contact/info/failure');

export const contactTopicsInit = createAction('contact/topics/init');
export const contactTopicsSuccess = createAction<ITopicAction>('contact/topics/success');
export const contactTopicsFailure = createAction<IError>('contact/topics/failure');
