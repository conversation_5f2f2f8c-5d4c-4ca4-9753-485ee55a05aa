import { createReducer } from '@reduxjs/toolkit';

import {
  contactInfoFailure,
  contactInfoInit,
  contactInfoSuccess,
  contactTopicsFailure,
  contactTopicsInit,
  contactTopicsSuccess,
} from '~/redux/reducers/info/contact/actions';
import { IContactReducer } from '~/redux/reducers/info/contact/types';

const initialState: IContactReducer = {
  error: { message: '', status: false },
  loading: false,
  info: null,
  topics: [],
};

export const reducer = createReducer(initialState, (builder) => {
  //INFO
  builder.addCase(contactInfoInit, (state) => {
    state.loading = true;
  });
  builder.addCase(contactInfoSuccess, (state, action) => {
    state.loading = false;
    state.error = { message: '', status: false };
    state.info = action.payload;
  });
  builder.addCase(contactInfoFailure, (state, action) => {
    state.loading = false;
    state.error = action.payload;
  });
  //TOPICS
  builder.addCase(contactTopicsInit, (state) => {
    state.loading = true;
  });
  builder.addCase(contactTopicsSuccess, (state, action) => {
    state.loading = false;
    state.error = { message: '', status: false };
    state.topics = action.payload.topics;
  });
  builder.addCase(contactTopicsFailure, (state, action) => {
    state.loading = false;
    state.error = action.payload;
  });
});
