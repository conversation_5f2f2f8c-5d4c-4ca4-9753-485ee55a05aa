import { getInfoApi, getTopicCollectionApi } from '~/redux/api/contact';
import {
  contactInfoFailure,
  contactInfoInit,
  contactInfoSuccess,
  contactTopicsFailure,
  contactTopicsInit,
  contactTopicsSuccess,
} from '~/redux/reducers/info/contact/actions';
import { ITopicAction } from '~/redux/reducers/info/contact/types';
import { AppThunk } from '~/redux/store';
import { IContactInfo } from '~/types/contact';

export const getInfo = () => (dispatch: AppThunk) => {
  dispatch(contactInfoInit());
  return new Promise((resolve: (value: IContactInfo) => void, reject) => {
    getInfoApi()
      .then((response) => {
        dispatch(contactInfoSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(contactInfoFailure({ message: error, status: true }));
        reject(error);
      });
  });
};

export const getTopics = () => (dispatch: AppThunk) => {
  dispatch(contactTopicsInit());
  return new Promise((resolve: (value: ITopicAction) => void, reject) => {
    getTopicCollectionApi()
      .then((response) => {
        dispatch(contactTopicsSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(contactTopicsFailure({ message: error, status: true }));
        reject(error);
      });
  });
};
