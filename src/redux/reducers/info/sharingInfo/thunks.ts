import { getSharingInfoApi } from '~/redux/api/sharingInfo';
import {
  sharingInfoFailure,
  sharingInfoInit,
  sharingInfoSuccess,
} from '~/redux/reducers/info/sharingInfo/actions';
import { AppThunk } from '~/redux/store';
import { SharingInfoResponse } from '~/types/sharingInfo';

export const getSharingInfo = () => (dispatch: AppThunk) => {
  dispatch(sharingInfoInit());
  return new Promise((resolve: (value: SharingInfoResponse) => void, reject) => {
    getSharingInfoApi()
      .then((response) => {
        dispatch(sharingInfoSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(sharingInfoFailure({ message: error, status: true }));
        reject(error);
      });
  });
};
