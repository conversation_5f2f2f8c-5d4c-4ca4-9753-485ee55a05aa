import { activeSectionsSuccess } from './actions';
import { IActiveSections } from './types';

import { getActiveSectionsApi } from '~/redux/api/activeSections';
import { AppThunk } from '~/redux/store';

export const getActiveSections = () => (dispatch: AppThunk) => {
  return new Promise((resolve: (value: IActiveSections) => void) => {
    getActiveSectionsApi().then((response) => {
      dispatch(activeSectionsSuccess(response.data));
      resolve(response.data);
    });
  });
};
