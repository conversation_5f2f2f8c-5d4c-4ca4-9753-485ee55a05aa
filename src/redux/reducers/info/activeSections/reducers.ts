import { createReducer } from '@reduxjs/toolkit';

import { activeSectionsSuccess } from './actions';
import { IActiveSections } from './types';

const initialState: IActiveSections = {
  card: true,
  coupon: true,
  leaflet: true,
  article: true,
  inspiration: true,
  brand: true,
};

export const reducer = createReducer(initialState, (builder) => {
  builder.addCase(activeSectionsSuccess, (state, action) => {
    Object.assign(state, action.payload);
  });
});
