import { getConsentsCollection<PERSON>pi, getLegalCollectionApi } from '~/redux/api/privacy';
import {
  privacyDetailFailure,
  privacyDetailInit,
  privacyDetailSuccess,
  privacyFailure,
  privacyInit,
  privacySuccess,
} from '~/redux/reducers/privacy/actions';
import { AppThunk } from '~/redux/store';
import { IConsentDetails, ILegal } from '~/types/privacy';

export const getConsents = () => (dispatch: AppThunk) => {
  dispatch(privacyInit());
  return new Promise((resolve: (value: Array<IConsentDetails>) => void, reject) => {
    getConsentsCollectionApi()
      .then((response) => {
        dispatch(privacySuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(privacyFailure({ message: error, status: true }));
        reject(error);
      });
  });
};

export const getLegal = (payload: { id: number }) => (dispatch: AppThunk) => {
  dispatch(privacyDetailInit());
  return new Promise((resolve: (value: ILegal) => void, reject) => {
    getLegalCollectionApi(payload)
      .then((response) => {
        dispatch(privacyDetailSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(privacyDetailFailure({ message: error, status: true }));
        reject(error);
      });
  });
};
