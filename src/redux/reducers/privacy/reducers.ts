import { createReducer } from '@reduxjs/toolkit';

import {
  privacyDetailClear,
  privacyDetailFailure,
  privacyDetailInit,
  privacyDetailSuccess,
  privacyFailure,
  privacyInit,
  privacySuccess,
} from '~/redux/reducers/privacy/actions';
import { IPrivacyReducer } from '~/redux/reducers/privacy/types';

const initialState: IPrivacyReducer = {
  error: { message: '', status: false },
  loading: false,
  consents: [],
  legal: null,
};

export const reducer = createReducer(initialState, (builder) => {
  //COLLECTION
  builder.addCase(privacyInit, (state) => {
    state.loading = true;
    state.error = { message: '', status: false };
  });
  builder.addCase(privacySuccess, (state, action) => {
    state.loading = false;
    state.error = { message: '', status: false };
    state.consents = action.payload;
  });
  builder.addCase(privacyFailure, (state, action) => {
    state.loading = false;
    state.error = action.payload;
  });
  //DETAIL
  builder.addCase(privacyDetailInit, (state) => {
    state.loading = true;
    state.error = { message: '', status: false };
  });
  builder.addCase(privacyDetailSuccess, (state, action) => {
    state.loading = false;
    state.error = { message: '', status: false };
    state.legal = action.payload;
  });
  builder.addCase(privacyDetailFailure, (state, action) => {
    state.loading = false;
    state.error = action.payload;
  });
  //CLEAR
  builder.addCase(privacyDetailClear, (state) => {
    state.legal = null;
  });
});
