import { createAction } from '@reduxjs/toolkit';

import { IError } from '~/redux/types/common.d';
import { IConsentDetails, ILegal } from '~/types/privacy.d';

export const privacyInit = createAction('privacy/init');
export const privacySuccess = createAction<Array<IConsentDetails>>('privacy/success');
export const privacyFailure = createAction<IError>('privacy/failure');

export const privacyDetailInit = createAction('privacy/detail/init');
export const privacyDetailSuccess = createAction<ILegal>('privacy/detail/success');
export const privacyDetailFailure = createAction<IError>('privacy/detail/failure');

export const privacyDetailClear = createAction('privacy/detail/clear');
