import { IReducer } from '~/redux/types/common';
import { IDetail, IMe, INotifySettings, IPremium, IToken } from '~/types/user.d';

export enum Session {
  AUTHORIZED = 'AUTHORIZED',
  UNAUTHORIZED = 'UNAUTHORIZED',
}
export interface IUserReducer extends IReducer {
  detail: IDetail;
  me: IMe | null;
  token: IToken | null;
  premium: IPremium | null;
  notification: string[];
  last_notify: string;
  last_refresh: string;
  notification_settings: Array<INotifySettings>;
  see_startup_info: boolean;
  onboarding: boolean;
}
