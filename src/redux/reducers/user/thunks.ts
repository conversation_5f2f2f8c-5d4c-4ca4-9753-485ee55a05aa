import { StorageEnum } from '~/enums/storage';
import { setItem } from '~/helpers/storage';
import {
  getMeA<PERSON>,
  getPremiumApi,
  getProfileApi,
  getRefreshTokenApi,
  getSocialAuth,
  getTokenApi,
} from '~/redux/api/user';
import {
  userDetailFailure,
  userDetailInit,
  userDetailSuccess,
  userMeFailure,
  userMeInit,
  userMeSuccess,
  userPremiumFailure,
  userPremiumInit,
  userPremiumSuccess,
  userSocialTokenFailure,
  userSocialTokenInit,
  userSocialTokenSuccess,
  userTokenFailure,
  userTokenInit,
  userTokenSuccess,
} from '~/redux/reducers/user/actions';
import { AppThunk } from '~/redux/store';
import { IDetail, IMe, IPremium, IToken, IUserSocialResponse } from '~/types/user.d';

export const getToken = (payload: { email: string; password: string }) => (dispatch: AppThunk) => {
  dispatch(userTokenInit());
  return new Promise((resolve: (value: IToken) => void, reject) => {
    getTokenApi(payload)
      .then((response) => {
        dispatch(userTokenSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(userTokenFailure({ message: error, status: true }));
        reject(error);
      });
  });
};

export const postRefreshToken = (payload: { refresh: string }) => (dispatch: AppThunk) => {
  dispatch(userTokenInit());
  return new Promise((resolve: (value: IToken) => void, reject) => {
    getRefreshTokenApi(payload)
      .then((response) => {
        dispatch(userTokenSuccess({ refresh: payload.refresh, access: response.data.access }));
        resolve({ refresh: payload.refresh, access: response.data.access });
      })
      .catch((error) => {
        dispatch(userTokenFailure({ message: error, status: true }));
        reject(error);
      });
  });
};

export const getProfile = (payload: { token: string }) => (dispatch: AppThunk) => {
  dispatch(userDetailInit());
  return new Promise((resolve: (value: IDetail) => void, reject) => {
    getProfileApi(payload)
      .then((response) => {
        dispatch(userDetailSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(userDetailFailure({ message: error, status: true }));
        reject(error);
      });
  });
};

export const getPremium = (payload: { token: string }) => (dispatch: AppThunk) => {
  dispatch(userPremiumInit());
  return new Promise((resolve: (value: IPremium) => void, reject) => {
    getPremiumApi(payload)
      .then((response) => {
        dispatch(userPremiumSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(userPremiumFailure({ message: error, status: true }));
        reject(error);
      });
  });
};

export const getMe = (payload: { token: string }) => (dispatch: AppThunk) => {
  dispatch(userMeInit());
  return new Promise((resolve: (value: IMe) => void, reject) => {
    getMeApi(payload)
      .then(async (response) => {
        await setItem(StorageEnum.LAST_LOGIN_EMAIL, response.data.email.toLowerCase());
        dispatch(userMeSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(userMeFailure({ message: error, status: true }));
        reject(error);
      });
  });
};

export const getSocialToken = (payload: { token: string }) => (dispatch: AppThunk) => {
  dispatch(userSocialTokenInit());
  return new Promise((resolve: (value: IUserSocialResponse) => void, reject) => {
    getSocialAuth(payload)
      .then((response) => {
        dispatch(userSocialTokenSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(userSocialTokenFailure({ message: error, status: true }));
        reject(error);
      });
  });
};
