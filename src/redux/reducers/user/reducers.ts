import { createReducer } from '@reduxjs/toolkit';

import { NotifyTypes } from '~/enums/notifyTypes';
import {
  userDetailFailure,
  userDetailInit,
  userDetailSuccess,
  userLastRefresh,
  userLogout,
  userMeFailure,
  userMeInit,
  userMeSuccess,
  userNotifySettingsChange,
  userOnboardingChange,
  userPremiumFailure,
  userPremiumInit,
  userPremiumSuccess,
  userSeeStartupInfo,
  userSetLastNotify,
  userShowNotification,
  userSocialTokenFailure,
  userSocialTokenInit,
  userSocialTokenSuccess,
  userTokenFailure,
  userTokenInit,
  userTokenSuccess,
} from '~/redux/reducers/user/actions';
import { IUserReducer } from '~/redux/reducers/user/types';

const initialState: IUserReducer = {
  error: { message: '', status: false },
  loading: false,
  detail: {
    first_name: '',
    last_name: '',
    phone_number: '',
    city: null,
    city_name: null,
  },
  me: null,
  token: null,
  premium: null,
  last_refresh: '',
  notification: [],
  last_notify: '',
  see_startup_info: false,
  notification_settings: [
    {
      type: NotifyTypes.push,
      status: true,
    },
    {
      type: NotifyTypes.email,
      status: true,
    },
    {
      type: NotifyTypes.sms,
      status: true,
    },
  ],
  onboarding: true,
};

export const reducer = createReducer(initialState, (builder) => {
  //LOGIN / TOKEN
  builder.addCase(userTokenInit, (state) => {
    state.loading = true;
    state.error = { message: '', status: false };
  });
  builder.addCase(userTokenSuccess, (state, action) => {
    state.loading = false;
    state.error = { message: '', status: false };
    state.token = action.payload;
  });
  builder.addCase(userTokenFailure, (state, action) => {
    state.loading = false;
    state.error = action.payload;
  });
  //DETAIL
  builder.addCase(userDetailInit, (state) => {
    state.loading = true;
    state.error = { message: '', status: false };
  });
  builder.addCase(userDetailSuccess, (state, action) => {
    state.loading = false;
    state.error = { message: '', status: false };
    state.detail = action.payload;
  });
  builder.addCase(userDetailFailure, (state, action) => {
    state.loading = false;
    state.error = action.payload;
  });
  //ME
  builder.addCase(userMeInit, (state) => {
    state.loading = true;
    state.error = { message: '', status: false };
  });
  builder.addCase(userMeSuccess, (state, action) => {
    state.loading = false;
    state.error = { message: '', status: false };
    state.me = action.payload;
  });
  builder.addCase(userMeFailure, (state, action) => {
    state.loading = false;
    state.error = action.payload;
  });
  //NOTIFICATION
  builder.addCase(userShowNotification, (state, action) => {
    if (!state.notification.includes(action.payload)) {
      state.notification.push(action.payload);
    }
  });
  builder.addCase(userSetLastNotify, (state, action) => {
    state.last_notify = action.payload;
  });
  builder.addCase(userNotifySettingsChange, (state, action) => {
    state.notification_settings = state.notification_settings.map((item) => {
      if (item.type === action.payload.type) {
        return { ...item, status: action.payload.status };
      }
      return item;
    });
  });
  //PREMIUM
  builder.addCase(userPremiumInit, (state) => {
    state.loading = true;
    state.error = { message: '', status: false };
  });
  builder.addCase(userPremiumSuccess, (state, action) => {
    state.loading = false;
    state.error = { message: '', status: false };
    state.premium = action.payload;
  });
  builder.addCase(userPremiumFailure, (state, action) => {
    state.loading = false;
    state.premium = null;
    state.error = action.payload;
  });
  //LOGOUT
  builder.addCase(userLogout, (state) => {
    state.token = null;
    state.detail = {
      first_name: '',
      last_name: '',
      phone_number: '',
      city: null,
      city_name: null,
    };
    state.me = null;
    state.premium = null;
  });
  //ONBOARDING
  builder.addCase(userOnboardingChange, (state, action) => {
    state.onboarding = action.payload;
  });
  builder.addCase(userLastRefresh, (state, action) => {
    state.last_refresh = action.payload;
  });
  //GOOGLE AUTH
  builder.addCase(userSocialTokenInit, (state) => {
    state.loading = true;
  });
  builder.addCase(userSocialTokenSuccess, (state, action) => {
    state.loading = false;
    state.token = {
      access: action.payload.tokens.access,
      refresh: action.payload.tokens.refresh,
    };
  });
  builder.addCase(userSocialTokenFailure, (state, action) => {
    state.loading = false;
    state.error = action.payload;
  });
  //SEE STARTUP INFO
  builder.addCase(userSeeStartupInfo, (state, action) => {
    state.see_startup_info = action.payload;
  });
});
