import { createAction } from '@reduxjs/toolkit';

import { IError } from '~/redux/types/common.d';
import {
  IDetail,
  IMe,
  INotifySettings,
  IPremium,
  IToken,
  IUserSocialResponse,
} from '~/types/user.d';

export const userTokenInit = createAction('user/token/init');
export const userTokenSuccess = createAction<IToken>('user/token/success');
export const userTokenFailure = createAction<IError>('user/token/failure');

export const userDetailInit = createAction('user/detail/init');
export const userDetailSuccess = createAction<IDetail>('user/detail/success');
export const userDetailFailure = createAction<IError>('user/detail/failure');

export const userMeInit = createAction('user/me/init');
export const userMeSuccess = createAction<IMe>('user/me/success');
export const userMeFailure = createAction<IError>('user/me/failure');

export const userPremiumInit = createAction('user/premium/init');
export const userPremiumSuccess = createAction<IPremium>('user/premium/success');
export const userPremiumFailure = createAction<IError>('user/premium/failure');

export const userShowNotification = createAction<string>('user/notification/show');
export const userSetLastNotify = createAction<string>('user/notification/last');

export const userNotifySettingsChange = createAction<INotifySettings>(
  'user/notification/settings/change',
);

export const userOnboardingChange = createAction<boolean>('user/onboarding/change');

export const userLogout = createAction('user/logout');
export const userLastRefresh = createAction<string>('user/lastRefresh');

export const userSocialTokenInit = createAction('user/social/init');
export const userSocialTokenSuccess = createAction<IUserSocialResponse>('user/social/success');
export const userSocialTokenFailure = createAction<IError>('user/social/failure');

export const userSeeStartupInfo = createAction<boolean>('user/startup/info');
