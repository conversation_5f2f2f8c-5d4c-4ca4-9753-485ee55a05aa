import { createReducer } from '@reduxjs/toolkit';

import { userSession } from '~/redux/reducers/states/actions';
import { SessionStates } from '~/redux/reducers/states/SessionStates';
import { ISessionReducer } from '~/redux/reducers/states/types';

const initialState: ISessionReducer = {
  session: SessionStates.UNAUTHENTICATED,
};

export const reducer = createReducer(initialState, (builder) => {
  builder.addCase(userSession, (state, action) => {
    state.session = action.payload;
  });
});
