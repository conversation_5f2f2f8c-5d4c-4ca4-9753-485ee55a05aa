import { createReducer } from '@reduxjs/toolkit';

import {
  localizationClear,
  localizationFailure,
  localizationInit,
  localizationPageFailure,
  localizationPageInit,
  localizationPageSuccess,
  localizationSuccess,
} from '~/redux/reducers/content/localization/actions';
import { ILocalizationReducer } from '~/redux/reducers/content/localization/types';

const initialState: ILocalizationReducer = {
  error: { message: '', status: false },
  loading: false,
  collection: {
    count: 0,
    next: null,
    previous: null,
    results: [],
  },
};

export const reducer = createReducer(initialState, (builder) => {
  //COLLECTION
  builder.addCase(localizationInit, (state) => {
    state.loading = true;
  });
  builder.addCase(localizationSuccess, (state, action) => {
    state.loading = false;
    state.error = { message: '', status: false };
    state.collection = action.payload;
  });
  builder.addCase(localizationFailure, (state, action) => {
    state.loading = false;
    state.error = action.payload;
  });
  //PAGE
  builder.addCase(localizationPageInit, (state) => {
    state.loading = true;
  });
  builder.addCase(localizationPageSuccess, (state, action) => {
    state.loading = false;
    state.error = { message: '', status: false };
    state.collection = {
      count: action.payload.count,
      next: action.payload.next,
      previous: action.payload.previous,
      results: [...state.collection.results, ...action.payload.results],
    };
  });
  builder.addCase(localizationPageFailure, (state, action) => {
    state.loading = false;
    state.error = action.payload;
  });
  //CLEAR
  builder.addCase(localizationClear, (state) => {
    state.collection = initialState.collection;
  });
});
