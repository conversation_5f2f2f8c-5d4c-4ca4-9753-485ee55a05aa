import { createAction } from '@reduxjs/toolkit';

import { IError } from '~/redux/types/common.d';
import { ILocalizationCollections } from '~/types/localization';

export const localizationInit = createAction('localization/init');
export const localizationSuccess = createAction<ILocalizationCollections>('localization/success');
export const localizationFailure = createAction<IError>('localization/failure');

export const localizationPageInit = createAction('localization/page/init');
export const localizationPageSuccess = createAction<ILocalizationCollections>(
  'localization/page/success',
);
export const localizationPageFailure = createAction<IError>('localization/page/failure');

export const localizationClear = createAction('localization/detail/clear');
