//import { getLocalizationCollectionApi } from '~/redux/api/localizations';
import {
  localizationFailure,
  localizationInit,
  localizationPageFailure,
  localizationPageInit,
  localizationPageSuccess,
  localizationSuccess,
} from '~/redux/reducers/content/localization/actions';
import { AppThunk } from '~/redux/store';
import { ILocalizationCollections } from '~/types/localization';
import { ParamsOptions } from '~/types/shared';

export const getLocalization = (payload?: ParamsOptions) => (dispatch: AppThunk) => {
  dispatch(localizationInit());
  return new Promise((resolve: (value: ILocalizationCollections) => void, reject) => {
    // getLocalizationCollectionApi(payload)
    //   .then((response) => {
    //     dispatch(localizationSuccess(response.data));
    //     resolve(response.data);
    //   })
    //   .catch((error) => {
    //     dispatch(localizationFailure({ message: error, status: true }));
    //     reject(error);
    //   });
  });
};

export const getLocalizationPage = (payload?: ParamsOptions) => (dispatch: AppThunk) => {
  dispatch(localizationPageInit());
  return new Promise((resolve: (value: ILocalizationCollections) => void, reject) => {
    // getLocalizationCollectionApi(payload)
    //   .then((response) => {
    //     dispatch(localizationPageSuccess(response.data));
    //     resolve(response.data);
    //   })
    //   .catch((error) => {
    //     dispatch(localizationPageFailure({ message: error, status: true }));
    //     reject(error);
    //   });
  });
};
