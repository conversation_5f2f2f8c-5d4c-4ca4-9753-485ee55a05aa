import { IArticle, IArticleDetails } from '~/types/article';
import { IBaseBrand, IBrandDetail } from '~/types/brands';
import { ICoupon, ICouponDetail } from '~/types/coupon';
import { IDiscount, IDiscountDetail } from '~/types/discount';
import { IInspiration, IInspirationDetails } from '~/types/inspirations';
import { INewspaper, INewspaperDetail } from '~/types/newspaper';

type Timestamp<T> = {
  timestamp: number;
} & T;
type InitialState = {
  discounts: {
    collection: IDiscount[];
    detail: Timestamp<IDiscountDetail>[];
  };
  coupons: {
    collection: Timestamp<ICoupon>[];
    detail: Timestamp<ICouponDetail>[];
  };
  articles: {
    collection: IArticle[];
    detail: Timestamp<IArticleDetails>[];
  };
  brands: {
    collection: IBaseBrand[];
    detail: Timestamp<IBrandDetail>[];
  };
  newspapers: {
    collection: INewspaper[];
    detail: Timestamp<INewspaperDetail>[];
  };
  inspirations: {
    collection: IInspiration[];
    detail: Timestamp<IInspirationDetails>[];
  };
};
export const initialState: InitialState = {
  discounts: {
    collection: [],
    detail: [],
  },
  coupons: {
    collection: [],
    detail: [],
  },
  articles: {
    collection: [],
    detail: [],
  },
  brands: {
    collection: [],
    detail: [],
  },
  newspapers: {
    collection: [],
    detail: [],
  },
  inspirations: {
    collection: [],
    detail: [],
  },
};
