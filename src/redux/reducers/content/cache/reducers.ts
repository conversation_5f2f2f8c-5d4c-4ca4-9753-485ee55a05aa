import { createReducer } from '@reduxjs/toolkit';

import { addDataToCache } from './actions';
import { initialState } from './initialState';

/**
 * TTL = Time To Live of cached item - currently set to 24 hours
 */
const TTL = 24 * 60 * 60 * 1000;

/**
 * @param state
 * @param payload
 * @description
 * Function for processing data and updating timestamp.
 * If data is already in cache it's updating timestamp.
 * If data is not in cache it's adding data to cache.
 * It's also removing old data from cache.
 * Old data is data that is older than TTL.
 */
const processDataAndUpdateTimestamp = (state: any, payload: any) => {
  const timestamp = Date.now();

  const existingIndex = state[payload.key][payload.type].findIndex((item: any) => {
    return item.id === payload.data.id;
  });

  if (existingIndex !== -1) {
    state[payload.key][payload.type][existingIndex].timestamp = timestamp;
  } else {
    state[payload.key][payload.type] = [
      ...state[payload.key][payload.type],
      { ...payload.data, timestamp },
    ];
  }

  state[payload.key][payload.type] = state[payload.key][payload.type].filter(
    (item: any) => timestamp - item.timestamp <= TTL,
  );
};

/**
 * @param actionType
 * @returns
 * @description
 * For action type CODE: it's adding code to discount (card that have field discounts).
 * Eg. { code: '1234' } will be appended to {id:1, description: 'test', discounts:[{..., code:'1234'}]}
 * For action type COLLECTION: it's replacing whole collection (max. 10 elements)
 * For action type DETAIL: it's adding element to cache (max. 24 hours, depends on TTL), if element is already in cache it's updating timestamp
 */
export const reducer = createReducer(initialState, (builder) => {
  builder.addCase(addDataToCache, (state, { payload }) => {
    switch (payload.type) {
      case 'code':
        if (payload.key === 'discounts' && payload.data.cardId) {
          return {
            ...state,
            [payload.key]: {
              ...state[payload.key],
              detail: state[payload.key].detail.map((item: any) => {
                if (item.id === payload.data.cardId) {
                  return {
                    ...item,
                    discounts: item.discounts.map((discount: any) => {
                      if (discount.id === payload.data.discountId) {
                        return {
                          ...discount,
                          code: payload.data.response.code,
                        };
                      }
                      return discount;
                    }),
                  };
                }
                return item;
              }),
            },
          };
        }
        processDataAndUpdateTimestamp(state, payload);
        return state;

      case 'detail':
        processDataAndUpdateTimestamp(state, payload);
        break;

      case 'collection':
        state[payload.key][payload.type] = payload.data;
        break;
      default:
        break;
    }
  });
});
