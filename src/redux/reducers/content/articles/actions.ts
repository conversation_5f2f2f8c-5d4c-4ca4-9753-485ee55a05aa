import { createAction } from '@reduxjs/toolkit';

import { IArticleDetailCache, IArticlePage } from './types';

import { IError } from '~/redux/types/common.d';
import { IArticle, IArticleCollections, IArticleDetails } from '~/types/article';
import { IFavoriteReducer } from '~/types/favorite';

export const articlesInit = createAction('articles/collection/init');
export const articlesSuccess = createAction<IArticleCollections>('articles/collection/success');
export const articlesFailure = createAction<IError>('articles/collection/failure');

export const articleDetailInit = createAction('articles/detail/init');
export const articleDetailSuccess = createAction<IArticleDetails>('articles/detail/success');
export const articleDetailFailure = createAction<IError>('articles/detail/failure');
export const articleDetailClear = createAction('articles/detail/clear');

/*
 * Similar
 */
export const articleSimilarInit = createAction('articles/similar/init');
export const articleSimilarSuccess = createAction<IArticleCollections>('articles/similar/success');
export const articleSimilarFailure = createAction<IError>('articles/similar/failure');
/*
 *Filtered
 */
export const articlesFilteredInit = createAction('articles/filtered/init');
export const articlesFilteredSuccess = createAction<IArticleCollections>(
  'articles/filtered/success',
);
export const articlesFilteredFailure = createAction<IError>('articles/filtered/failure');

export const articleChangeFavorite = createAction<IFavoriteReducer>('article/change/favorite');

/*
 * Search
 * may interest you content
 */
export const articlesMayInterestYou = createAction<IArticle[]>('articles/may/interest/you');
export const articlePage = createAction<IArticlePage>('article/page');
/*
 * Discount cache
 */
export const cacheArticleDetail = createAction<IArticleDetailCache>('articles/cache/detail');
