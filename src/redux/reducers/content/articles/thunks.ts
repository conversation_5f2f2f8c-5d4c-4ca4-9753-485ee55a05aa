import { getArticleCollection<PERSON>pi, getArticleDetailApi } from '~/redux/api/articles';
import {
  articleDetailFailure,
  articleDetailInit,
  articleDetailSuccess,
  articlesFailure,
  articlesFilteredFailure,
  articlesFilteredInit,
  articlesFilteredSuccess,
  articleSimilarFailure,
  articleSimilarInit,
  articleSimilarSuccess,
  articlesInit,
  articlesSuccess,
  cacheArticleDetail,
} from '~/redux/reducers/content/articles/actions';
import { AppThunk } from '~/redux/store';
import { IArticleCollections } from '~/types/article';
import { ParamsOptions } from '~/types/shared';
import { createFetchThunk } from '~/utils/thunk.config';

export const getArticles = (payload?: ParamsOptions) => (dispatch: AppThunk) => {
  dispatch(articlesInit());
  return new Promise((resolve: (value: IArticleCollections) => void, reject) => {
    getArticleCollectionApi(payload)
      .then((response) => {
        dispatch(articlesSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(articlesFailure({ message: error, status: true }));
        reject(error);
      });
  });
};
/*
 * Similar articles
 */
export const getSimilarArticles = (payload: ParamsOptions) => (dispatch: AppThunk) => {
  dispatch(articleSimilarInit());
  return new Promise((resolve: (value: IArticleCollections) => void, reject) => {
    getArticleCollectionApi(payload)
      .then((response) => {
        dispatch(articleSimilarSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(
          articleSimilarFailure({ message: 'Unable to get similar articles', status: true }),
        );
        reject(error);
      });
  });
};
/*
 * Filtered
 */
export const getFilteredArticles = (payload: ParamsOptions) => (dispatch: AppThunk) => {
  dispatch(articlesFilteredInit());
  return new Promise((resolve: (value: IArticleCollections) => void, reject) => {
    getArticleCollectionApi(payload)
      .then((response) => {
        dispatch(articlesFilteredSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(articlesFilteredFailure({ message: error, status: true }));
        reject(error);
      });
  });
};
export const getArticleDetail = (payload: { id: number }) =>
  createFetchThunk(
    articleDetailInit,
    articleDetailSuccess,
    articleDetailFailure,
    cacheArticleDetail,
    () => getArticleDetailApi(payload),
    payload,
    'articles',
  );
