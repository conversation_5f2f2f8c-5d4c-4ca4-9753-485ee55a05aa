import { createReducer } from '@reduxjs/toolkit';

import {
  favorite<PERSON><PERSON><PERSON><PERSON>per,
  favoriteHelper,
  favoriteSingleHelper,
} from '~/redux/helpers/favoriteHelper';
import {
  articleChangeFavorite,
  articleDetailFailure,
  articleDetailInit,
  articleDetailSuccess,
  articlePage,
  articlesFailure,
  articlesFilteredFailure,
  articlesFilteredInit,
  articlesFilteredSuccess,
  articleSimilarFailure,
  articleSimilarInit,
  articleSimilarSuccess,
  articlesInit,
  articlesMayInterestYou,
  articlesSuccess,
  cacheArticleDetail,
} from '~/redux/reducers/content/articles/actions';
import { initialState } from '~/redux/reducers/content/articles/initialState';

export const reducer = createReducer(initialState, (builder) => {
  //COLLECTION
  builder.addCase(articlesInit, (state) => {
    state.loading = true;
  });
  builder.addCase(articlesSuccess, (state, action) => {
    state.loading = false;
    state.error = { message: '', status: false };
    state.collection = action.payload;
  });
  builder.addCase(articlesFailure, (state, action) => {
    state.loading = false;
    state.error = action.payload;
  });
  //DETAIL
  builder.addCase(articleDetailInit, (state) => {
    state.detail.loading = true;
  });
  builder.addCase(articleDetailSuccess, (state, action) => {
    state.detail.loading = false;
    state.detail.error = { message: '', status: false };
    state.detail = {
      ...state.detail,
      ...action.payload,
    };
  });
  builder.addCase(articleDetailFailure, (state, action) => {
    state.detail.loading = false;
    state.detail.error = action.payload;
  });

  //SIMILAR
  builder.addCase(articleSimilarInit, (state) => {
    state.similar.articles.loading = true;
  });
  builder.addCase(articleSimilarSuccess, (state, action) => {
    state.similar.articles.loading = false;
    state.similar.articles.error = { message: '', status: false };
    state.similar.articles = {
      ...state.similar.articles,
      ...action.payload,
    };
  });
  builder.addCase(articleSimilarFailure, (state, action) => {
    state.similar.articles.loading = false;
    state.similar.articles.error = action.payload;
  });
  /*
   * Filtered
   */
  builder.addCase(articlesFilteredInit, (state) => {
    state.filteredArticles.loading = true;
  });
  builder.addCase(articlesFilteredSuccess, (state, action) => {
    state.filteredArticles.loading = false;
    state.filteredArticles.error = { message: '', status: false };
    state.filteredArticles.results = action.payload.results;
  });
  builder.addCase(articlesFilteredFailure, (state, action) => {
    state.filteredArticles.loading = false;
    state.filteredArticles.error = action.payload;
  });
  //FAVORITE
  builder.addCase(articleChangeFavorite, (state, action) => {
    state.collection = {
      ...state.collection,
      results: favoriteHelper({
        state: state.collection.results,
        objectId: action.payload.objectId,
        isFavorite: action.payload.isFavorite,
      }),
    };
    state.filteredArticles = {
      ...state.filteredArticles,
      results: favoriteHelper({
        state: state.filteredArticles.results,
        objectId: action.payload.objectId,
        isFavorite: action.payload.isFavorite,
      }),
    };
    state.similar.articles = {
      ...state.similar.articles,
      results: favoriteHelper({
        state: state.similar.articles.results,
        objectId: action.payload.objectId,
        isFavorite: action.payload.isFavorite,
      }),
    };
    state.mayInterestYou = favoriteHelper({
      state: state.mayInterestYou,
      objectId: action.payload.objectId,
      isFavorite: action.payload.isFavorite,
    });
    state.detail = favoriteSingleHelper({
      state: state.detail,
      objectId: action.payload.objectId,
      isFavorite: action.payload.isFavorite,
    });
    state.cache.detail = favoriteCacheHelper({
      state: state.cache.detail,
      objectId: action.payload.objectId,
      isFavorite: action.payload.isFavorite,
    });
  });
  /*
   * Search
   * may interest you content
   */
  builder.addCase(articlesMayInterestYou, (state, action) => {
    state.mayInterestYou = action.payload;
  });
  // PAGINATION
  builder.addCase(articlePage, (state, action) => {
    switch (action.payload.listType) {
      case 'collection':
        state.collection = {
          count: action.payload.collection.count,
          next: action.payload.collection.next,
          previous: action.payload.collection.previous,
          results: [...state.collection.results, ...action.payload.collection.results],
        };
        break;
      case 'filtered':
        state.filteredArticles = {
          loading: false,
          error: { message: '', status: false },
          count: action.payload.collection.count,
          next: action.payload.collection.next,
          previous: action.payload.collection.previous,
          results: [...state.filteredArticles.results, ...action.payload.collection.results],
        };
        break;
      case 'similar':
        state.similar.articles = {
          loading: false,
          error: { message: '', status: false },
          count: action.payload.collection.count,
          next: action.payload.collection.next,
          previous: action.payload.collection.previous,
          results: [...state.similar.articles.results, ...action.payload.collection.results],
        };
        break;
    }
  });
  /*
   * Discount cache
   */
  builder.addCase(cacheArticleDetail, (state, action) => {
    return {
      ...state,
      cache: {
        ...state.cache,
        detail: {
          ...state.cache.detail,
          [action.payload.id]: {
            data: action.payload.data,
            timestamp: action.payload.timestamp,
          },
        },
      },
    };
  });
});
