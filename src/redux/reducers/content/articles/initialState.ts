import { IArticlesReducer } from '~/redux/reducers/content/articles/types';

export const initialState: IArticlesReducer = {
  error: { message: '', status: false },
  loading: false,
  cache: {
    detail: {},
  },
  collection: {
    count: 0,
    next: null,
    previous: null,
    results: [],
  },
  detail: {
    error: { message: '', status: false },
    loading: false,
    id: 0,
    title: '',
    brand: {
      id: 0,
      name: '',
      logo: '',
      is_favorite: false,
    },
    first_image: '',
    publish_date: '',
    categories: [],
    tags: [],
    is_favorite: null,
    images: [],
    subtitle: '',
    text: '',
    products: [],
  },
  similar: {
    articles: {
      error: { message: '', status: false },
      loading: false,
      count: 0,
      next: null,
      previous: null,
      results: [],
    },
  },
  filteredArticles: {
    error: { message: '', status: false },
    loading: false,
    count: 0,
    next: null,
    previous: null,
    results: [],
  },
  mayInterestYou: [],
};
