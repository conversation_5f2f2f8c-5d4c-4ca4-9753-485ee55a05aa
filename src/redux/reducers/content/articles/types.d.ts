import { IReducer } from '~/redux/types/common';
import { IArticle, IArticleCollections, IArticleDetails } from '~/types/article';

export type TArticleCollection = IArticleCollections & IReducer;
export type TArticleDetail = IArticleDetails & IReducer;

export interface IArticleDetailCache {
  id: number;
  data: IArticleDetails;
  timestamp: number;
}

export interface IArticlesReducer extends IReducer {
  collection: IArticleCollections;
  detail: TArticleDetail;
  cache: {
    detail: {
      [key: string]: Omit<IArticleDetailCache, 'id'>;
    };
  };
  similar: {
    articles: TArticleCollection;
  };
  filteredArticles: TArticleCollection;
  mayInterestYou: IArticle[];
}

export interface IArticlePage {
  listType: 'similar' | 'filtered' | 'collection';
  collection: IArticleCollections;
}
