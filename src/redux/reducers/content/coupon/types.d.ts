import { IReducer } from '~/redux/types/common';
import { ICoupon, ICouponCollections, ICouponDetail } from '~/types/coupon';

export type TCouponCollection = ICouponCollections & IReducer;
export type TCouponDetail = ICouponDetail & IReducer;

export interface ICouponDetailCache {
  id: number;
  data: ICouponDetail;
  timestamp: number;
}

export interface ICouponReducer extends IReducer {
  collection: ICouponCollections;
  detail: TCouponDetail;
  cache: {
    detail: {
      [key: string]: Omit<ICouponDetailCache, 'id'>;
    };
  };
  similar: {
    coupons: TCouponCollection;
  };
  filteredCoupons: TCouponCollection;
  mayInterestYou: ICoupon[];
}

export interface ICouponPage {
  listType: 'similar' | 'filtered' | 'collection';
  collection: ICouponCollections;
}
