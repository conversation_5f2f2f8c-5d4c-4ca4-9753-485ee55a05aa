import { createReducer } from '@reduxjs/toolkit';

import {
  favorite<PERSON><PERSON><PERSON>elper,
  favoriteHelper,
  favoriteSingleHelper,
} from '~/redux/helpers/favoriteHelper';
import {
  cacheCouponDetail,
  couponChangeFavorite,
  couponDetailFailure,
  couponDetailInit,
  couponDetailSuccess,
  couponsFailure,
  couponsFilteredFailure,
  couponsFilteredInit,
  couponsFilteredSuccess,
  couponsInit,
  couponsMayInterestYou,
  couponsPage,
  couponsSuccess,
  similarCouponsFailure,
  similarCouponsInit,
  similarCouponsSuccess,
} from '~/redux/reducers/content/coupon/actions';
import { initialState } from '~/redux/reducers/content/coupon/initialState';

export const reducer = createReducer(initialState, (builder) => {
  //COLLECTION
  builder.addCase(couponsInit, (state) => {
    state.loading = true;
  });
  builder.addCase(couponsSuccess, (state, action) => {
    state.loading = false;
    state.error = { message: '', status: false };
    state.collection = action.payload;
  });
  builder.addCase(couponsFailure, (state, action) => {
    state.loading = false;
    state.error = action.payload;
  });
  //DETAIL
  builder.addCase(couponDetailInit, (state) => {
    state.detail.loading = true;
  });
  builder.addCase(couponDetailSuccess, (state, action) => {
    state.detail.loading = false;
    state.detail.error = { message: '', status: false };
    state.detail = {
      ...state.detail,
      ...action.payload,
    };
  });
  builder.addCase(couponDetailFailure, (state, action) => {
    state.detail.loading = false;
    state.detail.error = action.payload;
  });
  /*
   * Similar
   */
  builder.addCase(similarCouponsInit, (state) => {
    state.similar.coupons.loading = true;
  });
  builder.addCase(similarCouponsSuccess, (state, action) => {
    state.similar.coupons.loading = false;
    state.similar.coupons.error = { message: '', status: false };
    state.similar.coupons = {
      ...state.similar.coupons,
      ...action.payload,
    };
  });
  builder.addCase(similarCouponsFailure, (state, action) => {
    state.similar.coupons.loading = false;
    state.similar.coupons.error = action.payload;
  });
  /*
   * Filtered
   */
  builder.addCase(couponsFilteredInit, (state) => {
    state.filteredCoupons.loading = true;
  });
  builder.addCase(couponsFilteredSuccess, (state, action) => {
    state.filteredCoupons.loading = false;
    state.filteredCoupons.error = { message: '', status: false };

    state.filteredCoupons.count = action.payload.count;
    state.filteredCoupons.next = action.payload.next;
    state.filteredCoupons.previous = action.payload.previous;
    state.filteredCoupons.results = action.payload.results;
  });
  builder.addCase(couponsFilteredFailure, (state, action) => {
    state.filteredCoupons.loading = false;
    state.filteredCoupons.error = action.payload;
  });

  //FAVORITE
  builder.addCase(couponChangeFavorite, (state, action) => {
    state.collection = {
      ...state.collection,
      results: favoriteHelper({
        state: state.collection.results,
        objectId: action.payload.objectId,
        isFavorite: action.payload.isFavorite,
      }),
    };
    state.filteredCoupons = {
      ...state.filteredCoupons,
      results: favoriteHelper({
        state: state.filteredCoupons.results,
        objectId: action.payload.objectId,
        isFavorite: action.payload.isFavorite,
      }),
    };
    state.similar.coupons = {
      ...state.similar.coupons,
      results: favoriteHelper({
        state: state.similar.coupons.results,
        objectId: action.payload.objectId,
        isFavorite: action.payload.isFavorite,
      }),
    };
    state.mayInterestYou = favoriteHelper({
      state: state.mayInterestYou,
      objectId: action.payload.objectId,
      isFavorite: action.payload.isFavorite,
    });
    state.detail = favoriteSingleHelper({
      state: state.detail,
      objectId: action.payload.objectId,
      isFavorite: action.payload.isFavorite,
    });
    state.cache.detail = favoriteCacheHelper({
      state: state.cache.detail,
      objectId: action.payload.objectId,
      isFavorite: action.payload.isFavorite,
    });
  });
  /*
   * Search
   * may interest you content
   */
  builder.addCase(couponsMayInterestYou, (state, action) => {
    state.mayInterestYou = action.payload;
  });
  // PAGINATION
  builder.addCase(couponsPage, (state, action) => {
    switch (action.payload.listType) {
      case 'collection':
        state.collection = {
          count: action.payload.collection.count,
          next: action.payload.collection.next,
          previous: action.payload.collection.previous,
          results: [...state.collection.results, ...action.payload.collection.results],
        };
        break;
      case 'filtered':
        state.filteredCoupons = {
          loading: false,
          error: { message: '', status: false },
          count: action.payload.collection.count,
          next: action.payload.collection.next,
          previous: action.payload.collection.previous,
          results: [...state.filteredCoupons.results, ...action.payload.collection.results],
        };
        break;
      case 'similar':
        state.similar.coupons = {
          loading: false,
          error: { message: '', status: false },
          count: action.payload.collection.count,
          next: action.payload.collection.next,
          previous: action.payload.collection.previous,
          results: [...state.similar.coupons.results, ...action.payload.collection.results],
        };
        break;
    }
  });
  /*
   * Discount cache
   */
  builder.addCase(cacheCouponDetail, (state, action) => {
    return {
      ...state,
      cache: {
        ...state.cache,
        detail: {
          ...state.cache.detail,
          [action.payload.id]: {
            data: action.payload.data,
            timestamp: action.payload.timestamp,
          },
        },
      },
    };
  });
});
