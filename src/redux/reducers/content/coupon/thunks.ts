import { getCouponCollection<PERSON>pi, getCouponDetailApi } from '~/redux/api/coupons';
import {
  cacheCouponDetail,
  couponDetailFailure,
  couponDetailInit,
  couponDetailSuccess,
  couponsFailure,
  couponsFilteredFailure,
  couponsFilteredInit,
  couponsFilteredSuccess,
  couponsInit,
  couponsSuccess,
  similarCouponsFailure,
  similarCouponsInit,
  similarCouponsSuccess,
} from '~/redux/reducers/content/coupon/actions';
import { AppThunk } from '~/redux/store';
import { ICouponCollections } from '~/types/coupon';
import { ParamsOptions } from '~/types/shared';
import { createFetchThunk } from '~/utils/thunk.config';

export const getCoupons = (payload?: ParamsOptions) => (dispatch: AppThunk) => {
  dispatch(couponsInit());
  return new Promise((resolve: (value: ICouponCollections) => void, reject) => {
    getCouponCollectionApi(payload)
      .then((response) => {
        dispatch(couponsSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(couponsFailure({ message: error, status: true }));
        reject(error);
      });
  });
};

export const getSimilarCoupons = (payload: ParamsOptions) => (dispatch: AppThunk) => {
  dispatch(similarCouponsInit());
  return new Promise((resolve: (value: ICouponCollections) => void, reject) => {
    getCouponCollectionApi(payload)
      .then((response) => {
        dispatch(similarCouponsSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(similarCouponsFailure({ message: error, status: true }));
        reject(error);
      });
  });
};
/*
 * Filtered coupons
 */
export const getFilteredCoupons = (payload: ParamsOptions) => (dispatch: AppThunk) => {
  dispatch(couponsFilteredInit());
  return new Promise((resolve: (value: ICouponCollections) => void, reject) => {
    getCouponCollectionApi(payload)
      .then((response) => {
        dispatch(couponsFilteredSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(couponsFilteredFailure({ message: error, status: true }));
        reject(error);
      });
  });
};
export const getCouponDetail = (payload: { id: number }) =>
  createFetchThunk(
    couponDetailInit,
    couponDetailSuccess,
    couponDetailFailure,
    cacheCouponDetail,
    () => getCouponDetailApi(payload),
    payload,
    'coupons',
  );

// export const getCouponDetail = (payload: { id: number }) => (dispatch: AppThunk) => {
//   dispatch(couponDetailInit());
//   return new Promise((resolve: (value: ICouponDetail) => void, reject) => {
//     getCouponDetailApi(payload)
//       .then((response) => {
//         dispatch(couponDetailSuccess(response.data));
//         resolve(response.data);
//       })
//       .catch((error) => {
//         dispatch(couponDetailFailure({ message: error, status: true }));
//         reject(error);
//       });
//   });
// };
