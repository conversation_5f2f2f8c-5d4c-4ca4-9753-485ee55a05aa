import { ValueTypeEnum } from '~/enums/shared';
import { ICouponReducer } from '~/redux/reducers/content/coupon/types';

export const initialState: ICouponReducer = {
  error: { message: '', status: false },
  loading: false,
  cache: {
    detail: {},
  },
  collection: {
    count: 0,
    next: null,
    previous: null,
    results: [],
  },
  similar: {
    coupons: {
      error: { message: '', status: false },
      loading: false,
      count: 0,
      next: null,
      previous: null,
      results: [],
    },
  },
  filteredCoupons: {
    error: { message: '', status: false },
    loading: false,
    count: 0,
    next: null,
    previous: null,
    results: [],
  },
  detail: {
    error: { message: '', status: false },
    loading: false,
    id: 0,
    title: '',
    start_date: '',
    end_date: '',
    bg_color: '',
    logo: '',
    bg_image: '',
    brand: {
      is_favorite: false,
      id: 0,
      name: '',
      logo: '',
    },
    custom_brand_name: '',
    categories: [],
    value_type: ValueTypeEnum.OTHER,
    discount_value: '',
    is_favorite: false,
    tags: [],
    affiliate_link: '',
    coupon_string: '',
    shop_type: 0,
  },
  mayInterestYou: [],
};
