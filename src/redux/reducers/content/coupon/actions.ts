import { createAction } from '@reduxjs/toolkit';

import { ICouponDetailCache, ICouponPage } from './types';

import { IError } from '~/redux/types/common.d';
import { ICoupon, ICouponCollections, ICouponDetail } from '~/types/coupon';
import { IFavoriteReducer } from '~/types/favorite';

export const couponsInit = createAction('coupons/collection/init');
export const couponsSuccess = createAction<ICouponCollections>('coupons/collection/success');
export const couponsFailure = createAction<IError>('coupons/collection/failure');

export const couponDetailInit = createAction('coupons/detail/init');
export const couponDetailSuccess = createAction<ICouponDetail>('coupons/detail/success');
export const couponDetailFailure = createAction<IError>('coupons/detail/failure');

export const similarCouponsInit = createAction('coupons/similar/init');
export const similarCouponsSuccess = createAction<ICouponCollections>('coupons/similar/success');
export const similarCouponsFailure = createAction<IError>('coupons/similar/failure');

export const clearSimilarCoupons = createAction('coupons/similar/clear');
export const clearCouponDetail = createAction('coupons/detail/clear');
/*
 * Filtered coupons
 */
export const couponsFilteredInit = createAction('coupons/filter/init');
export const couponsFilteredSuccess = createAction<ICouponCollections>('coupons/filter/success');
export const couponsFilteredFailure = createAction<IError>('coupons/filter/failure');

export const couponChangeFavorite = createAction<IFavoriteReducer>('coupons/change/favorite');

/*
 * Search
 * may interest you content
 */
export const couponsMayInterestYou = createAction<ICoupon[]>('coupons/may/interest/you');

export const couponsPage = createAction<ICouponPage>('coupons/page');
/*
 * Discount cache
 */
export const cacheCouponDetail = createAction<ICouponDetailCache>('coupons/cache/detail');
