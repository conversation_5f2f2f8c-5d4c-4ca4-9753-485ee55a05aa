import { getNotificationCollection<PERSON><PERSON> } from '~/redux/api/notifications';
import {
  notificationsFailure,
  notificationsInit,
  notificationsSuccess,
} from '~/redux/reducers/content/notifications/actions';
import { AppThunk } from '~/redux/store';
import { INotificationCollections } from '~/types/notification';

export const getNotifications = () => (dispatch: AppThunk) => {
  dispatch(notificationsInit());
  return new Promise((resolve: (value: INotificationCollections) => void, reject) => {
    getNotificationCollectionApi()
      .then((response) => {
        dispatch(notificationsSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(notificationsFailure({ message: error, status: true }));
        reject(error);
      });
  });
};
