import { createReducer } from '@reduxjs/toolkit';

import {
  notificationsClear,
  notificationsFailure,
  notificationsInit,
  notificationsSuccess,
} from '~/redux/reducers/content/notifications/actions';
import { INotificationsReducer } from '~/redux/reducers/content/notifications/types';

const initialState: INotificationsReducer = {
  error: { message: '', status: false },
  loading: false,
  collection: {
    count: 0,
    next: null,
    previous: null,
    results: [],
  },
};

export const reducer = createReducer(initialState, (builder) => {
  //COLLECTION
  builder.addCase(notificationsInit, (state) => {
    state.loading = true;
  });
  builder.addCase(notificationsSuccess, (state, action) => {
    state.loading = false;
    state.error = { message: '', status: false };
    state.collection = action.payload;
  });
  builder.addCase(notificationsFailure, (state, action) => {
    state.loading = false;
    state.error = action.payload;
  });
  //CLEAR
  builder.addCase(notificationsClear, (state) => {
    state.collection = initialState.collection;
  });
});
