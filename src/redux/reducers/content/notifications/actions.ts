import { createAction } from '@reduxjs/toolkit';

import { IError } from '~/redux/types/common.d';
import { INotificationCollections } from '~/types/notification';

export const notificationsInit = createAction('notifications/init');
export const notificationsSuccess = createAction<INotificationCollections>('notifications/success');
export const notificationsFailure = createAction<IError>('notifications/failure');

export const notificationsClear = createAction('notifications/detail/clear');
