import { IReducer } from '~/redux/types/common';
import { INewspaper, INewspaperCollections, INewspaperDetail } from '~/types/newspaper';

export type TNewspaperCollection = INewspaperCollections & IReducer;
export type TNewspaperDetail = INewspaperDetail & IReducer;

export interface INewspapersDetailCache {
  id: number;
  data: INewspaperDetail;
  timestamp: number;
}

export interface INewspapersReducer extends IReducer {
  collection: INewspaperCollections;
  detail: TNewspaperDetail;
  cache: {
    detail: {
      [key: string]: Omit<INewspapersDetailCache, 'id'>;
    };
  };
  similar: {
    newspapers: TNewspaperCollection;
  };
  filteredNewspapers: TNewspaperCollection;
  mayInterestYou: INewspaper[];
}

export interface INewspaperPage {
  listType: 'similar' | 'filtered' | 'collection';
  collection: INewspaperCollections;
}
