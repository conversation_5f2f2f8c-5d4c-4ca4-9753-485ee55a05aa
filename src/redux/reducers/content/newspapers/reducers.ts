import { createReducer } from '@reduxjs/toolkit';

import {
  favorite<PERSON>ache<PERSON>elper,
  favoriteHelper,
  favoriteSingleHelper,
} from '~/redux/helpers/favoriteHelper';
import {
  cacheNewspapersDetail,
  newspaperChangeFavorite,
  newspaperDetailFailure,
  newspaperDetailInit,
  newspaperDetailSuccess,
  newspapersFailure,
  newspapersFilteredFailure,
  newspapersFilteredInit,
  newspapersFilteredSuccess,
  newspapersInit,
  newspapersMayInterestYou,
  newspapersPage,
  newspapersSuccess,
  similarNewspapersFailure,
  similarNewspapersInit,
  similarNewspapersSuccess,
} from '~/redux/reducers/content/newspapers/actions';
import { initialState } from '~/redux/reducers/content/newspapers/initialState';

export const reducer = createReducer(initialState, (builder) => {
  //COLLECTION
  builder.addCase(newspapersInit, (state) => {
    state.loading = true;
  });
  builder.addCase(newspapersSuccess, (state, action) => {
    state.loading = false;
    state.collection = action.payload;
  });
  builder.addCase(newspapersFailure, (state, action) => {
    state.loading = false;
    state.error = action.payload;
  });
  //DETAIL
  builder.addCase(newspaperDetailInit, (state) => {
    state.detail.loading = true;
  });
  builder.addCase(newspaperDetailSuccess, (state, action) => {
    state.detail.loading = false;
    state.detail.error = { message: '', status: false };
    state.detail = {
      ...state.detail,
      ...action.payload,
    };
  });
  builder.addCase(newspaperDetailFailure, (state, action) => {
    state.detail.loading = false;
    state.detail.error = action.payload;
  });

  /*
   * Similar
   */
  builder.addCase(similarNewspapersInit, (state) => {
    state.similar.newspapers.loading = true;
  });
  builder.addCase(similarNewspapersSuccess, (state, action) => {
    state.similar.newspapers.loading = false;
    state.similar.newspapers.error = { message: '', status: false };
    state.similar.newspapers = {
      ...state.similar.newspapers,
      ...action.payload,
    };
  });
  builder.addCase(similarNewspapersFailure, (state, action) => {
    state.similar.newspapers.loading = false;
    state.similar.newspapers.error = action.payload;
  });
  /*
   * Filtered
   */
  builder.addCase(newspapersFilteredInit, (state) => {
    state.filteredNewspapers.loading = true;
  });
  builder.addCase(newspapersFilteredSuccess, (state, action) => {
    state.filteredNewspapers.loading = false;
    state.filteredNewspapers.error = { message: '', status: false };
    state.filteredNewspapers.count = action.payload.count;
    state.filteredNewspapers.next = action.payload.next;
    state.filteredNewspapers.previous = action.payload.previous;
    state.filteredNewspapers.results = action.payload.results;
  });
  builder.addCase(newspapersFilteredFailure, (state, action) => {
    state.filteredNewspapers.loading = false;
    state.filteredNewspapers.error = action.payload;
  });
  //FAVORITE
  builder.addCase(newspaperChangeFavorite, (state, action) => {
    state.collection = {
      ...state.collection,
      results: favoriteHelper({
        state: state.collection.results,
        objectId: action.payload.objectId,
        isFavorite: action.payload.isFavorite,
      }),
    };
    state.filteredNewspapers = {
      ...state.filteredNewspapers,
      results: favoriteHelper({
        state: state.filteredNewspapers.results,
        objectId: action.payload.objectId,
        isFavorite: action.payload.isFavorite,
      }),
    };
    state.similar.newspapers = {
      ...state.similar.newspapers,
      results: favoriteHelper({
        state: state.similar.newspapers.results,
        objectId: action.payload.objectId,
        isFavorite: action.payload.isFavorite,
      }),
    };
    state.mayInterestYou = favoriteHelper({
      state: state.mayInterestYou,
      objectId: action.payload.objectId,
      isFavorite: action.payload.isFavorite,
    });
    state.detail = favoriteSingleHelper({
      state: state.detail,
      objectId: action.payload.objectId,
      isFavorite: action.payload.isFavorite,
    });
    state.cache.detail = favoriteCacheHelper({
      state: state.cache.detail,
      objectId: action.payload.objectId,
      isFavorite: action.payload.isFavorite,
    });
  });
  /*
   * Search
   * may interest you content
   */
  builder.addCase(newspapersMayInterestYou, (state, action) => {
    state.mayInterestYou = action.payload;
  });
  // PAGINATION
  builder.addCase(newspapersPage, (state, action) => {
    switch (action.payload.listType) {
      case 'collection':
        state.collection = {
          count: action.payload.collection.count,
          next: action.payload.collection.next,
          previous: action.payload.collection.previous,
          results: [...state.collection.results, ...action.payload.collection.results],
        };
        break;
      case 'filtered':
        state.filteredNewspapers = {
          loading: false,
          error: { message: '', status: false },
          count: action.payload.collection.count,
          next: action.payload.collection.next,
          previous: action.payload.collection.previous,
          results: [...state.filteredNewspapers.results, ...action.payload.collection.results],
        };
        break;
      case 'similar':
        state.similar.newspapers = {
          loading: false,
          error: { message: '', status: false },
          count: action.payload.collection.count,
          next: action.payload.collection.next,
          previous: action.payload.collection.previous,
          results: [...state.similar.newspapers.results, ...action.payload.collection.results],
        };
        break;
    }
  });
  /*
   * Discount cache
   */
  builder.addCase(cacheNewspapersDetail, (state, action) => {
    return {
      ...state,
      cache: {
        ...state.cache,
        detail: {
          ...state.cache.detail,
          [action.payload.id]: {
            data: action.payload.data,
            timestamp: action.payload.timestamp,
          },
        },
      },
    };
  });
});
