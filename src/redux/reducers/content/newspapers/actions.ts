import { createAction } from '@reduxjs/toolkit';

import { INewspaperPage, INewspapersDetailCache } from './types';

import { IError } from '~/redux/types/common.d';
import { IFavoriteReducer } from '~/types/favorite';
import { INewspaper, INewspaperCollections, INewspaperDetail } from '~/types/newspaper';

export const newspapersInit = createAction('newspapers/collection/init');
export const newspapersSuccess = createAction<INewspaperCollections>(
  'newspapers/collection/success',
);
export const newspapersFailure = createAction<IError>('newspapers/collection/failure');

export const newspaperDetailInit = createAction('newspapers/detail/init');
export const newspaperDetailSuccess = createAction<INewspaperDetail>('newspapers/detail/success');
export const newspaperDetailFailure = createAction<IError>('newspapers/detail/failure');
/*
 * Similar
 */
export const similarNewspapersInit = createAction('newspapers/similar/init');
export const similarNewspapersSuccess = createAction<INewspaperCollections>(
  'newspapers/similar/success',
);
export const similarNewspapersFailure = createAction<IError>('newspapers/similar/failure');

/*
 *Filtered
 */
export const newspapersFilteredInit = createAction('newspapers/filtered/init');
export const newspapersFilteredSuccess = createAction<INewspaperCollections>(
  'newspapers/filtered/success',
);
export const newspapersFilteredFailure = createAction<IError>('newspapers/filtered/failure');
// export const

export const newspaperChangeFavorite = createAction<IFavoriteReducer>('newspapers/change/favorite');
/*
 * Search
 * may interest you content
 */
export const newspapersMayInterestYou = createAction<INewspaper[]>('newspapers/may/interest/you');

export const newspapersPage = createAction<INewspaperPage>('newspapers/page');
/*
 * Discount cache
 */
export const cacheNewspapersDetail =
  createAction<INewspapersDetailCache>('newspapers/cache/detail');
