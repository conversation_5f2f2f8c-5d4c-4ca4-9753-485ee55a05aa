import { getNewspaperCollectionApi, getNewspaperDetailApi } from '~/redux/api/newspapers';
import {
  cacheNewspapersDetail,
  newspaperDetailFailure,
  newspaperDetailInit,
  newspaperDetailSuccess,
  newspapersFailure,
  newspapersFilteredFailure,
  newspapersFilteredInit,
  newspapersFilteredSuccess,
  newspapersInit,
  newspapersSuccess,
  similarNewspapersFailure,
  similarNewspapersInit,
  similarNewspapersSuccess,
} from '~/redux/reducers/content/newspapers/actions';
import { AppThunk } from '~/redux/store';
import { INewspaperCollections } from '~/types/newspaper';
import { ParamsOptions } from '~/types/shared';
import { createFetchThunk } from '~/utils/thunk.config';

export const getNewspapers = (payload?: ParamsOptions) => (dispatch: AppThunk) => {
  dispatch(newspapersInit());
  return new Promise((resolve: (value: INewspaperCollections) => void, reject) => {
    getNewspaperCollectionApi(payload)
      .then((response) => {
        dispatch(newspapersSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(newspapersFailure({ message: error, status: true }));
        reject(error);
      });
  });
};

// export const getNewspaperDetail = (payload: { id: number }) => (dispatch: AppThunk) => {
//   dispatch(newspaperDetailInit());
//   return new Promise((resolve: (value: INewspaperDetail) => void, reject) => {
//     getNewspaperDetailApi(payload)
//       .then((response) => {
//         dispatch(newspaperDetailSuccess(response.data));
//         resolve(response.data);
//       })
//       .catch((error) => {
//         dispatch(newspaperDetailFailure({ message: error, status: true }));
//         reject(error);
//       });
//   });
// };
export const getNewspaperDetail = (payload: { id: number }) =>
  createFetchThunk(
    newspaperDetailInit,
    newspaperDetailSuccess,
    newspaperDetailFailure,
    cacheNewspapersDetail,
    () => getNewspaperDetailApi(payload),
    payload,
    'newspapers',
  );

/*
 * Similar
 */
export const getSimilarNewspapers = (payload: ParamsOptions) => (dispatch: AppThunk) => {
  dispatch(similarNewspapersInit());
  return new Promise((resolve: (value: INewspaperCollections) => void, reject) => {
    getNewspaperCollectionApi(payload)
      .then((response) => {
        dispatch(similarNewspapersSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(similarNewspapersFailure({ message: error, status: true }));
        reject(error);
      });
  });
};
/*
 * Filtered
 */
export const getFilteredNewspapers = (payload: ParamsOptions) => (dispatch: AppThunk) => {
  dispatch(newspapersFilteredInit());
  return new Promise((resolve: (value: INewspaperCollections) => void, reject) => {
    getNewspaperCollectionApi(payload)
      .then((response) => {
        dispatch(newspapersFilteredSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(newspapersFilteredFailure({ message: error, status: true }));
        reject(error);
      });
  });
};
