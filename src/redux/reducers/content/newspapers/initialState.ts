import { INewspapersReducer } from '~/redux/reducers/content/newspapers/types';

export const initialState: INewspapersReducer = {
  error: { message: '', status: false },
  loading: false,
  cache: {
    detail: {},
  },
  collection: {
    count: 0,
    next: null,
    previous: null,
    results: [],
  },

  detail: {
    error: { message: '', status: false },
    loading: false,
    id: 0,
    title: '',
    valid_from: '',
    valid_to: '',
    brand: {
      is_favorite: false,
      id: 0,
      name: '',
      logo: '',
    },
    categories: [],
    image: '',
    is_favorite: false,
  },
  filteredNewspapers: {
    error: { message: '', status: false },
    loading: false,
    count: 0,
    next: null,
    previous: null,
    results: [],
  },
  similar: {
    newspapers: {
      error: { message: '', status: false },
      loading: false,
      count: 0,
      next: null,
      previous: null,
      results: [],
    },
  },
  mayInterestYou: [],
};
