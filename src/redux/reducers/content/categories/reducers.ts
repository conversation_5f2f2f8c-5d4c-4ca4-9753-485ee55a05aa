import { createReducer } from '@reduxjs/toolkit';

import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { CategoryTypeEnum, ShopTypeEnum, SortByEnum } from '~/enums/shared';
import {
  categoriesFailure,
  categoriesInit,
  categoriesStoreTypeChange,
  categoriesSuccess,
  clearFilters,
  countSelected,
  removeSelectedCategories,
  removeSelectedShop,
  removeSelectedSort,
  selectAll,
  selectCategory,
  selectShop,
  selectSort,
  setSelectedCategories,
  setSelectedShop,
  setSelectedSort,
} from '~/redux/reducers/content/categories/actions';
import { ICategoryReducer } from '~/redux/reducers/content/categories/types';
import { ICategoryShop, ICategorySort } from '~/types/category';

const initialState: ICategoryReducer = {
  loading: false,
  error: { message: '', status: false },
  categoryCollection: [],
  shopCollection: [],
  sortCollection: [],
  selectedSort: null,
  selectedShop: null,
  selectedCategories: [],
  selectedCount: 0,
};

const sort: ICategorySort[] = [
  {
    id: 1,
    name: 'Najn<PERSON><PERSON>',
    selected: false,
    type: SortByEnum.NEWEST,
    displayFor: [
      ObjectTypeEnum.Card,
      ObjectTypeEnum.Coupon,
      ObjectTypeEnum.Brand,
      ObjectTypeEnum.Leaflet,
      ObjectTypeEnum.Article,
      ObjectTypeEnum.Inspiration,
    ],
  },
  {
    id: 2,
    name: 'Wygasające',
    selected: false,
    type: SortByEnum.EXPIRING,
    displayFor: [ObjectTypeEnum.Card, ObjectTypeEnum.Coupon, ObjectTypeEnum.Leaflet],
  },
  // TODO FOR FEATURE
  // {
  //   id: 3,
  //   name: 'Największy rabat',
  //   selected: false,
  //   type: SortByEnum.BIGGEST_DISCOUNT,
  //   displayFor: [ObjectTypeEnum.Card, ObjectTypeEnum.Coupon],
  // },
  {
    id: 4,
    name: 'A-z',
    selected: false,
    type: SortByEnum.ALPHABETICAL,
    displayFor: [
      ObjectTypeEnum.Card,
      ObjectTypeEnum.Coupon,
      ObjectTypeEnum.Brand,
      ObjectTypeEnum.Leaflet,
      ObjectTypeEnum.Article,
      ObjectTypeEnum.Inspiration,
    ],
  },
];

const shop: ICategoryShop[] = [
  {
    id: 1,
    name: 'Wszystkie',
    type: ShopTypeEnum.ALL,
    selected: false,
    displayFor: [
      ObjectTypeEnum.Card,
      ObjectTypeEnum.Coupon,
      ObjectTypeEnum.Brand,
      ObjectTypeEnum.Leaflet,
      ObjectTypeEnum.Article,
      ObjectTypeEnum.Inspiration,
    ],
  },
  {
    id: 2,
    name: 'Stacjonarne',
    selected: false,
    type: ShopTypeEnum.STATIONARY,
    displayFor: [ObjectTypeEnum.Card, ObjectTypeEnum.Coupon, ObjectTypeEnum.Brand],
  },
  {
    id: 3,
    name: 'Internetowe',
    selected: false,
    type: ShopTypeEnum.ONLINE,
    displayFor: [ObjectTypeEnum.Card, ObjectTypeEnum.Coupon, ObjectTypeEnum.Brand],
  },
];
export const reducer = createReducer(initialState, (builder) => {
  builder
    .addCase(categoriesInit, (state) => {
      state.loading = true;
    })
    .addCase(categoriesSuccess, (state, action) => {
      state.loading = false;
      state.error = { message: '', status: false };
      state.categoryCollection = action.payload.map((parent) => ({
        ...parent,
        selected: false,
        children: parent.children.map((child) => {
          return {
            ...child,
            selected: false,
            count: child.count,
          };
        }),
      }));
      state.sortCollection = sort;
      state.shopCollection = shop;
    })
    .addCase(categoriesStoreTypeChange, (state, action) => {
      const updatedCategories = action.payload;
      state.categoryCollection.forEach((parent) => {
        const updatedParent = updatedCategories.find((u) => u.id === parent.id);
        if (updatedParent) {
          parent.children = parent.children.map((child) => {
            const updatedChild = updatedParent.children.find((uChild) => uChild.id === child.id);
            return {
              ...child,
              count: updatedChild ? updatedChild.count : child.count,
            };
          });
        }
      });
    })

    .addCase(categoriesFailure, (state, action) => {
      state.loading = false;
      state.error = action.payload;
    })
    .addCase(selectCategory, (state, action) => {
      const id = action.payload;
      state.categoryCollection.forEach((parent) => {
        if (parent.id === id) {
          parent.selected = !parent.selected;
          state.selectedCount = state.selectedCount > 0 ? state.selectedCount - 1 : 0;
          return;
        }
        parent.children.forEach((child) => {
          if (child.id === id) {
            child.selected = !child.selected;
            return;
          }
        });
      });
    })
    .addCase(selectAll, (state, action) => {
      state.categoryCollection.forEach((parent) => {
        const id = action.payload;
        if (id === parent.id) {
          if (parent.selected) {
            // Jeśli kategoria nadrzędna jest zaznaczona, usuń wszystkie podrzędne kategorie z tablicy selectedCategories
            parent.selected = false;
            parent.children.forEach((child) => {
              const index = state.selectedCategories.findIndex((sc) => sc.id === child.id);
              if (index !== -1) {
                state.selectedCategories.splice(index, 1);
              }
              child.selected = false;
            });
          } else {
            // Jeśli kategoria nadrzędna nie jest zaznaczona, dodaj wszystkie podrzędne kategorie do tablicy selectedCategories
            parent.selected = true;
            parent.children.forEach((child) => {
              if (!state.selectedCategories.some((sc) => sc.id === child.id)) {
                state.selectedCategories.push({
                  id: child.id,
                  categoryType: CategoryTypeEnum.CATEGORY,
                  name: child.name,
                });
              }
              child.selected = true;
            });
          }
        }
      });
    })
    .addCase(selectSort, (state, action) => {
      const id = action.payload;
      state.sortCollection.forEach((item) => {
        if (item.id === id) {
          item.selected = true;
          return;
        } else {
          item.selected = false;
        }
      });
    })
    .addCase(selectShop, (state, action) => {
      const id = action.payload;
      state.shopCollection.forEach((item) => {
        if (item.id === id) {
          item.selected = true;
          return;
        } else {
          item.selected = false;
        }
      });
    })
    .addCase(clearFilters, (state) => {
      state.categoryCollection.forEach((parent) => {
        parent.selected = false;
        parent.children.forEach((child) => {
          child.selected = false;
        });
      });
      state.sortCollection.forEach((item) => {
        item.selected = false;
      });
      state.shopCollection.forEach((item) => {
        item.selected = false;
      });
      state.selectedShop = null;
      state.selectedSort = null;
      state.selectedCategories = [];
    })
    .addCase(countSelected, (state) => {
      let count = 0;
      state.categoryCollection.forEach((parent) => {
        if (parent.selected) count++;
        parent.children.forEach((child) => {
          if (child.selected) count++;
        });
      });

      state.sortCollection.forEach((item) => {
        if (item.selected) count++;
      });

      state.shopCollection.forEach((item) => {
        if (item.selected) count++;
      });

      state.selectedCount = count;
    })
    .addCase(setSelectedSort, (state, action) => {
      state.selectedSort = action.payload;
    })
    .addCase(setSelectedShop, (state, action) => {
      state.selectedShop = action.payload;
    })
    .addCase(setSelectedCategories, (state, action) => {
      const itemIdExists = state.selectedCategories.some((item) => item.id === action.payload.id);
      if (itemIdExists) {
        state.selectedCategories = state.selectedCategories.filter(
          (item) => item.id !== action.payload.id,
        );
      } else {
        state.selectedCategories.push(action.payload);
      }
    })
    .addCase(removeSelectedSort, (state) => {
      state.selectedSort = null;
      state.sortCollection.map((item) => {
        item.selected = false;
      });
    })
    .addCase(removeSelectedShop, (state) => {
      state.selectedShop = null;
      state.shopCollection.map((item) => {
        item.selected = false;
      });
    })
    .addCase(removeSelectedCategories, (state, action) => {
      state.selectedCategories = state.selectedCategories.filter(
        (item) => item.id !== action.payload.id,
      );
    });
});
