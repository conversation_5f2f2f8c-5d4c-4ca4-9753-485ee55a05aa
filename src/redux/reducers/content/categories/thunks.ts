import { getCategoryCollectionApi } from '~/redux/api/categories';
import {
  categoriesFailure,
  categoriesInit,
  categoriesStoreTypeChange,
  categoriesSuccess,
} from '~/redux/reducers/content/categories/actions';
import { AppThunk } from '~/redux/store';
import { IParentCategory } from '~/types/category';
import { ParamsOptions } from '~/types/shared';

export const getCategories = (payload?: ParamsOptions) => (dispatch: AppThunk) => {
  dispatch(categoriesInit());
  return new Promise((resolve: (value: IParentCategory[]) => void, reject) => {
    getCategoryCollectionApi(payload)
      .then((response) => {
        dispatch(categoriesSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(categoriesFailure({ message: error, status: true }));
        reject(error);
      });
  });
};

export const getCategoriesWithStoreTypeChange =
  (payload?: ParamsOptions) => (dispatch: AppThunk) => {
    dispatch(categoriesInit());
    return new Promise((resolve: (value: IParentCategory[]) => void, reject) => {
      getCategoryCollectionApi(payload)
        .then((response) => {
          dispatch(categoriesStoreTypeChange(response.data));
          resolve(response.data);
        })
        .catch((error) => {
          dispatch(categoriesFailure({ message: error, status: true }));
          reject(error);
        });
    });
  };
