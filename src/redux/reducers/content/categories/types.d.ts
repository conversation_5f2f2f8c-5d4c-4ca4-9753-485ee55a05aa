import { CategoryTypeEnum, ShopTypeEnum, SortByEnum } from '~/enums/shared';
import { IReducer } from '~/redux/types/common';
import { ICategoryShop, ICategorySort, IParentCategory } from '~/types/category';

export interface ICategoryReducer extends IReducer {
  categoryCollection: IParentCategory[];
  sortCollection: ICategorySort[];
  shopCollection: ICategoryShop[];
  selectedSort?: {
    id: number;
    categoryType: CategoryTypeEnum;
    name: string;
    objectType: SortByEnum;
  } | null;
  selectedShop?: {
    id: number;
    categoryType: CategoryTypeEnum;
    name: string;
    objectType: ShopTypeEnum;
  } | null;
  selectedCategories: {
    id: number;
    categoryType: CategoryTypeEnum;
    name: string;
  }[];

  selectedCount: number;
}
