import { createAction } from '@reduxjs/toolkit';

import { CategoryTypeEnum, ShopTypeEnum, SortByEnum } from '~/enums/shared';
import { IError } from '~/redux/types/common.d';
import { IParentCategory } from '~/types/category';

export const categoriesInit = createAction('categories/init');
export const categoriesSuccess = createAction<IParentCategory[]>('categories/success');
export const categoriesFailure = createAction<IError>('categories/failure');

export const categoriesStoreTypeChange = createAction<IParentCategory[]>(
  'categories/storeTypeChange',
);
export const selectCategory = createAction<number>('categories/select');
export const selectSort = createAction<number>('categories/sort');
export const selectShop = createAction<number>('categories/shop');
export const selectAll = createAction<number>('categories/all');
export const clearFilters = createAction('categories/clear');
export const countSelected = createAction('categories/count');
export const setSelectedSort = createAction<{
  id: number;
  categoryType: CategoryTypeEnum;
  name: string;
  objectType: SortByEnum;
}>('categories/setSelectedSort');
export const setSelectedShop = createAction<{
  id: number;
  categoryType: CategoryTypeEnum;
  name: string;
  objectType: ShopTypeEnum;
}>('categories/setSelectedShop');
export const setSelectedCategories = createAction<{
  id: number;
  categoryType: CategoryTypeEnum;
  name: string;
}>('categories/setSelectedCategories');
export const removeSelectedSort = createAction('categories/removeSelectedSort');
export const removeSelectedShop = createAction('categories/removeSelectedShop');
export const removeSelectedCategories = createAction<{ id: number }>(
  'categories/removeSelectedCategories',
);
