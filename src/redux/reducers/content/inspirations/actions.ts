import { createAction } from '@reduxjs/toolkit';

import { IInspirationDetailCache, IInspirationPage } from './types';

import { IError } from '~/redux/types/common.d';
import { IFavoriteReducer } from '~/types/favorite';
import { IInspiration, IInspirationCollections, IInspirationDetails } from '~/types/inspirations';

export const inspirationsInit = createAction('inspirations/collection/init');
export const inspirationsSuccess = createAction<IInspirationCollections>(
  'inspirations/collection/success',
);
export const inspirationsFailure = createAction<IError>('inspirations/collection/failure');
/*
 * Detail
 */
export const inspirationDetailInit = createAction('inspirations/detail/init');
export const inspirationDetailSuccess = createAction<IInspirationDetails>(
  'inspirations/detail/success',
);
export const inspirationDetailFailure = createAction<IError>('inspirations/detail/failure');
/*
 * Similar
 */
export const similarInspirationsInit = createAction('inspirations/similar/init');
export const similarInspirationsSuccess = createAction<IInspirationCollections>(
  'inspirations/similar/success',
);
export const similarInspirationsFailure = createAction<IError>('inspirations/similar/failure');
/*
 * Filtered
 */
export const inspirationsFilteredInit = createAction('inspirations/filtered/init');
export const inspirationsFilteredSuccess = createAction<IInspirationCollections>(
  'inspirations/filtered/success',
);
export const inspirationsFilteredFailure = createAction<IError>('inspirations/filtered/failure');

export const inspirationChangeFavorite = createAction<IFavoriteReducer>(
  'inspirations/change/favorite',
);

/*
 * Search
 * may interest you content
 */
export const inspirationsMayInterestYou = createAction<IInspiration[]>(
  'inspirations/may/interest/you',
);
/*
 * Discount cache
 */
export const cacheInspirationDetail = createAction<IInspirationDetailCache>(
  'inspirations/cache/detail',
);

export const inspirationPage = createAction<IInspirationPage>('inspiration/page');
