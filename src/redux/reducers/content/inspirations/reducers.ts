import { createReducer } from '@reduxjs/toolkit';

import {
  favorite<PERSON><PERSON><PERSON><PERSON>per,
  favoriteHelper,
  favoriteSingleHelper,
} from '~/redux/helpers/favoriteHelper';
import {
  cacheInspirationDetail,
  inspirationChangeFavorite,
  inspirationDetailFailure,
  inspirationDetailInit,
  inspirationDetailSuccess,
  inspirationPage,
  inspirationsFailure,
  inspirationsFilteredFailure,
  inspirationsFilteredInit,
  inspirationsFilteredSuccess,
  inspirationsInit,
  inspirationsMayInterestYou,
  inspirationsSuccess,
  similarInspirationsFailure,
  similarInspirationsInit,
  similarInspirationsSuccess,
} from '~/redux/reducers/content/inspirations/actions';
import { initialState } from '~/redux/reducers/content/inspirations/initialState';

export const reducer = createReducer(initialState, (builder) => {
  //COLLECTION
  builder.addCase(inspirationsInit, (state) => {
    state.loading = true;
  });
  builder.addCase(inspirationsSuccess, (state, action) => {
    state.loading = false;
    state.error = { message: '', status: false };
    state.collection = action.payload;
  });
  builder.addCase(inspirationsFailure, (state, action) => {
    state.loading = false;
    state.error = action.payload;
  });
  //DETAIL
  builder.addCase(inspirationDetailInit, (state) => {
    state.detail.loading = true;
  });
  builder.addCase(inspirationDetailSuccess, (state, action) => {
    state.detail.loading = false;
    state.detail.error = { message: '', status: false };
    state.detail = {
      ...state.detail,
      ...action.payload,
    };
  });
  builder.addCase(inspirationDetailFailure, (state, action) => {
    state.detail.loading = false;
    state.detail.error = action.payload;
  });
  //SIMILAR
  builder.addCase(similarInspirationsInit, (state) => {
    state.similar.inspirations.loading = true;
  });
  builder.addCase(similarInspirationsSuccess, (state, action) => {
    state.similar.inspirations.loading = false;
    state.similar.inspirations.error = { message: '', status: false };
    state.similar.inspirations = {
      ...state.similar.inspirations,
      ...action.payload,
    };
  });
  builder.addCase(similarInspirationsFailure, (state, action) => {
    state.similar.inspirations.loading = false;
    state.similar.inspirations.error = action.payload;
  });
  /*
   * Filtered
   */
  builder.addCase(inspirationsFilteredInit, (state) => {
    state.filteredInspirations.loading = true;
  });
  builder.addCase(inspirationsFilteredSuccess, (state, action) => {
    state.filteredInspirations.loading = false;
    state.filteredInspirations.error = { message: '', status: false };
    state.filteredInspirations.count = action.payload.count;
    state.filteredInspirations.next = action.payload.next;
    state.filteredInspirations.previous = action.payload.previous;
    state.filteredInspirations.results = action.payload.results;
  });
  builder.addCase(inspirationsFilteredFailure, (state, action) => {
    state.filteredInspirations.loading = false;
    state.filteredInspirations.error = action.payload;
  });
  //FAVORITE
  builder.addCase(inspirationChangeFavorite, (state, action) => {
    state.collection = {
      ...state.collection,
      results: favoriteHelper({
        state: state.collection.results,
        objectId: action.payload.objectId,
        isFavorite: action.payload.isFavorite,
      }),
    };
    state.filteredInspirations = {
      ...state.filteredInspirations,
      results: favoriteHelper({
        state: state.filteredInspirations.results,
        objectId: action.payload.objectId,
        isFavorite: action.payload.isFavorite,
      }),
    };
    state.similar.inspirations = {
      ...state.similar.inspirations,
      results: favoriteHelper({
        state: state.similar.inspirations.results,
        objectId: action.payload.objectId,
        isFavorite: action.payload.isFavorite,
      }),
    };
    state.mayInterestYou = favoriteHelper({
      state: state.mayInterestYou,
      objectId: action.payload.objectId,
      isFavorite: action.payload.isFavorite,
    });
    state.detail = favoriteSingleHelper({
      state: state.detail,
      objectId: action.payload.objectId,
      isFavorite: action.payload.isFavorite,
    });
    state.cache.detail = favoriteCacheHelper({
      state: state.cache.detail,
      objectId: action.payload.objectId,
      isFavorite: action.payload.isFavorite,
    });
  });
  /*
   * Search
   * may interest you content
   */
  builder.addCase(inspirationsMayInterestYou, (state, action) => {
    state.mayInterestYou = action.payload;
  });
  // PAGINATION
  builder.addCase(inspirationPage, (state, action) => {
    switch (action.payload.listType) {
      case 'collection':
        state.collection = {
          count: action.payload.collection.count,
          next: action.payload.collection.next,
          previous: action.payload.collection.previous,
          results: [...state.collection.results, ...action.payload.collection.results],
        };
        break;
      case 'filtered':
        state.filteredInspirations = {
          loading: false,
          error: { message: '', status: false },
          count: action.payload.collection.count,
          next: action.payload.collection.next,
          previous: action.payload.collection.previous,
          results: [...state.filteredInspirations.results, ...action.payload.collection.results],
        };
        break;
      case 'similar':
        state.similar.inspirations = {
          loading: false,
          error: { message: '', status: false },
          count: action.payload.collection.count,
          next: action.payload.collection.next,
          previous: action.payload.collection.previous,
          results: [...state.similar.inspirations.results, ...action.payload.collection.results],
        };
        break;
    }
  });
  /*
   * Discount cache
   */
  builder.addCase(cacheInspirationDetail, (state, action) => {
    return {
      ...state,
      cache: {
        ...state.cache,
        detail: {
          ...state.cache.detail,
          [action.payload.id]: {
            data: action.payload.data,
            timestamp: action.payload.timestamp,
          },
        },
      },
    };
  });
});
