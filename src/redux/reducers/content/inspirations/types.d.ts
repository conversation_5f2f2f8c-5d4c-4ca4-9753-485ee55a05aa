import { IReducer } from '~/redux/types/common';
import { IInspiration, IInspirationCollections, IInspirationDetails } from '~/types/inspirations';

export type TInspirationCollection = IInspirationCollections & IReducer;
export type TInspirationDetail = IInspirationDetails & IReducer;

export interface IInspirationDetailCache {
  id: number;
  data: IInspirationDetails;
  timestamp: number;
}

export interface IInspirationReducer extends IReducer {
  collection: IInspirationCollections;
  detail: TInspirationDetail;
  cache: {
    detail: {
      [key: string]: Omit<IInspirationDetailCache, 'id'>;
    };
  };
  similar: {
    inspirations: TInspirationCollection;
  };
  filteredInspirations: TInspirationCollection;
  mayInterestYou: IInspiration[];
}

export interface IInspirationPage {
  listType: 'similar' | 'filtered' | 'collection';
  collection: IInspirationCollections;
}
