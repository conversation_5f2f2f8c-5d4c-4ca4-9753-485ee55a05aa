import { IInspirationReducer } from '~/redux/reducers/content/inspirations/types';

export const initialState: IInspirationReducer = {
  error: { message: '', status: false },
  loading: false,
  cache: {
    detail: {},
  },
  collection: {
    count: 0,
    next: null,
    previous: null,
    results: [],
  },
  detail: {
    error: { message: '', status: false },
    loading: false,
    id: 0,
    brand: {
      id: 0,
      name: '',
      logo: '',
      is_favorite: false,
    },
    first_image: '',
    categories: [],
    is_favorite: null,
    images: [],
    title: '',
    subtitle: '',
    description: '',
    products: [],
  },
  similar: {
    inspirations: {
      error: { message: '', status: false },
      loading: false,
      count: 0,
      next: null,
      previous: null,
      results: [],
    },
  },
  filteredInspirations: {
    error: { message: '', status: false },
    loading: false,
    count: 0,
    next: null,
    previous: null,
    results: [],
  },
  mayInterestYou: [],
};
