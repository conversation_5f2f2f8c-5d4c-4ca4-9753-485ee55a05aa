import { getInspirationCollection<PERSON><PERSON>, getInspirationDetailApi } from '~/redux/api/inspirations';
import {
  cacheInspirationDetail,
  inspirationDetailFailure,
  inspirationDetailInit,
  inspirationDetailSuccess,
  inspirationsFailure,
  inspirationsFilteredFailure,
  inspirationsFilteredInit,
  inspirationsFilteredSuccess,
  inspirationsInit,
  inspirationsSuccess,
  similarInspirationsFailure,
  similarInspirationsInit,
  similarInspirationsSuccess,
} from '~/redux/reducers/content/inspirations/actions';
import { AppThunk } from '~/redux/store';
import { IInspirationCollections } from '~/types/inspirations';
import { ParamsOptions } from '~/types/shared';
import { createFetchThunk } from '~/utils/thunk.config';

export const getInspirations = (payload?: ParamsOptions) => (dispatch: AppThunk) => {
  dispatch(inspirationsInit());
  return new Promise((resolve: (value: IInspirationCollections) => void, reject) => {
    getInspirationCollectionApi(payload)
      .then((response) => {
        dispatch(inspirationsSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(inspirationsFailure({ message: error, status: true }));
        reject(error);
      });
  });
};
/*
 *Similar
 */
export const getSimilarInspirations = (payload: ParamsOptions) => (dispatch: AppThunk) => {
  dispatch(similarInspirationsInit());
  return new Promise((resolve: (value: IInspirationCollections) => void, reject) => {
    getInspirationCollectionApi(payload)
      .then((response) => {
        dispatch(similarInspirationsSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(
          similarInspirationsFailure({
            message: error,
            status: true,
          }),
        );
        reject(error);
      });
  });
};
/*
 * Filtered
 */
export const getFilteredInspirations = (payload: ParamsOptions) => (dispatch: AppThunk) => {
  dispatch(inspirationsFilteredInit());
  return new Promise((resolve: (value: IInspirationCollections) => void, reject) => {
    getInspirationCollectionApi(payload)
      .then((response) => {
        dispatch(inspirationsFilteredSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(inspirationsFilteredFailure({ message: error, status: true }));
        reject(error);
      });
  });
};

export const getInspirationDetail = (payload: { id: number }) =>
  createFetchThunk(
    inspirationDetailInit,
    inspirationDetailSuccess,
    inspirationDetailFailure,
    cacheInspirationDetail,
    () => getInspirationDetailApi(payload),
    payload,
    'inspirations',
  );
