import { createAction } from '@reduxjs/toolkit';

import { IBrandPage, IBrandsDetailCache } from './types';

import { IError } from '~/redux/types/common.d';
import { IAddress } from '~/types/addresses';
import { IBrandCollections, IBrandDetail } from '~/types/brands';
import { IFavoriteReducer } from '~/types/favorite';

/*
 * Brands
 */
export const brandsInit = createAction('brands/collection/init');
export const brandsSuccess = createAction<IBrandCollections>('brands/collection/success');
export const brandsFailure = createAction<IError>('brands/collection/failure');

export const brandDetailInit = createAction('brands/detail/init');
export const brandDetailSuccess = createAction<IBrandDetail>('brands/detail/success');
export const brandDetailFailure = createAction<IError>('brands/detail/failure');

export const brandsAddressesInit = createAction('brands/addresses/init');
export const brandsAddressesSuccess = createAction<IAddress[]>('brands/addresses/success');
export const brandsAddressesFailure = createAction<IError>('brands/addresses/failure');

/*
 *Filtered
 */
export const brandsFilteredInit = createAction('brands/filtered/init');
export const brandsFilteredSuccess = createAction<IBrandCollections>('brands/filtered/success');
export const brandsFilteredFailure = createAction<IError>('brands/filtered/failure');

/*
 * Discount cache
 */
export const cacheBrandDetail = createAction<IBrandsDetailCache>('brands/cache/detail');

export const brandChangeFavorite = createAction<IFavoriteReducer>('brands/change/favorite');
/*
 * Search
 * may interest you content
 */
export const brandsMayInterestYou = createAction<IBrandDetail[]>('brands/may/interest/you');

export const brandPage = createAction<IBrandPage>('brands/page');
