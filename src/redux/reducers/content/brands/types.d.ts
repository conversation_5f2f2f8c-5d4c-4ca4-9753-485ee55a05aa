import { IReducer } from '~/redux/types/common';
import { IAddress } from '~/types/addresses';
import { IBrandCollections, IBrandDetail } from '~/types/brands';

export type TBrandCollection = IBrandCollections & IReducer;
export type TBrandDetail = IBrandDetail & IReducer;

export interface IBrandsDetailCache {
  id: number;
  data: IBrandDetail;
  timestamp: number;
}

export interface IBrandsReducer extends IReducer {
  collection: IBrandCollections;
  detail: TBrandDetail;
  cache: {
    detail: {
      [key: string]: Omit<IBrandsDetailCache, 'id'>;
    };
  };
  addresses: IAddress[];
  filteredBrands: TBrandCollection;
  mayInterestYou: IBrandDetail[];
}

export interface IBrandPage {
  listType: 'similar' | 'filtered' | 'collection';
  collection: IBrandCollections;
}
