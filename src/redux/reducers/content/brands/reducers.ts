import { createReducer } from '@reduxjs/toolkit';

import {
  favorite<PERSON><PERSON><PERSON>elper,
  favoriteHelper,
  favoriteSingleHelper,
} from '~/redux/helpers/favoriteHelper';
import {
  brandChangeFavorite,
  brandDetailFailure,
  brandDetailInit,
  brandDetailSuccess,
  brandPage,
  brandsAddressesFailure,
  brandsAddressesInit,
  brandsAddressesSuccess,
  brandsFailure,
  brandsFilteredFailure,
  brandsFilteredInit,
  brandsFilteredSuccess,
  brandsInit,
  brandsMayInterestYou,
  brandsSuccess,
  cacheBrandDetail,
} from '~/redux/reducers/content/brands/actions';
import { initialState } from '~/redux/reducers/content/brands/initialState';

export const reducer = createReducer(initialState, (builder) => {
  //COLLECTION
  builder.addCase(brandsInit, (state) => {
    state.loading = true;
  });
  builder.addCase(brandsSuccess, (state, action) => {
    state.loading = false;
    state.error = { message: '', status: false };
    state.collection = action.payload;
  });
  builder.addCase(brandsFailure, (state, action) => {
    state.loading = false;
    state.error = action.payload;
  });
  //DETAIL
  builder.addCase(brandDetailInit, (state) => {
    state.detail.loading = true;
  });
  builder.addCase(brandDetailSuccess, (state, action) => {
    state.detail.loading = false;
    state.detail.error = { message: '', status: false };
    state.detail = {
      ...state.detail,
      ...action.payload,
    };
  });
  builder.addCase(brandDetailFailure, (state, action) => {
    state.detail.loading = false;
    state.detail.error = action.payload;
  });
  /*
   * Filtered
   */
  builder.addCase(brandsFilteredInit, (state) => {
    state.filteredBrands.loading = true;
  });
  builder.addCase(brandsFilteredSuccess, (state, action) => {
    state.filteredBrands.loading = false;
    state.filteredBrands.error = { message: '', status: false };

    state.filteredBrands.count = action.payload.count;
    state.filteredBrands.next = action.payload.next;
    state.filteredBrands.previous = action.payload.previous;
    state.filteredBrands.results = action.payload.results;
  });
  builder.addCase(brandsFilteredFailure, (state, action) => {
    state.filteredBrands.loading = false;
    state.filteredBrands.error = action.payload;
  });
  // ADDRESSES
  builder.addCase(brandsAddressesInit, (state) => {
    state.loading = true;
  });
  builder.addCase(brandsAddressesSuccess, (state, action) => {
    state.loading = false;
    state.error = { message: '', status: false };
    state.addresses = action.payload;
  });
  builder.addCase(brandsAddressesFailure, (state, action) => {
    state.loading = false;
    state.error = action.payload;
  });

  //FAVORITE
  builder.addCase(brandChangeFavorite, (state, action) => {
    state.collection = {
      ...state.collection,
      results: favoriteHelper({
        state: state.collection.results,
        objectId: action.payload.objectId,
        isFavorite: action.payload.isFavorite,
      }),
    };
    state.filteredBrands = {
      ...state.filteredBrands,
      results: favoriteHelper({
        state: state.filteredBrands.results,
        objectId: action.payload.objectId,
        isFavorite: action.payload.isFavorite,
      }),
    };
    state.mayInterestYou = favoriteHelper({
      state: state.mayInterestYou,
      objectId: action.payload.objectId,
      isFavorite: action.payload.isFavorite,
    });
    state.detail = favoriteSingleHelper({
      state: state.detail,
      objectId: action.payload.objectId,
      isFavorite: action.payload.isFavorite,
    });
    state.cache.detail = favoriteCacheHelper({
      state: state.cache.detail,
      objectId: action.payload.objectId,
      isFavorite: action.payload.isFavorite,
    });
  });
  /*
   * Search
   * may interest you content
   */
  builder.addCase(brandsMayInterestYou, (state, action) => {
    state.mayInterestYou = action.payload;
  });

  // PAGINATION
  builder.addCase(brandPage, (state, action) => {
    switch (action.payload.listType) {
      case 'collection':
        state.collection = {
          count: action.payload.collection.count,
          next: action.payload.collection.next,
          previous: action.payload.collection.previous,
          results: [...state.collection.results, ...action.payload.collection.results],
        };
        break;
      case 'filtered':
        state.filteredBrands = {
          loading: false,
          error: { message: '', status: false },
          count: action.payload.collection.count,
          next: action.payload.collection.next,
          previous: action.payload.collection.previous,
          results: [...state.filteredBrands.results, ...action.payload.collection.results],
        };
        break;
    }
  });
  /*
   * Discount cache
   */
  builder.addCase(cacheBrandDetail, (state, action) => {
    return {
      ...state,
      cache: {
        ...state.cache,
        detail: {
          ...state.cache.detail,
          [action.payload.id]: {
            data: action.payload.data,
            timestamp: action.payload.timestamp,
          },
        },
      },
    };
  });
});
