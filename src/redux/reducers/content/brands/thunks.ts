import { getBrandAddress<PERSON><PERSON>, getBrandCollection<PERSON>pi, getBrandDetailApi } from '~/redux/api/brands';
import {
  brandDetailFailure,
  brandDetailInit,
  brandDetailSuccess,
  brandsAddressesFailure,
  brandsAddressesInit,
  brandsAddressesSuccess,
  brandsFailure,
  brandsFilteredFailure,
  brandsFilteredInit,
  brandsFilteredSuccess,
  brandsInit,
  brandsSuccess,
  cacheBrandDetail,
} from '~/redux/reducers/content/brands/actions';
import { AppThunk } from '~/redux/store';
import { IAddress } from '~/types/addresses';
import { IBrandCollections } from '~/types/brands';
import { ParamsOptions } from '~/types/shared';
import { createFetchThunk } from '~/utils/thunk.config';

/*
 *Collection
 */
export const getBrands = (payload?: ParamsOptions) => (dispatch: AppThunk) => {
  dispatch(brandsInit());
  return new Promise((resolve: (value: IBrandCollections) => void, reject) => {
    getBrandCollectionApi(payload)
      .then((response) => {
        dispatch(brandsSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(brandsFailure({ message: error, status: true }));
        reject(error);
      });
  });
};

/*
 * Detail
 */
export const getBrandDetail = (payload: { id: number }) =>
  createFetchThunk(
    brandDetailInit,
    brandDetailSuccess,
    brandDetailFailure,
    cacheBrandDetail,
    () => getBrandDetailApi(payload),
    payload,
    'brands',
  );

/*
 * Filtered
 */
export const getFilteredBrands = (payload: ParamsOptions) => (dispatch: AppThunk) => {
  dispatch(brandsFilteredInit());
  return new Promise((resolve: (value: IBrandCollections) => void, reject) => {
    getBrandCollectionApi(payload)
      .then((response) => {
        dispatch(brandsFilteredSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(brandsFilteredFailure({ message: error, status: true }));
        reject(error);
      });
  });
};

/*
 * Addresses for specific brand
 */
export const getBrandAddresses = (payload: { id: number }) => (dispatch: AppThunk) => {
  dispatch(brandsAddressesInit());
  return new Promise((resolve: (value: IAddress[]) => void, reject) => {
    getBrandAddressApi(payload)
      .then((response) => {
        dispatch(brandsAddressesSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(brandsAddressesFailure({ message: error, status: true }));
        reject(error);
      });
  });
};
