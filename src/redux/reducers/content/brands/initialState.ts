import { IBrandsReducer } from '~/redux/reducers/content/brands/types';

export const initialState: IBrandsReducer = {
  error: { message: '', status: false },
  loading: false,
  collection: { count: 0, next: null, previous: null, results: [] },
  cache: {
    detail: {},
  },
  detail: {
    error: { message: '', status: false },
    loading: false,
    id: 0,
    name: '',
    logo: '',
    bg_color: '',
    is_favorite: false,
    image: '',
    categories: [],
    description: '',
    site: '',
    site_btn_text: null,
  },
  addresses: [],
  filteredBrands: {
    error: { message: '', status: false },
    loading: false,
    count: 0,
    next: null,
    previous: null,
    results: [],
  },
  mayInterestYou: [],
};
