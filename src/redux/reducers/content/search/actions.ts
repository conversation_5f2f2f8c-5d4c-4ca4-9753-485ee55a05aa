import { createAction } from '@reduxjs/toolkit';

import { IError } from '~/redux/types/common.d';
import { ISearchResultData } from '~/types/search';

export const popularSearchesInit = createAction('popularSearches/init');
export const popularSearchesSuccess = createAction<ISearchResultData[]>('popularSearches/success');
export const popularSearchesFailure = createAction<IError>('popularSearches/failure');
export const setPopularSearchesValue = createAction<string>('popularSearches/value');
export const setSearchString = createAction<string>('popularSearches/searchString');
export const clearPopularSearchesValue = createAction('popularSearches/clear');
