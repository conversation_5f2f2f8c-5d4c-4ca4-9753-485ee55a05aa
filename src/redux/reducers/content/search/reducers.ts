import { createReducer } from '@reduxjs/toolkit';

import {
  clearPopularSearchesValue,
  popularSearchesFailure,
  popularSearchesInit,
  popularSearchesSuccess,
  setPopularSearchesValue,
  setSearchString,
} from '~/redux/reducers/content/search/actions';
import { IPopularSearchesReducer } from '~/redux/reducers/content/search/types';

const initialState: IPopularSearchesReducer = {
  error: { message: '', status: false },
  loading: false,
  data: [],
  value: '',
  searchString: '',
};

export const reducer = createReducer(initialState, (builder) => {
  //COLLECTION
  builder.addCase(popularSearchesInit, (state) => {
    state.loading = true;
  });
  builder.addCase(popularSearchesSuccess, (state, action) => {
    state.loading = false;
    state.error = { message: '', status: false };
    state.data = action.payload;
  });
  builder.addCase(popularSearchesFailure, (state, action) => {
    state.loading = false;
    state.error = action.payload;
  });
  builder.addCase(setPopularSearchesValue, (state, action) => {
    state.value = action.payload;
  });
  builder.addCase(setSearchString, (state, action) => {
    state.searchString = action.payload;
  });
  //CLEAR
  builder.addCase(clearPopularSearchesValue, (state) => {
    state.value = '';
  });
});
