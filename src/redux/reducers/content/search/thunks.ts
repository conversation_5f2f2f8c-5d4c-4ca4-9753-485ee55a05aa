import { getPopularCollection<PERSON>pi, getSearchCollectionApi } from '~/redux/api/search';
import {
  popularSearchesFailure,
  popularSearchesInit,
  popularSearchesSuccess,
} from '~/redux/reducers/content/search/actions';
import { AppThunk } from '~/redux/store';
import { ISearchResultData } from '~/types/search';
import { ParamsOptions } from '~/types/shared';

export const getSearch = (payload?: ParamsOptions) => {
  return new Promise((resolve: (value: ISearchResultData[]) => void, reject) => {
    getSearchCollectionApi(payload)
      .then((response) => {
        resolve(response.data);
      })
      .catch((error) => {
        reject(error);
      });
  });
};

export const getPopular = () => (dispatch: AppThunk) => {
  dispatch(popularSearchesInit());
  return new Promise((resolve: (value: ISearchResultData[]) => void, reject) => {
    getPopularCollectionApi()
      .then((response) => {
        dispatch(popularSearchesSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(popularSearchesFailure({ message: error, status: true }));
        reject(error);
      });
  });
};
