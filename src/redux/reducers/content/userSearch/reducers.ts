import { createReducer } from '@reduxjs/toolkit';

import { IPopularSearchesReducer } from './types';
import { setUserSearches } from './actions';

const initialState: IPopularSearchesReducer = {
  error: { message: '', status: false },
  loading: false,
  userSearches: [],
};

export const reducer = createReducer(initialState, (builder) => {
  //USER SEARCHES
  builder.addCase(setUserSearches, (state, action) => {
    const newSearch = action.payload;

    const alreadyExists = state.userSearches.some((search) => search.id === newSearch.id);

    if (!alreadyExists) {
      state.userSearches.push(newSearch);

      if (state.userSearches.length > 10) {
        state.userSearches.shift();
      }
    }
  });
});
