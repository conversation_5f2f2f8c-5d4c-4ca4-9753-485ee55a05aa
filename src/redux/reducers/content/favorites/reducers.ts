import { createReducer, current } from '@reduxjs/toolkit';

import {
  favoriteChange<PERSON><PERSON><PERSON>elper,
  favoriteD<PERSON>teHelper,
  favoriteGetAllHelper,
} from '~/redux/helpers/favoriteHelper';
import {
  favoritesAllChange,
  favoritesAllFailure,
  favoritesAllInit,
  favoritesAllSuccess,
  favoritesDeleteSuccess,
} from '~/redux/reducers/content/favorites/actions';
import { initialState } from '~/redux/reducers/content/favorites/initialState';

export const reducer = createReducer(initialState, (builder) => {
  //ALL
  builder.addCase(favoritesAllInit, (state) => {
    state.loading = true;
  });
  builder.addCase(favoritesAllSuccess, (state, action) => {
    state.loading = false;
    state.error = { message: '', status: false };
    state.download = true;
    state.collection = favoriteGetAllHelper({
      results: action.payload,
    });
  });
  builder.addCase(favoritesAllFailure, (state, action) => {
    state.loading = false;
    state.error = action.payload;
  });
  //DELETE
  builder.addCase(favoritesDeleteSuccess, (state, action) => {
    state.collection = favoriteDeleteHelper({
      collection: current(state.collection),
      objectId: action.payload.object_id,
      objectType: action.payload.object_type,
    });
  });
  //CHANGE
  builder.addCase(favoritesAllChange, (state, action) => {
    state.collection = favoriteChangeAllHelper({
      collection: current(state.collection),
      objectId: action.payload.objectId,
      objectType: action.payload.objectType,
      isFavorite: action.payload.isFavorite,
      bookmark: action.payload.bookmark,
    });
  });
});
