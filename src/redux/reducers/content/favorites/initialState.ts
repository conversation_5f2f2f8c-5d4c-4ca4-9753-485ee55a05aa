import { IFavoritesReducer } from '~/redux/reducers/content/favorites/types';

export const defaultValues = {
  count: 0,
  next: null,
  previous: null,
  results: [],
};

export const initialState: IFavoritesReducer = {
  error: { message: '', status: false },
  loading: false,
  download: false,
  collection: {
    article: defaultValues,
    brand: defaultValues,
    card: defaultValues,
    coupon: defaultValues,
    inspiration: defaultValues,
    leaflet: defaultValues,
  },
};
