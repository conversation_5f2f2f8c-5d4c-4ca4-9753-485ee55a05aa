import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import {
  deleteFavorite<PERSON>pi,
  getFavoriteCollection<PERSON>pi,
  postFavoriteApi,
  putFavoriteApi,
} from '~/redux/api/favorites';
import {
  favoritesAllFailure,
  favoritesAllInit,
  favoritesAllSuccess,
  favoritesDeleteFailure,
  favoritesDeleteInit,
  favoritesDeleteSuccess,
  favoritesPostFailure,
  favoritesPostInit,
  favoritesPostSuccess,
  favoritesPutFailure,
  favoritesPutInit,
  favoritesPutSuccess,
} from '~/redux/reducers/content/favorites/actions';
import { IFavoritesCollection } from '~/redux/reducers/content/favorites/types';
import { AppThunk } from '~/redux/store';

export const getFavoritesAll = () => (dispatch: AppThunk) => {
  dispatch(favoritesAllInit());
  return new Promise((resolve: (value: IFavoritesCollection) => void, reject) => {
    getFavoriteCollectionApi()
      .then((response) => {
        dispatch(favoritesAllSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(favoritesAllFailure({ message: error, status: true }));
        reject(error);
      });
  });
};

export const postFavorites =
  (payload: { object_type: ObjectTypeEnum; object_id: number; bookmark?: string }) =>
  (dispatch: AppThunk) => {
    dispatch(favoritesPostInit());
    return new Promise((resolve, reject) => {
      postFavoriteApi(payload)
        .then((response) => {
          dispatch(favoritesPostSuccess());
          resolve(response.data);
        })
        .catch((error) => {
          dispatch(favoritesPostFailure({ message: error, status: true }));
          reject(error);
        });
    });
  };

export const putFavorites =
  (payload: { object_type: ObjectTypeEnum; object_id: number; bookmark?: string }) =>
  (dispatch: AppThunk) => {
    dispatch(favoritesPutInit());
    return new Promise((resolve, reject) => {
      putFavoriteApi(payload)
        .then((response) => {
          dispatch(favoritesPutSuccess());
          resolve(response.data);
        })
        .catch((error) => {
          dispatch(favoritesPutFailure({ message: error, status: true }));
          reject(error);
        });
    });
  };

export const deleteFavorites =
  (payload: { object_id: number; object_type: ObjectTypeEnum }) => (dispatch: AppThunk) => {
    dispatch(favoritesDeleteInit());
    return new Promise((resolve, reject) => {
      deleteFavoriteApi(payload)
        .then((response) => {
          dispatch(favoritesDeleteSuccess(payload));
          resolve(response.data);
        })
        .catch((error) => {
          dispatch(favoritesDeleteFailure({ message: error, status: true }));
          reject(error);
        });
    });
  };
