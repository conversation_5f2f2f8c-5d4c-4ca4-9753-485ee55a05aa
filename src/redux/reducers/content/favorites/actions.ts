import { createAction } from '@reduxjs/toolkit';

import {
  IFavoriteItemAll,
  IFavoritesChange,
  IFavoritesDelete,
} from '~/redux/reducers/content/favorites/types';
import { IError } from '~/redux/types/common.d';

export const favoritesAllInit = createAction('favorites/all/init');
export const favoritesAllSuccess = createAction<Array<IFavoriteItemAll>>('favorites/all/success');
export const favoritesAllFailure = createAction<IError>('favorites/all/failure');
export const favoritesAllChange = createAction<IFavoritesChange>('favorites/all/change');

export const favoritesPostInit = createAction('favorites/post/init');
export const favoritesPostSuccess = createAction('favorites/post/success');
export const favoritesPostFailure = createAction<IError>('favorites/post/failure');

export const favoritesPutInit = createAction('favorites/put/init');
export const favoritesPutSuccess = createAction('favorites/put/success');
export const favoritesPutFailure = createAction<IError>('favorites/put/failure');

export const favoritesDeleteInit = createAction('favorites/delete/init');
export const favoritesDeleteSuccess = createAction<IFavoritesDelete>('favorites/delete/success');
export const favoritesDeleteFailure = createAction<IError>('favorites/delete/failure');
