import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { IReducer } from '~/redux/types/common';
import { IArticle } from '~/types/article';
import { IBaseBrand } from '~/types/brands';
import { ICoupon } from '~/types/coupon';
import { IDiscount } from '~/types/discount';
import { IInspiration } from '~/types/inspirations';
import { INewspaper } from '~/types/newspaper';

interface IFavoriteItem {
  id: number;
  object_type: string;
  object_id: number;
  bookmark: string;
}

interface IFavoriteRequest {
  count: number;
  next: string | null;
  previous: string | null;
}

//ARTICLE
export interface IFavoriteItemArticle extends IFavoriteItem {
  object: IArticle;
}

export interface IFavoritesArticle extends IFavoriteRequest {
  results: Array<IFavoriteItemArticle>;
}

//BRAND
export interface IFavoriteItemBrand extends IFavoriteItem {
  object: IBaseBrand;
}

export interface IFavoritesBrand extends IFavoriteRequest {
  results: Array<IFavoriteItemBrand>;
}

//CARD
export interface IFavoriteItemCard extends IFavoriteItem {
  object: IDiscount;
}

export interface IFavoritesCard extends IFavoriteRequest {
  results: Array<IFavoriteItemCard>;
}

//COUPON
export interface IFavoriteItemCoupon extends IFavoriteItem {
  object: ICoupon;
}

export interface IFavoritesCoupon extends IFavoriteRequest {
  results: Array<IFavoriteItemCoupon>;
}

//INSPIRATION
export interface IFavoriteItemInspiration extends IFavoriteItem {
  object: IInspiration;
}

export interface IFavoritesInspiration extends IFavoriteRequest {
  results: Array<IFavoriteItemInspiration>;
}

//LEAFLET
export interface IFavoriteItemLeaflet extends IFavoriteItem {
  object: INewspaper;
}

export interface IFavoritesLeaflet extends IFavoriteRequest {
  results: Array<IFavoriteItemLeaflet>;
}

//ALL
// export interface IFavoriteItemAll extends IFavoriteItem {
//   object:
//     | IFavoriteItemBrand
//     | IFavoriteItemArticle
//     | IFavoriteItemCard
//     | IFavoriteItemCoupon
//     | IFavoriteItemInspiration
//     | IFavoriteItemLeaflet;
// }

export interface IFavoriteItemAll extends IFavoriteItem {
  object: IBaseBrand | IArticle | IDiscount | ICoupon | INewspaper | IInspiration;
}
export interface IFavoritesAll extends IFavoriteRequest {
  results: Array<IFavoriteItemAll>;
}

export interface IFavoritesCollection {
  article: IFavoritesArticle;
  brand: IFavoritesBrand;
  card: IFavoritesCard;
  coupon: IFavoritesCoupon;
  inspiration: IFavoritesInspiration;
  leaflet: IFavoritesLeaflet;
}

export interface IFavoritesReducer extends IReducer {
  download: boolean;
  collection: IFavoritesCollection;
}

export interface IFavoritesChange {
  isFavorite: boolean | null;
  objectId: number;
  objectType: ObjectTypeEnum;
  bookmark: null | string;
}

export interface IFavoritesDelete {
  object_id: number;
  object_type: ObjectTypeEnum;
}
