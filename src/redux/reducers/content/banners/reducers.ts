import { createReducer } from '@reduxjs/toolkit';

import {
  bannersFailure,
  bannersInit,
  bannersSuccess,
} from '~/redux/reducers/content/banners/actions';
import { IBannersReducer } from '~/redux/reducers/content/banners/types';

const initialState: IBannersReducer = {
  error: { message: '', status: false },
  loading: false,
  collection: [],
};

export const reducer = createReducer(initialState, (builder) => {
  builder.addCase(bannersInit, (state) => {
    state.loading = true;
  });
  builder.addCase(bannersSuccess, (state, action) => {
    state.loading = false;
    state.error = { message: '', status: false };
    state.collection = action.payload;
  });
  builder.addCase(bannersFailure, (state, action) => {
    state.loading = false;
    state.error = action.payload;
  });
});
