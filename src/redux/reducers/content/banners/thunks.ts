//import { getBannerCollectionA<PERSON> } from '~/redux/api/banners';
import {
  bannersFailure,
  bannersInit,
  bannersSuccess,
} from '~/redux/reducers/content/banners/actions';
import { AppThunk } from '~/redux/store';
import { IBanner } from '~/types/banner';

export const getBanners = () => (dispatch: AppThunk) => {
  dispatch(bannersInit());
  return new Promise((resolve: (value: IBanner[]) => void, reject) => {
    // getBannerCollectionApi()
    //   .then((response) => {
    //     dispatch(bannersSuccess(response.data));
    //     resolve(response.data);
    //   })
    //   .catch((error) => {
    //     dispatch(bannersFailure({ message: error, status: true }));
    //     reject(error);
    //   });
  });
};
