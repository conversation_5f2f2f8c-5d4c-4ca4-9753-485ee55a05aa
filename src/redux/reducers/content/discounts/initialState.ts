import { ShopTypeEnum, ValueTypeEnum } from '~/enums/shared';
import { IDiscountReducer } from '~/redux/reducers/content/discounts/types';

export const initialState: IDiscountReducer = {
  error: { message: '', status: false },
  loading: false,
  cache: {
    detail: {},
  },
  collection: {
    count: 0,
    next: null,
    previous: null,
    results: [],
  },
  filteredDiscounts: {
    error: { message: '', status: false },
    loading: false,
    count: 0,
    next: null,
    previous: null,
    results: [],
  },
  similar: {
    discounts: {
      error: { message: '', status: false },
      loading: false,
      count: 0,
      next: null,
      previous: null,
      results: [],
    },
  },
  detail: {
    error: { message: '', status: false },
    loading: false,
    id: 0,
    name: '',
    valid_from: '',
    valid_to: '',
    bg_color: '',
    logo: '',
    bg_image: '',
    brand: {
      is_favorite: false,
      id: 0,
      name: '',
      logo: '',
    },
    custom_brand_name: '',
    categories: [],
    value_type: ValueTypeEnum.OTHER,
    discount_value: '',
    is_favorite: false,
    tags: [],
    type: ShopTypeEnum.ONLINE,
    discounts: [],
    description: '',
    site: '',
  },
  discountCode: null,
  addresses: [],
  mayInterestYou: [],
};
