import { IReducer } from '~/redux/types/common';
import { IAddress } from '~/types/addresses';
import { IDiscount, IDiscountCode, IDiscountCollections, IDiscountDetail } from '~/types/discount';

export type TDiscountCollection = IDiscountCollections & IReducer;
export type TDiscountDetail = IDiscountDetail & IReducer;

export interface IDiscountDetailCache {
  id: number;
  data: IDiscountDetail;
  timestamp: number;
}

export interface IDiscountReducer extends IReducer {
  collection: IDiscountCollections;
  cache: {
    detail: {
      [key: string]: Omit<IDiscountDetailCache, 'id'>;
    };
  };
  detail: TDiscountDetail;
  similar: {
    discounts: TDiscountCollection;
  };
  filteredDiscounts: TDiscountCollection;
  discountCode: IDiscountCode | null;
  addresses: IAddress[];
  mayInterestYou: IDiscount[];
}

export interface IDiscountPage {
  listType: 'similar' | 'filtered' | 'collection';
  collection: IDiscountCollections;
}
