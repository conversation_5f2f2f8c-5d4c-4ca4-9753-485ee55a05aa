import { createAction } from '@reduxjs/toolkit';

import { IDiscountDetailCache, IDiscountPage } from '~/redux/reducers/content/discounts/types';
import { IError } from '~/redux/types/common.d';
import { IAddress } from '~/types/addresses';
import {
  IDiscount,
  IDiscountCodeAction,
  IDiscountCollections,
  IDiscountDetail,
} from '~/types/discount';
import { IFavoriteReducer } from '~/types/favorite';

export const discountsInit = createAction('discounts/collection/init');
export const discountsSuccess = createAction<IDiscountCollections>('discounts/collection/success');
export const discountsFailure = createAction<IError>('discounts/collection/failure');

export const discountDetailInit = createAction('discounts/detail/init');
export const discountDetailSuccess = createAction<IDiscountDetail>('discounts/detail/success');
export const discountDetailFailure = createAction<IError>('discounts/detail/failure');

export const discountsAddressesInit = createAction('discounts/addresses/init');
export const discountsAddressesSuccess = createAction<IAddress[]>('discounts/addresses/success');
export const discountsAddressesFailure = createAction<IError>('discounts/addresses/failure');

/*
 * Discount filtered
 */
export const discountFilteredInit = createAction('discounts/filtered/init');
export const discountFilteredSuccess = createAction<IDiscountCollections>(
  'discounts/filtered/success',
);
export const discountFilteredFailure = createAction<IError>('discounts/filtered/failure');

/*
 * Similar discounts
 */
export const similarDiscountsInit = createAction('discounts/similar/init');
export const similarDiscountsSuccess = createAction<IDiscountCollections>(
  'discounts/similar/success',
);
export const similarDiscountsFailure = createAction<IError>('discounts/similar/failure');

export const discountCodeInit = createAction('discounts/code/init');
export const discountCodeSuccess = createAction<IDiscountCodeAction>('discounts/code/success');
export const discountCodeFailure = createAction<IError>('discounts/code/failure');
export const discountCodeClear = createAction('discounts/code/clear');

export const discountChangeFavorite = createAction<IFavoriteReducer>('discounts/change/favorite');

/*
 * Search
 * may interest you content
 */
export const discountsMayInterestYou = createAction<IDiscount[]>('discounts/may/interest/you');
export const discountPage = createAction<IDiscountPage>('discounts/page');

/*
 * Discount cache
 */
export const cacheDiscountDetail = createAction<IDiscountDetailCache>('discounts/cache/detail');
