import {
  getDiscountAddress<PERSON>pi,
  getDiscountCode<PERSON>pi,
  // getDiscountCollectionApi,
  // getDiscountDetailApi,
} from '~/redux/api/discounts';
import {
  cacheDiscountDetail,
  discountCodeFailure,
  discountCodeInit,
  discountCodeSuccess,
  discountDetailFailure,
  discountDetailInit,
  discountDetailSuccess,
  discountFilteredFailure,
  discountFilteredInit,
  discountFilteredSuccess,
  discountsAddressesFailure,
  discountsAddressesInit,
  discountsAddressesSuccess,
  discountsFailure,
  discountsInit,
  discountsSuccess,
  similarDiscountsFailure,
  similarDiscountsInit,
  similarDiscountsSuccess,
} from '~/redux/reducers/content/discounts/actions';
import { AppThunk } from '~/redux/store';
import { IAddress } from '~/types/addresses';
import { IDiscountCode, IDiscountCollections } from '~/types/discount';
import { ParamsOptions } from '~/types/shared';
import { createFetchThunk } from '~/utils/thunk.config';

// export const getDiscounts = (payload?: ParamsOptions) => (dispatch: AppThunk) => {
//   dispatch(discountsInit());
//   return new Promise((resolve: (value: IDiscountCollections) => void, reject) => {
//     getDiscountCollectionApi(payload)
//       .then((response) => {
//         dispatch(discountsSuccess(response.data));
//         resolve(response.data);
//       })
//       .catch((error) => {
//         dispatch(discountsFailure({ message: error, status: true }));
//         reject(error);
//       });
//   });
// };
// /*
//  * Similar discounts
//  */
// export const getSimilarDiscounts = (payload: ParamsOptions) => (dispatch: AppThunk) => {
//   dispatch(similarDiscountsInit());
//   return new Promise((resolve: (value: IDiscountCollections) => void, reject) => {
//     getDiscountCollectionApi(payload)
//       .then((response) => {
//         dispatch(similarDiscountsSuccess(response.data));
//         resolve(response.data);
//       })
//       .catch((error) => {
//         dispatch(similarDiscountsFailure({ message: error, status: true }));
//         reject(error);
//       });
//   });
// };
// /*
//  * Filtered discounts
//  */
// export const getFilteredDiscounts = (payload: ParamsOptions) => (dispatch: AppThunk) => {
//   dispatch(discountFilteredInit());
//   return new Promise((resolve: (value: IDiscountCollections) => void, reject) => {
//     getDiscountCollectionApi(payload)
//       .then((response) => {
//         dispatch(discountFilteredSuccess(response.data));
//         resolve(response.data);
//       })
//       .catch((error) => {
//         dispatch(discountFilteredFailure({ message: error, status: true }));
//         reject(error);
//       });
//   });
// };

// export const getDiscountDetail = (payload: { id: number }) =>
//   createFetchThunk(
//     discountDetailInit,
//     discountDetailSuccess,
//     discountDetailFailure,
//     cacheDiscountDetail,
//     () => getDiscountDetailApi(payload),
//     payload,
//     'discounts',
//   );

export const getDiscountCode =
  (payload: { cardId?: number; discountId: number }) => (dispatch: AppThunk) => {
    dispatch(discountCodeInit());
    return new Promise((resolve: (value: IDiscountCode) => void, reject) => {
      getDiscountCodeApi({ id: payload.discountId })
        .then((response) => {
          dispatch(
            discountCodeSuccess({
              cardId: payload.cardId,
              discountId: payload.discountId,
              response: response.data,
            }),
          );
          resolve(response.data);
        })
        .catch((error) => {
          if (error.response.status === 403) {
            dispatch(
              discountCodeFailure({
                message: 'Brak dostępu do zasobu, skontaktuj się z administratorem',
                status: true,
              }),
            );
            reject('Brak dostępu do zasobu, skontaktuj się z administratorem');
          } else if (error.response.status === 404) {
            dispatch(
              discountCodeFailure({
                message: 'Brak kodu premium, skontaktuj się z administratorem',
                status: true,
              }),
            );
            reject('Brak kodu premium, skontaktuj się z administratorem');
          } else {
            dispatch(
              discountCodeFailure({
                message: 'Coś poszło nie tak',
                status: true,
              }),
            );
          }
        });
    });
  };

export const getDiscountAddresses = (payload: { id: number }) => (dispatch: AppThunk) => {
  dispatch(discountsAddressesInit());
  return new Promise((resolve: (value: IAddress[]) => void, reject) => {
    getDiscountAddressApi(payload)
      .then((response) => {
        dispatch(discountsAddressesSuccess(response.data));
        resolve(response.data);
      })
      .catch((error) => {
        dispatch(discountsAddressesFailure({ message: error, status: true }));
        reject(error);
      });
  });
};
