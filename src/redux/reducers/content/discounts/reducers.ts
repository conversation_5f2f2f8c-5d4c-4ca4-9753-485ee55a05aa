import { createReducer } from '@reduxjs/toolkit';

import {
  favorite<PERSON>ache<PERSON>elper,
  favoriteHelper,
  favoriteSingleHelper,
} from '~/redux/helpers/favoriteHelper';
import {
  cacheDiscountDetail,
  discountChangeFavorite,
  discountCodeClear,
  discountCodeFailure,
  discountCodeInit,
  discountCodeSuccess,
  discountDetailFailure,
  discountDetailInit,
  discountDetailSuccess,
  discountFilteredFailure,
  discountFilteredInit,
  discountFilteredSuccess,
  discountPage,
  discountsAddressesFailure,
  discountsAddressesInit,
  discountsAddressesSuccess,
  discountsFailure,
  discountsInit,
  discountsMayInterestYou,
  discountsSuccess,
  similarDiscountsFailure,
  similarDiscountsInit,
  similarDiscountsSuccess,
} from '~/redux/reducers/content/discounts/actions';
import { initialState } from '~/redux/reducers/content/discounts/initialState';

export const reducer = createReducer(initialState, (builder) => {
  //COLLECTION
  builder.addCase(discountsInit, (state) => {
    state.loading = true;
  });
  builder.addCase(discountsSuccess, (state, action) => {
    state.loading = false;
    state.collection = action.payload;
  });
  builder.addCase(discountsFailure, (state, action) => {
    state.loading = false;
    state.error = action.payload;
  });
  //DETAIL
  builder.addCase(discountDetailInit, (state) => {
    state.detail.loading = true;
  });
  builder.addCase(discountDetailSuccess, (state, action) => {
    state.detail.loading = false;
    state.detail.error = { message: '', status: false };
    state.detail = {
      ...state.detail,
      ...action.payload,
    };
  });
  builder.addCase(discountDetailFailure, (state, action) => {
    state.loading = false;
    state.error = action.payload;
  });
  // ADDRESSES
  builder.addCase(discountsAddressesInit, (state) => {
    state.loading = true;
  });
  builder.addCase(discountsAddressesSuccess, (state, action) => {
    state.loading = false;
    state.error = { message: '', status: false };
    state.addresses = action.payload;
  });
  builder.addCase(discountsAddressesFailure, (state, action) => {
    state.loading = false;
    state.error = action.payload;
  });
  //SIMILAR
  builder.addCase(similarDiscountsInit, (state) => {
    state.similar.discounts.loading = true;
  });
  builder.addCase(similarDiscountsSuccess, (state, action) => {
    state.similar.discounts.loading = false;
    state.similar.discounts.error = { message: '', status: false };
    state.similar.discounts = {
      ...state.similar.discounts,
      ...action.payload,
    };
  });
  builder.addCase(similarDiscountsFailure, (state, action) => {
    state.similar.discounts.loading = false;
    state.similar.discounts.error = action.payload;
  });
  /*
   * Discount filtered
   */
  builder.addCase(discountFilteredInit, (state) => {
    state.filteredDiscounts.loading = true;
  });
  builder.addCase(discountFilteredSuccess, (state, action) => {
    state.filteredDiscounts.loading = false;
    state.filteredDiscounts.error = { message: '', status: false };

    state.filteredDiscounts.count = action.payload.count;
    state.filteredDiscounts.next = action.payload.next;
    state.filteredDiscounts.previous = action.payload.previous;
    state.filteredDiscounts.results = action.payload.results;
  });
  builder.addCase(discountFilteredFailure, (state, action) => {
    state.filteredDiscounts.loading = false;
    state.filteredDiscounts.error = action.payload;
  });
  // CODE
  builder.addCase(discountCodeInit, (state) => {
    state.detail.loading = true;
  });
  builder.addCase(discountCodeSuccess, (state, action) => {
    state.detail.loading = false;
    state.error = { message: '', status: false };

    state.detail.discounts = state.detail.discounts.map((discount) => {
      if (discount.id === action.payload.discountId) {
        return {
          ...discount,
          code: action.payload.response.code,
        };
      }
      return discount;
    });
  });
  builder.addCase(discountCodeFailure, (state, action) => {
    state.detail.loading = false;
    state.error = action.payload;
  });
  builder.addCase(discountCodeClear, (state) => {
    state.loading = false;
    state.error = { message: '', status: false };
    state.discountCode = null;
  });

  //FAVORITE
  builder.addCase(discountChangeFavorite, (state, action) => {
    state.collection = {
      ...state.collection,
      results: favoriteHelper({
        state: state.collection.results,
        objectId: action.payload.objectId,
        isFavorite: action.payload.isFavorite,
      }),
    };
    state.filteredDiscounts = {
      ...state.filteredDiscounts,
      results: favoriteHelper({
        state: state.filteredDiscounts.results,
        objectId: action.payload.objectId,
        isFavorite: action.payload.isFavorite,
      }),
    };
    state.similar.discounts = {
      ...state.similar.discounts,
      results: favoriteHelper({
        state: state.similar.discounts.results,
        objectId: action.payload.objectId,
        isFavorite: action.payload.isFavorite,
      }),
    };
    state.mayInterestYou = favoriteHelper({
      state: state.mayInterestYou,
      objectId: action.payload.objectId,
      isFavorite: action.payload.isFavorite,
    });
    state.detail = favoriteSingleHelper({
      state: state.detail,
      objectId: action.payload.objectId,
      isFavorite: action.payload.isFavorite,
    });
    state.cache.detail = favoriteCacheHelper({
      state: state.cache.detail,
      objectId: action.payload.objectId,
      isFavorite: action.payload.isFavorite,
    });
  });
  // PAGINATION
  builder.addCase(discountPage, (state, action) => {
    switch (action.payload.listType) {
      case 'collection':
        state.collection = {
          count: action.payload.collection.count,
          next: action.payload.collection.next,
          previous: action.payload.collection.previous,
          results: [...state.collection.results, ...action.payload.collection.results],
        };
        break;
      case 'filtered':
        state.filteredDiscounts = {
          loading: false,
          error: { message: '', status: false },
          count: action.payload.collection.count,
          next: action.payload.collection.next,
          previous: action.payload.collection.previous,
          results: [...state.filteredDiscounts.results, ...action.payload.collection.results],
        };
        break;
      case 'similar':
        state.similar.discounts = {
          loading: false,
          error: { message: '', status: false },
          count: action.payload.collection.count,
          next: action.payload.collection.next,
          previous: action.payload.collection.previous,
          results: [...state.similar.discounts.results, ...action.payload.collection.results],
        };
        break;
    }
  });

  /*
   * Search
   * may interest you content
   */
  builder.addCase(discountsMayInterestYou, (state, action) => {
    state.mayInterestYou = action.payload;
  });
  /*
   * Discount cache
   */
  builder.addCase(cacheDiscountDetail, (state, action) => {
    return {
      ...state,
      cache: {
        ...state.cache,
        detail: {
          ...state.cache.detail,
          [action.payload.id]: {
            data: action.payload.data,
            timestamp: action.payload.timestamp,
          },
        },
      },
    };
  });
});
