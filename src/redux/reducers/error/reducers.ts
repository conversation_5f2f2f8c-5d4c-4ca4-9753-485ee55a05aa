import { createReducer } from '@reduxjs/toolkit';

import { errorClear, errorInit } from './actions';
import { ErrorState } from './types';

const initialState: ErrorState = {
  message: '',
  status: false,
};

export const errorReducer = createReducer(initialState, (builder) => {
  builder.addCase(errorInit, (state, action) => {
    state.message = action.payload.message;
    state.status = true;
  });
  builder.addCase(errorClear, (state) => {
    state.message = '';
    state.status = false;
  });
});
