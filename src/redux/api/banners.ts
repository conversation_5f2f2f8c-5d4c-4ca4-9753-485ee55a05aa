import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

import { SERVER_URL } from '~/config.api';
import { IBanner } from '~/types/banner';
//import axios from '~/utils/axios.config';

// export const getBannerCollectionApi = () => {
//   return axios<IBanner[]>({
//     method: 'GET',
//     url: `${SERVER_URL}/content/banners/`,
//     headers: {
//       Accept: 'application/json',
//     },
//   });
// };

export const api = createApi({
  reducerPath: 'api/content/banners',
  baseQuery: fetchBaseQuery({ baseUrl: `${SERVER_URL}/content/banners/` }),
  endpoints: (builder) => ({
    getBanners: builder.query<IBanner[], void>({
      query: () => {
        return {
          url: '',
          method: 'GET',
          headers: {
            Accept: 'application/json',
          },
        };
      },
    }),
  }),
});

export const { useGetBannersQuery } = api;
