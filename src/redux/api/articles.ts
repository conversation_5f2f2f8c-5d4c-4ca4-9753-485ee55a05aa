import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import _isEqual from 'lodash/isEqual';

import { POSTFavoriteParam } from '../types/common';

import { contentApi } from './contentApi';

import { SERVER_URL } from '~/config.api';
import { axiosParamsSerializer, buildPrams } from '~/helpers/buildApiParam';
import { IArticleCollections, IArticleDetails } from '~/types/article';
import { ParamsOptions } from '~/types/shared';
// import axios from '~/utils/axios.config';

// export const getArticleCollectionApi = (payload?: ParamsOptions) => {
//   const params: ParamsOptions = payload ? buildPrams(payload) : {};
//   return axios<IArticleCollections>({
//     method: 'GET',
//     url: `${SERVER_URL}/content/articles/`,
//     headers: {
//       'Content-Type': 'application/json',
//     },
//     params,
//     paramsSerializer: axiosParamsSerializer,
//   });
// };

// export const getArticleDetailApi = (payload: { id: number }) => {
//   return axios<IArticleDetails>({
//     method: 'GET',
//     url: `${SERVER_URL}/content/articles/${payload.id}/`,
//     headers: {
//       Accept: 'application/json',
//     },
//   });
// };

// export const api = createApi({
//   reducerPath: 'api/content/articles',
//   baseQuery: fetchBaseQuery({ baseUrl: `${SERVER_URL}/content/articles/` }),
//   endpoints: (builder) => ({
//     getArticles: builder.query<IArticleCollections, void>({
//       query: () => {
//         return {
//           url: '',
//           method: 'GET',
//           headers: {
//             Accept: 'application/json',
//           },
//         };
//       },
//     }),
//   }),
// });

export type GETArticlesParams = ParamsOptions;
export interface WithTokenParams extends GETArticlesParams {
  token?: string | null;
}
//type Param = {
const api = contentApi.injectEndpoints({
  endpoints: (builder) => ({
    getArticles: builder.query<IArticleCollections, WithTokenParams>({
      query: (params) => {
        let headers: any;
        if (params.token) {
          headers = {
            Authorization: `Bearer ${params.token}`,
          };
        }
        return {
          url: 'content/articles/',
          method: 'GET',
          headers,
          params,
        };
      },
      providesTags: ['Article'],
    }),

    getSimilarArticles: builder.query<IArticleCollections, GETArticlesParams>({
      query: (params: GETArticlesParams) => {
        return {
          url: 'content/articles/',
          method: 'GET',
          params,
        };
      },
      providesTags: ['Article'],
    }),

    getArticlesWithPagination: builder.query<IArticleCollections, GETArticlesParams>({
      query: (params: GETArticlesParams) => {
        return {
          url: 'content/articles/',
          method: 'GET',
          params,
        };
      },

      serializeQueryArgs: ({ endpointName }) => {
        return endpointName;
      },

      merge: (currentCache, newItems, { arg }) => {
        if (arg?.page) {
          currentCache.next = newItems.next;
          currentCache.previous = newItems.previous;
          currentCache.count = newItems.count;
          currentCache.results.push(...newItems.results);
        } else {
          currentCache.next = newItems.next;
          currentCache.previous = newItems.previous;
          currentCache.count = newItems.count;
          currentCache.results = newItems.results;
        }
      },

      forceRefetch({ currentArg, previousArg }) {
        return !_isEqual(currentArg, previousArg);
      },
      providesTags: ['Article'],
    }),

    getArticlesDetails: builder.query<IArticleDetails, { id: number }>({
      query: (params) => {
        return {
          url: `content/articles/${params.id}/`,
          method: 'GET',
        };
      },
      providesTags: ['Article'],
    }),

    //!FAVORITES
    postArticleFavorite: builder.mutation<void, POSTFavoriteParam>({
      query: (params: POSTFavoriteParam) => ({
        url: `content/favorites/add/`,
        method: 'POST',
        body: params.payload,
      }),

      invalidatesTags: ['Favorite'],

      async onQueryStarted({ payload }, { dispatch, queryFulfilled, getState }) {
        for (const { endpointName, originalArgs } of api.util.selectInvalidatedBy(getState(), [
          { type: 'Article' },
        ])) {
          if (
            endpointName !== 'getArticles' &&
            endpointName !== 'getArticlesDetails' &&
            endpointName !== 'getArticlesWithPagination' &&
            endpointName !== 'getSimilarArticles'
          )
            continue;
          const patchResult = dispatch(
            api.util.updateQueryData(endpointName, originalArgs, (draft) => {
              if (draft) {
                if ('results' in draft) {
                  const index = draft.results.findIndex((item) => item.id === payload.object_id);
                  if (index !== -1) {
                    draft.results[index].is_favorite = true;
                  }
                } else if ('id' in draft) {
                  draft.is_favorite = true;
                }
              }
            }),
          );
          try {
            await queryFulfilled;
          } catch {
            patchResult.undo();
          }
        }
      },
    }),

    putArticleFavorite: builder.mutation<void, POSTFavoriteParam>({
      query: (params: POSTFavoriteParam) => ({
        url: `content/favorites/update/${params.payload.object_id}/`,
        method: 'PUT',
        params: {
          object_type: params.payload.object_type,
        },
        body: {
          bookmark: params.payload.bookmark,
        },
      }),

      invalidatesTags: ['Favorite'],
    }),

    deleteArticleFavorite: builder.mutation<void, POSTFavoriteParam>({
      query: (params: POSTFavoriteParam) => ({
        url: `content/favorites/${params.payload.object_id}/`,
        method: 'DELETE',
        params: {
          object_type: params.payload.object_type,
        },
      }),

      invalidatesTags: ['Favorite'],

      async onQueryStarted({ payload }, { dispatch, queryFulfilled, getState }) {
        for (const { endpointName, originalArgs } of api.util.selectInvalidatedBy(getState(), [
          { type: 'Article' },
        ])) {
          if (
            endpointName !== 'getArticles' &&
            endpointName !== 'getArticlesDetails' &&
            endpointName !== 'getArticlesWithPagination' &&
            endpointName !== 'getSimilarArticles'
          )
            continue;
          const patchResult = dispatch(
            api.util.updateQueryData(endpointName, originalArgs, (draft) => {
              if (draft) {
                if ('results' in draft) {
                  const index = draft.results.findIndex((item) => item.id === payload.object_id);
                  if (index !== -1) {
                    draft.results[index].is_favorite = false;
                  }
                } else if ('id' in draft) {
                  draft.is_favorite = false;
                }
              }
            }),
          );
          try {
            await queryFulfilled;
          } catch {
            patchResult.undo();
          }
        }
      },
    }),
  }),
});

export const {
  useGetArticlesQuery,
  useLazyGetArticlesQuery,
  useGetSimilarArticlesQuery,
  useGetArticlesDetailsQuery,
  useLazyGetArticlesDetailsQuery,
  useGetArticlesWithPaginationQuery,
  usePostArticleFavoriteMutation,
  usePutArticleFavoriteMutation,
  useDeleteArticleFavoriteMutation,
} = api;
