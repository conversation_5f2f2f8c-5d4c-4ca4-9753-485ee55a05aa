import { IFavoriteItemAll } from '../reducers/content/favorites/types';

import { contentApi } from './contentApi';

import { SERVER_URL } from '~/config.api';
import { BookmarkEnum } from '~/enums/bookmarkEnum';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { axiosParamsSerializer, buildPrams } from '~/helpers/buildApiParam';
import { ParamsOptions } from '~/types/shared';
import axios from '~/utils/axios.config';

export const getFavoriteCollectionApi = (payload?: ParamsOptions) => {
  const params: ParamsOptions = payload ? buildPrams(payload) : {};
  return axios<any>({
    method: 'GET',
    url: `${SERVER_URL}/content/favorites/`,
    headers: {
      'Content-Type': 'application/json',
    },
    params,
    paramsSerializer: axiosParamsSerializer,
  });
};

export const postFavoriteApi = (payload: {
  object_type: ObjectTypeEnum;
  object_id: number;
  bookmark?: string;
}) => {
  return axios({
    method: 'POST',
    url: `${SERVER_URL}/content/favorites/add/`,
    headers: {
      'Content-Type': 'application/json',
    },
    data: payload,
  });
};

export const putFavoriteApi = (payload: {
  object_type: ObjectTypeEnum;
  object_id: number;
  bookmark?: string;
}) => {
  return axios({
    method: 'PATCH',
    url: `${SERVER_URL}/content/favorites/update/${payload.object_id}/`,
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      object_type: payload.object_type,
    },
    paramsSerializer: axiosParamsSerializer,
    data: {
      bookmark: payload.bookmark,
    },
  });
};

export const deleteFavoriteApi = (payload: { object_type: ObjectTypeEnum; object_id: number }) => {
  const params: ParamsOptions = payload ? buildPrams(payload) : {};
  return axios({
    method: 'DELETE',
    url: `${SERVER_URL}/content/favorites/${payload.object_id}/`,
    headers: {
      'Content-Type': 'application/json',
    },
    params,
    paramsSerializer: axiosParamsSerializer,
  });
};

//! NEW FAVORITES API
export interface GETFavoritesParams {
  bookmark?: typeof BookmarkEnum | null;
  object_type?: ObjectTypeEnum;
}

const api = contentApi.injectEndpoints({
  endpoints: (builder) => ({
    getFavorites: builder.query<Array<IFavoriteItemAll>, GETFavoritesParams>({
      query: (params: GETFavoritesParams) => {
        return {
          url: 'content/favorites/',
          method: 'GET',
          params,
        };
      },
      transformResponse: (
        data: Array<IFavoriteItemAll>,
      ): IFavoriteItemAll[] | Promise<IFavoriteItemAll[]> => {
        return data.filter((item) => Object.keys(item.object).length !== 0);
      },

      providesTags: ['Favorite'],
    }),
  }),
});
export const { useGetFavoritesQuery } = api;
