import { SERVER_URL } from '~/config.api';
import { ITopicAction } from '~/redux/reducers/info/contact/types';
import { IContactInfo } from '~/types/contact';
import axios from '~/utils/axios.config';

export const getInfoApi = () => {
  return axios<IContactInfo>({
    method: 'GET',
    url: `${SERVER_URL}/info/contact-info/`,
    headers: {
      Accept: 'application/json',
    },
  });
};

export const getTopicCollectionApi = () => {
  return axios<ITopicAction>({
    method: 'GET',
    url: `${SERVER_URL}/info/contact-topics/`,
    headers: {
      Accept: 'application/json',
    },
  });
};
