import _isEqual from 'lodash/isEqual';

import { POSTFavoriteParam } from '../types/common';

import { contentApi } from './contentApi';

import { SERVER_URL } from '~/config.api';
import { axiosParamsSerializer, buildPrams } from '~/helpers/buildApiParam';
import { INewspaperCollections, INewspaperDetail } from '~/types/newspaper';
import { ParamsOptions } from '~/types/shared';
import axios from '~/utils/axios.config';

export const getNewspaperCollectionApi = (payload?: ParamsOptions) => {
  const params: ParamsOptions = payload ? buildPrams(payload) : {};
  return axios<INewspaperCollections>({
    method: 'GET',
    url: `${SERVER_URL}/content/leaflets/`,
    headers: {
      Accept: 'application/json',
    },
    params,
    paramsSerializer: axiosParamsSerializer,
  });
};
export const getNewspaperDetailApi = (payload: { id: number }) => {
  return axios<INewspaperDetail>({
    method: 'GET',
    url: `${SERVER_URL}/content/leaflets/${payload.id}/`,
    headers: {
      Accept: 'application/json',
    },
  });
};

export type GETNewspaperParams = ParamsOptions;
export interface WithTokenParams extends GETNewspaperParams {
  token?: string | null;
}
const api = contentApi.injectEndpoints({
  endpoints: (builder) => ({
    getLeaflets: builder.query<INewspaperCollections, WithTokenParams>({
      query: (params) => {
        let headers: any;
        if (params.token) {
          headers = {
            Authorization: `Bearer ${params.token}`,
          };
        }
        return {
          url: 'content/leaflets/',
          method: 'GET',
          headers,
          params,
        };
      },
      providesTags: ['Leaflet'],
    }),

    getSimilarLeaflets: builder.query<INewspaperCollections, GETNewspaperParams>({
      query: (params: GETNewspaperParams) => {
        return {
          url: 'content/leaflets/',
          method: 'GET',
          params,
        };
      },
      providesTags: ['Leaflet'],
    }),

    getLeafletsWithPagination: builder.query<INewspaperCollections, GETNewspaperParams>({
      query: (params) => {
        return {
          url: 'content/leaflets/',
          method: 'GET',
          params,
        };
      },

      serializeQueryArgs: ({ endpointName }) => {
        return endpointName;
      },

      merge: (currentCache, newItems, { arg }) => {
        if (arg?.page) {
          currentCache.next = newItems.next;
          currentCache.previous = newItems.previous;
          currentCache.count = newItems.count;
          currentCache.results.push(...newItems.results);
        } else {
          currentCache.next = newItems.next;
          currentCache.previous = newItems.previous;
          currentCache.count = newItems.count;
          currentCache.results = newItems.results;
        }
      },

      forceRefetch({ currentArg, previousArg }) {
        return !_isEqual(currentArg, previousArg);
      },
      providesTags: ['Leaflet'],
    }),

    getLeafletsDetails: builder.query<INewspaperDetail, { id: number }>({
      query: (params) => {
        return {
          url: `content/leaflets/${params.id}/`,
          method: 'GET',
        };
      },
      providesTags: ['Leaflet'],
    }),

    //!FAVORITES
    postLeafletFavorite: builder.mutation<void, POSTFavoriteParam>({
      query: (params: POSTFavoriteParam) => ({
        url: `content/favorites/add/`,
        method: 'POST',
        body: params.payload,
      }),

      invalidatesTags: ['Favorite'],

      async onQueryStarted({ payload }, { dispatch, queryFulfilled, getState }) {
        for (const { endpointName, originalArgs } of api.util.selectInvalidatedBy(getState(), [
          { type: 'Leaflet' },
        ])) {
          if (
            endpointName !== 'getLeaflets' &&
            endpointName !== 'getLeafletsDetails' &&
            endpointName !== 'getLeafletsWithPagination' &&
            endpointName !== 'getSimilarLeaflets'
          )
            continue;
          const patchResult = dispatch(
            api.util.updateQueryData(endpointName, originalArgs, (draft) => {
              if (draft) {
                if ('results' in draft) {
                  const index = draft.results.findIndex((item) => item.id === payload.object_id);
                  if (index !== -1) {
                    draft.results[index].is_favorite = true;
                  }
                } else if ('id' in draft) {
                  draft.is_favorite = true;
                }
              }
            }),
          );
          try {
            await queryFulfilled;
          } catch {
            patchResult.undo();
          }
        }
      },
    }),

    putLeafletFavorite: builder.mutation<void, POSTFavoriteParam>({
      query: (params: POSTFavoriteParam) => ({
        url: `content/favorites/update/${params.payload.object_id}/`,
        method: 'PUT',
        params: {
          object_type: params.payload.object_type,
        },
        body: {
          bookmark: params.payload.bookmark,
        },
      }),

      invalidatesTags: ['Favorite'],
    }),

    deleteLeafletFavorite: builder.mutation<void, POSTFavoriteParam>({
      query: (params: POSTFavoriteParam) => ({
        url: `content/favorites/${params.payload.object_id}/`,
        method: 'DELETE',
        params: {
          object_type: params.payload.object_type,
        },
      }),

      invalidatesTags: ['Favorite'],

      async onQueryStarted({ payload }, { dispatch, queryFulfilled, getState }) {
        for (const { endpointName, originalArgs } of api.util.selectInvalidatedBy(getState(), [
          { type: 'Leaflet' },
        ])) {
          if (
            endpointName !== 'getLeaflets' &&
            endpointName !== 'getLeafletsDetails' &&
            endpointName !== 'getLeafletsWithPagination' &&
            endpointName !== 'getSimilarLeaflets'
          )
            continue;
          const patchResult = dispatch(
            api.util.updateQueryData(endpointName, originalArgs, (draft) => {
              if (draft) {
                if ('results' in draft) {
                  const index = draft.results.findIndex((item) => item.id === payload.object_id);
                  if (index !== -1) {
                    draft.results[index].is_favorite = false;
                  }
                } else if ('id' in draft) {
                  draft.is_favorite = false;
                }
              }
            }),
          );
          try {
            await queryFulfilled;
          } catch {
            patchResult.undo();
          }
        }
      },
    }),
  }),
});

export const {
  useDeleteLeafletFavoriteMutation,
  useGetLeafletsQuery,
  useLazyGetSimilarLeafletsQuery,
  useGetSimilarLeafletsQuery,
  useGetLeafletsDetailsQuery,
  useGetLeafletsWithPaginationQuery,
  usePostLeafletFavoriteMutation,
  useLazyGetLeafletsDetailsQuery,
  usePutLeafletFavoriteMutation,
  useLazyGetLeafletsQuery,
  useLazyGetLeafletsWithPaginationQuery,
} = api;
