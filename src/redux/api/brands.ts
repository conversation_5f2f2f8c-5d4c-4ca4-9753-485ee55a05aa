import _isEqual from 'lodash/isEqual';

import { POSTFavoriteParam } from '../types/common';

import { contentApi } from './contentApi';

import { SERVER_URL } from '~/config.api';
import { axiosParamsSerializer, buildPrams } from '~/helpers/buildApiParam';
import { IAddress } from '~/types/addresses';
import { IBrandCollections, IBrandDetail } from '~/types/brands';
import { ParamsOptions } from '~/types/shared';
import axios from '~/utils/axios.config';

export const getBrandCollectionApi = (payload?: ParamsOptions) => {
  const params: ParamsOptions = payload ? buildPrams(payload) : {};
  return axios<IBrandCollections>({
    method: 'GET',
    url: `${SERVER_URL}/content/brands/`,
    headers: {
      Accept: 'application/json',
    },
    params,
    paramsSerializer: axiosParamsSerializer,
  });
};
export const getBrandDetailApi = (payload: { id: number }) => {
  return axios<IBrandDetail>({
    method: 'GET',
    url: `${SERVER_URL}/content/brands/${payload.id}/`,
    headers: {
      Accept: 'application/json',
    },
  });
};
export const getBrandAddressApi = (payload: { id: number }) => {
  return axios<IAddress[]>({
    method: 'GET',
    url: `${SERVER_URL}/content/brands/${payload.id}/addresses/`,
    headers: {
      Accept: 'application/json',
    },
  });
};

export type GETBrandParams = ParamsOptions;
export interface WithTokenParams extends GETBrandParams {
  token?: string | null;
}

const api = contentApi.injectEndpoints({
  endpoints: (builder) => ({
    getBrands: builder.query<IBrandCollections, WithTokenParams>({
      query: (params: WithTokenParams) => {
        let headers: any;
        if (params.token) {
          headers = {
            Authorization: `Bearer ${params.token}`,
          };
        }

        return {
          url: 'content/brands/',
          method: 'GET',
          headers,
          params,
        };
      },
      providesTags: ['Brand'],
    }),

    getBrandsWithPagination: builder.query<IBrandCollections, GETBrandParams>({
      query: (params: GETBrandParams) => {
        return {
          url: 'content/brands/',
          method: 'GET',
          params,
        };
      },

      serializeQueryArgs: ({ endpointName }) => {
        return endpointName;
      },

      merge: (currentCache, newItems, { arg }) => {
        if (arg?.page) {
          currentCache.next = newItems.next;
          currentCache.previous = newItems.previous;
          currentCache.count = newItems.count;
          currentCache.results.push(...newItems.results);
        } else {
          currentCache.next = newItems.next;
          currentCache.previous = newItems.previous;
          currentCache.count = newItems.count;
          currentCache.results = newItems.results;
        }
      },

      forceRefetch({ currentArg, previousArg }) {
        return !_isEqual(currentArg, previousArg);
      },
      providesTags: ['Brand'],
    }),

    getBrandsDetails: builder.query<IBrandDetail, { id: number }>({
      query: (params) => {
        return {
          url: `content/brands/${params.id}/`,
          method: 'GET',
        };
      },
      providesTags: ['Brand'],
    }),

    //!FAVORITES
    postBrandFavorite: builder.mutation<void, POSTFavoriteParam>({
      query: (params: POSTFavoriteParam) => ({
        url: `content/favorites/add/`,
        method: 'POST',
        body: params.payload,
      }),

      invalidatesTags: ['Favorite'],

      async onQueryStarted({ payload }, { dispatch, queryFulfilled, getState }) {
        for (const { endpointName, originalArgs } of api.util.selectInvalidatedBy(getState(), [
          { type: 'Brand' },
        ])) {
          if (
            endpointName !== 'getBrands' &&
            endpointName !== 'getBrandsDetails' &&
            endpointName !== 'getBrandsWithPagination'
          )
            continue;
          const patchResult = dispatch(
            api.util.updateQueryData(endpointName, originalArgs, (draft) => {
              if (draft) {
                if ('results' in draft) {
                  const index = draft.results.findIndex((item) => item.id === payload.object_id);
                  if (index !== -1) {
                    draft.results[index].is_favorite = true;
                  }
                } else if ('id' in draft) {
                  draft.is_favorite = true;
                }
              }
            }),
          );
          try {
            await queryFulfilled;
          } catch {
            patchResult.undo();
          }
        }
      },
    }),

    putBrandFavorite: builder.mutation<void, POSTFavoriteParam>({
      query: (params: POSTFavoriteParam) => ({
        url: `content/favorites/update/${params.payload.object_id}/`,
        method: 'PUT',
        params: {
          object_type: params.payload.object_type,
        },
        body: {
          bookmark: params.payload.bookmark,
        },
      }),

      invalidatesTags: ['Favorite'],
    }),

    deleteBrandFavorite: builder.mutation<void, POSTFavoriteParam>({
      query: (params: POSTFavoriteParam) => ({
        url: `content/favorites/${params.payload.object_id}/`,
        method: 'DELETE',
        params: {
          object_type: params.payload.object_type,
        },
      }),

      invalidatesTags: ['Favorite'],

      async onQueryStarted({ payload }, { dispatch, queryFulfilled, getState }) {
        for (const { endpointName, originalArgs } of api.util.selectInvalidatedBy(getState(), [
          { type: 'Brand' },
        ])) {
          if (
            endpointName !== 'getBrands' &&
            endpointName !== 'getBrandsDetails' &&
            endpointName !== 'getBrandsWithPagination'
          )
            continue;
          const patchResult = dispatch(
            api.util.updateQueryData(endpointName, originalArgs, (draft) => {
              if (draft) {
                if ('results' in draft) {
                  const index = draft.results.findIndex((item) => item.id === payload.object_id);
                  if (index !== -1) {
                    draft.results[index].is_favorite = false;
                  }
                } else if ('id' in draft) {
                  draft.is_favorite = false;
                }
              }
            }),
          );
          try {
            await queryFulfilled;
          } catch {
            patchResult.undo();
          }
        }
      },
    }),
  }),
});

export const {
  useGetBrandsQuery,
  useLazyGetBrandsQuery,
  useGetBrandsDetailsQuery,
  useLazyGetBrandsDetailsQuery,
  useGetBrandsWithPaginationQuery,
  usePostBrandFavoriteMutation,
  usePutBrandFavoriteMutation,
  useDeleteBrandFavoriteMutation,
} = api;
