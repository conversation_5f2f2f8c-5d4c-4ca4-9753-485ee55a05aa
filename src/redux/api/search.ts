import { SERVER_URL } from '~/config.api';
import { axiosParamsSerializer, buildPrams } from '~/helpers/buildApiParam';
import { ISearchResultData } from '~/types/search';
import { ParamsOptions } from '~/types/shared';
import axios from '~/utils/axios.config';

export const getSearchCollectionApi = (payload?: ParamsOptions) => {
  const params: ParamsOptions = payload ? buildPrams(payload) : {};
  return axios<ISearchResultData[]>({
    method: 'GET',
    url: `${SERVER_URL}/content/search/`,
    headers: {
      Accept: 'application/json',
    },
    params,
    paramsSerializer: axiosParamsSerializer,
  });
};

export const getPopularCollectionApi = () => {
  return axios<ISearchResultData[]>({
    method: 'GET',
    url: `${SERVER_URL}/content/popular/`,
    headers: {
      Accept: 'application/json',
    },
  });
};
