import { SERVER_URL } from '~/config.api';
import { INotificationCollections } from '~/types/notification';
import axios from '~/utils/axios.config';

export const getNotificationCollectionApi = () => {
  return axios<INotificationCollections>({
    method: 'GET',
    url: `${SERVER_URL}/content/notifications/`,
    headers: {
      Accept: 'application/json',
    },
  });
};

export const pushDeviceToken = (payload: { token: string }) => {
  return axios({
    method: 'POST',
    url: `${SERVER_URL}/content/pushtoken/`,
    headers: {
      Accept: 'application/json',
    },
    data: payload,
  });
};

export const deleteDeviceToken = (payload: { email: string; token: string }) => {
  return axios({
    method: 'DELETE',
    url: `${SERVER_URL}/content/pushtoken/${payload.email}/${payload.token}/`,
    headers: {
      Accept: 'application/json',
    },
  });
};
