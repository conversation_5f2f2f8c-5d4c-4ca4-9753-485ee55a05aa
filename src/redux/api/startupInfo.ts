import _isEqual from 'lodash/isEqual';
import { contentApi } from './contentApi';
import { IStartupInfo } from '~/types/startupInfo';

const api = contentApi.injectEndpoints({
  endpoints: (builder) => ({
    getStartupInfo: builder.query<Array<IStartupInfo>, void>({
      query: () => {
        return {
          url: 'content/startupinfo/',
          method: 'GET',
        };
      },
      providesTags: ['StartupInfo'],
    }),
  }),
});
export const { useGetStartupInfoQuery, useLazyGetStartupInfoQuery } = api;
