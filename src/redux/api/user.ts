import { SERVER_URL } from '~/config.api';
import {
  IDetail,
  IMe,
  IMeConsents,
  IPremium,
  IToken,
  IUserSocialResponse,
  UserCreate,
} from '~/types/user';
import axios from '~/utils/axios.config';

export const getTokenApi = (payload: { email: string; password: string }) => {
  return axios<IToken>({
    method: 'POST',
    url: `${SERVER_URL}/user/token/`,
    headers: {
      Accept: 'application/json',
    },
    data: {
      email: payload.email.toLowerCase(),
      password: payload.password,
    },
  });
};

export const postUserCreate = (payload: UserCreate) => {
  try {
    return axios<{ email: string; is_verified: boolean }>({
      method: 'POST',
      url: `${SERVER_URL}/user/create/`,
      headers: {
        Accept: 'application/json',
      },
      data: payload,
    });
  } catch (err) {
    console.log('postUserCreate', err);
  }
};
export const getRefreshTokenApi = (payload: { refresh: string }) => {
  return axios<IToken>({
    method: 'POST',
    url: `${SERVER_URL}/user/token/refresh/`,
    headers: {
      Accept: 'application/json',
    },
    data: {
      refresh: payload.refresh,
    },
  });
};
export const getProfileApi = (payload: { token: string }) => {
  return axios<IDetail>({
    method: 'GET',
    url: `${SERVER_URL}/user/me/profile/`,
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${payload.token}`,
    },
  });
};
export const getPremiumApi = (payload: { token: string }) => {
  return axios<IPremium>({
    method: 'GET',
    url: `${SERVER_URL}/content/premium/`,
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${payload.token}`,
    },
  });
};
export const getMeApi = (payload: { token: string }) => {
  return axios<IMe>({
    method: 'GET',
    url: `${SERVER_URL}/user/me/`,
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${payload.token}`,
    },
  });
};
export const getMeConsents = (payload: { token: string }) => {
  return axios<IMeConsents>({
    method: 'GET',
    url: `${SERVER_URL}/user/me/consents/`,
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${payload.token}`,
    },
  });
};
export const getSocialAuth = (payload: { token: string }) => {
  return axios<IUserSocialResponse>({
    method: 'POST',
    url: `${SERVER_URL}/social-auth/firebase/`,
    headers: {
      Accept: 'application/json',
    },
    data: {
      token: payload.token,
    },
  });
};
