import { contentApi } from './contentApi';

import { SERVER_URL } from '~/config.api';
import { axiosParamsSerializer, buildPrams } from '~/helpers/buildApiParam';
import { IParentCategory } from '~/types/category';
import { ParamsOptions } from '~/types/shared';
import axios from '~/utils/axios.config';

export const getCategoryCollectionApi = (payload?: ParamsOptions) => {
  const params: ParamsOptions = payload ? buildPrams(payload) : {};
  return axios<IParentCategory[]>({
    method: 'GET',
    url: `${SERVER_URL}/content/categories/`,
    headers: {
      Accept: 'application/json',
    },
    params,
    paramsSerializer: axiosParamsSerializer,
  });
};

const api = contentApi.injectEndpoints({
  endpoints: (builder) => ({
    getCategories: builder.query<IParentCategory[], ParamsOptions>({
      query: (params: ParamsOptions) => {
        return {
          url: 'content/categories/',
          method: 'GET',
          params,
        };
      },
    }),
  }),
});

export const { useLazyGetCategoriesQuery } = api;
