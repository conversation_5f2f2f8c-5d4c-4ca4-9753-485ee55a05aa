import { SERVER_URL } from '~/config.api';
import { IConsentDetails, ILegal } from '~/types/privacy';
import axios from '~/utils/axios.config';

export const getConsentsCollectionApi = () => {
  return axios<Array<IConsentDetails>>({
    method: 'GET',
    url: `${SERVER_URL}/user/consents/`,
    headers: {
      Accept: 'application/json',
    },
  });
};

export const getLegalCollectionApi = (payload: { id: number }) => {
  return axios<ILegal>({
    method: 'GET',
    url: `${SERVER_URL}/user/legal/${payload.id}/`,
    headers: {
      Accept: 'application/json',
    },
  });
};
