import _isEqual from 'lodash/isEqual';

import { POSTFavoriteParam } from '../types/common';

import { contentApi } from './contentApi';

import { SERVER_URL } from '~/config.api';
import { axiosParamsSerializer, buildPrams } from '~/helpers/buildApiParam';
import { IBrandCollections, IBrandDetail } from '~/types/brands';
import { ICouponCollections, ICouponDetail } from '~/types/coupon';
import { ParamsOptions } from '~/types/shared';
import axios from '~/utils/axios.config';

export const getCouponCollectionApi = (payload?: ParamsOptions) => {
  const params: ParamsOptions = payload ? buildPrams(payload) : {};
  return axios<ICouponCollections>({
    method: 'GET',
    url: `${SERVER_URL}/content/coupons/`,
    headers: {
      Accept: 'application/json',
    },
    params,
    paramsSerializer: axiosParamsSerializer,
  });
};
export const getCouponDetailApi = (payload: { id: number }) => {
  return axios<ICouponDetail>({
    method: 'GET',
    url: `${SERVER_URL}/content/coupons/${payload.id}/`,
    headers: {
      Accept: 'application/json',
    },
  });
};

// export const api = createApi({
//   reducerPath: 'api/content/coupons',
//   baseQuery: fetchBaseQuery({ baseUrl: `${SERVER_URL}/content/coupons/` }),
//   endpoints: (builder) => ({
//     getCoupons: builder.query<ICouponCollections, ParamsOptions>({
//       query: (params) => {
//         return {
//           url: '',
//           method: 'GET',
//           headers: {
//             Accept: 'application/json',
//           },
//           params,
//         };
//       },
//     }),
//   }),
// });

export type GETCouponsParams = ParamsOptions;
export interface WithTokenParams extends GETCouponsParams {
  token?: string | null;
}
//type Param = {
const api = contentApi.injectEndpoints({
  endpoints: (builder) => ({
    getCoupons: builder.query<ICouponCollections, WithTokenParams>({
      query: (params: WithTokenParams) => {
        let headers: any;
        if (params.token) {
          headers = {
            Authorization: `Bearer ${params.token}`,
          };
        }

        return {
          url: 'content/coupons/',
          method: 'GET',
          params,
          headers,
        };
      },
      providesTags: ['Coupon'],
    }),

    getSimilarCoupons: builder.query<ICouponCollections, GETCouponsParams>({
      query: (params: GETCouponsParams) => {
        return {
          url: 'content/coupons/',
          method: 'GET',
          params,
        };
      },
      providesTags: ['Coupon'],
    }),

    getCouponsWithPagination: builder.query<ICouponCollections, GETCouponsParams>({
      query: (params: GETCouponsParams) => {
        return {
          url: 'content/coupons/',
          method: 'GET',
          params,
        };
      },

      serializeQueryArgs: ({ endpointName }) => {
        return endpointName;
      },

      merge: (currentCache, newItems, { arg }) => {
        if (arg?.page) {
          currentCache.next = newItems.next;
          currentCache.previous = newItems.previous;
          currentCache.count = newItems.count;
          currentCache.results.push(...newItems.results);
        } else {
          currentCache.next = newItems.next;
          currentCache.previous = newItems.previous;
          currentCache.count = newItems.count;
          currentCache.results = newItems.results;
        }
      },

      forceRefetch({ currentArg, previousArg }) {
        return !_isEqual(currentArg, previousArg);
      },
      providesTags: ['Coupon'],
    }),

    getCouponsDetails: builder.query<ICouponDetail, { id: number }>({
      query: (params) => {
        return {
          url: `content/coupons/${params.id}/`,
          method: 'GET',
        };
      },
      providesTags: ['Coupon'],
    }),

    //!FAVORITES
    postCouponFavorite: builder.mutation<void, POSTFavoriteParam>({
      query: (params: POSTFavoriteParam) => ({
        url: `content/favorites/add/`,
        method: 'POST',
        body: params.payload,
      }),

      invalidatesTags: ['Favorite'],

      async onQueryStarted({ payload }, { dispatch, queryFulfilled, getState }) {
        for (const { endpointName, originalArgs } of api.util.selectInvalidatedBy(getState(), [
          { type: 'Coupon' },
        ])) {
          if (
            endpointName !== 'getCoupons' &&
            endpointName !== 'getCouponsDetails' &&
            endpointName !== 'getCouponsWithPagination' &&
            endpointName !== 'getSimilarCoupons'
          )
            continue;
          const patchResult = dispatch(
            api.util.updateQueryData(endpointName, originalArgs, (draft) => {
              if (draft) {
                if ('results' in draft) {
                  const index = draft.results.findIndex((item) => item.id === payload.object_id);
                  if (index !== -1) {
                    draft.results[index].is_favorite = true;
                  }
                } else if ('id' in draft) {
                  draft.is_favorite = true;
                }
              }
            }),
          );
          try {
            await queryFulfilled;
          } catch {
            patchResult.undo();
          }
        }
      },
    }),

    putCouponFavorite: builder.mutation<void, POSTFavoriteParam>({
      query: (params: POSTFavoriteParam) => ({
        url: `content/favorites/update/${params.payload.object_id}/`,
        method: 'PUT',
        params: {
          object_type: params.payload.object_type,
        },
        body: {
          bookmark: params.payload.bookmark,
        },
      }),

      invalidatesTags: ['Favorite'],
    }),

    deleteCouponFavorite: builder.mutation<void, POSTFavoriteParam>({
      query: (params: POSTFavoriteParam) => ({
        url: `content/favorites/${params.payload.object_id}/`,
        method: 'DELETE',
        params: {
          object_type: params.payload.object_type,
        },
      }),

      invalidatesTags: ['Favorite'],

      async onQueryStarted({ payload }, { dispatch, queryFulfilled, getState }) {
        for (const { endpointName, originalArgs } of api.util.selectInvalidatedBy(getState(), [
          { type: 'Coupon' },
        ])) {
          if (
            endpointName !== 'getCoupons' &&
            endpointName !== 'getCouponsDetails' &&
            endpointName !== 'getCouponsWithPagination' &&
            endpointName !== 'getSimilarCoupons'
          )
            continue;
          const patchResult = dispatch(
            api.util.updateQueryData(endpointName, originalArgs, (draft) => {
              if (draft) {
                if ('results' in draft) {
                  const index = draft.results.findIndex((item) => item.id === payload.object_id);
                  if (index !== -1) {
                    draft.results[index].is_favorite = false;
                  }
                } else if ('id' in draft) {
                  draft.is_favorite = false;
                }
              }
            }),
          );
          try {
            await queryFulfilled;
          } catch {
            patchResult.undo();
          }
        }
      },
    }),
  }),
});

export const {
  useGetCouponsQuery,
  useLazyGetCouponsQuery,
  useGetSimilarCouponsQuery,
  useGetCouponsWithPaginationQuery,
  useGetCouponsDetailsQuery,
  useLazyGetCouponsDetailsQuery,
  usePostCouponFavoriteMutation,
  usePutCouponFavoriteMutation,
  useDeleteCouponFavoriteMutation,
} = api;
