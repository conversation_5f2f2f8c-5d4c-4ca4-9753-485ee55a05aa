import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

import { SERVER_URL } from '~/config.api';
import { buildPrams } from '~/helpers/buildApiParam';
import { ILocalizationCollections } from '~/types/localization';
import { ParamsOptions } from '~/types/shared';
// import axios from '~/utils/axios.config';

// export const getLocalizationCollectionApi = (payload?: ParamsOptions) => {
//   const params: ParamsOptions = payload ? buildPrams(payload) : {};
//   return axios<ILocalizationCollections>({
//     method: 'GET',
//     url: `${SERVER_URL}/content/cities/`,
//     headers: {
//       Accept: 'application/json',
//     },
//     params: params,
//   });
// };

export const api = createApi({
  reducerPath: 'api/content/cities',
  baseQuery: fetchBaseQuery({
    baseUrl: `${SERVER_URL}/content/cities/`,
    paramsSerializer: (params) => {
      return Object.keys(params)
        .flatMap((key) => {
          const value = params[key];

          if (value !== undefined && value !== null) {
            if (Array.isArray(value) && value.length > 0) {
              return value.map((v) => `${key}=${v}`);
            } else if (typeof value === 'boolean') {
              return `${key}=${value.toString()}`;
            } else if (typeof value === 'number' || typeof value === 'string') {
              return `${key}=${value}`;
            }
          }
          return '';
        })
        .filter((param) => param !== '')
        .join('&');
    },
  }),

  endpoints: (builder) => ({
    getCities: builder.query<ILocalizationCollections, ParamsOptions>({
      query: (params?: ParamsOptions) => {
        return {
          url: '',
          method: 'GET',
          headers: {
            Accept: 'application/json',
          },
          params,
        };
      },
      serializeQueryArgs: ({ endpointName }) => {
        return endpointName;
      },

      // Merge only when the page arg changes
      merge: (currentCache, newItems, { arg }) => {
        if (arg?.page) {
          currentCache.next = newItems.next;
          currentCache.previous = newItems.previous;
          currentCache.count = newItems.count;
          currentCache.results.push(...newItems.results);
        } else {
          currentCache.next = newItems.next;
          currentCache.previous = newItems.previous;
          currentCache.count = newItems.count;
          currentCache.results = newItems.results;
        }
      },

      // Refetch when the arg changes
      forceRefetch({ currentArg, previousArg }) {
        return currentArg !== previousArg;
      },
    }),
  }),
});

export const { useLazyGetCitiesQuery, useGetCitiesQuery } = api;
