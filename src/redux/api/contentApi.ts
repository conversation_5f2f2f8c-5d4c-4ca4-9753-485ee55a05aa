import { createApi } from '@reduxjs/toolkit/query/react';

import { customBaseQuery } from '~/utils/fetchBaseQuery.config';

export const tagTypes = [
  'Article',
  'Card',
  'Coupon',
  'Inspiration',
  'Leaflet',
  'Brand',
  'Favorite',
  'StartupInfo',
] as const;

export type TContentApiTagType = (typeof tagTypes)[number];

export const contentApi = createApi({
  reducerPath: 'contentApi',
  baseQuery: customBaseQuery,
  endpoints: () => ({}),
  tagTypes: tagTypes,
  keepUnusedDataFor: 15 * 60,
});
