import _isEqual from 'lodash/isEqual';

import { POSTFavoriteParam } from '../types/common';

import { contentApi } from './contentApi';

import { SERVER_URL } from '~/config.api';
import { IAddress } from '~/types/addresses';
import { IDiscountCode, IDiscountCollections, IDiscountDetail } from '~/types/discount';
import { ParamsOptions } from '~/types/shared';
import axios from '~/utils/axios.config';

export const getDiscountCodeApi = (payload: { id: number }) => {
  return axios<IDiscountCode>({
    method: 'GET',
    url: `${SERVER_URL}/content/discount/${payload.id}/`,
    headers: {
      Accept: 'application/json',
    },
  });
};

export const getDiscountAddressApi = (payload: { id: number }) => {
  return axios<IAddress[]>({
    method: 'GET',
    url: `${SERVER_URL}/content/cards/${payload.id}/addresses/`,
    headers: {
      Accept: 'application/json',
    },
  });
};

export type GETCardParams = ParamsOptions;
export interface WithTokenParams extends GETCardParams {
  token?: string | null;
}
//type Param = {
const api = contentApi.injectEndpoints({
  endpoints: (builder) => ({
    getCards: builder.query<IDiscountCollections, WithTokenParams>({
      query: (params: WithTokenParams) => {
        let headers: any;
        if (params.token) {
          headers = {
            Authorization: `Bearer ${params.token}`,
          };
        }

        return {
          url: 'content/cards/',
          method: 'GET',
          params,
          headers,
        };
      },
      providesTags: ['Card'],
    }),

    getSimilarCards: builder.query<IDiscountCollections, GETCardParams>({
      query: (params: GETCardParams) => {
        return {
          url: 'content/cards/',
          method: 'GET',
          params,
        };
      },
      providesTags: ['Card'],
    }),

    getCardsWithPagination: builder.query<IDiscountCollections, GETCardParams>({
      query: (params: GETCardParams) => {
        return {
          url: 'content/cards/',
          method: 'GET',
          params,
        };
      },

      serializeQueryArgs: ({ endpointName }) => {
        return endpointName;
      },

      // Merge only when the page arg changes
      merge: (currentCache, newItems, { arg }) => {
        if (arg?.page) {
          currentCache.next = newItems.next;
          currentCache.previous = newItems.previous;
          currentCache.count = newItems.count;
          currentCache.results.push(...newItems.results);
        } else {
          currentCache.next = newItems.next;
          currentCache.previous = newItems.previous;
          currentCache.count = newItems.count;
          currentCache.results = newItems.results;
        }
      },

      forceRefetch({ currentArg, previousArg }) {
        return !_isEqual(currentArg, previousArg);
      },
      providesTags: ['Card'],
    }),

    getCardsDetails: builder.query<IDiscountDetail, { id: number }>({
      query: (params) => {
        return {
          url: `content/cards/${params.id}/`,
          method: 'GET',
        };
      },
      providesTags: ['Card'],
    }),

    getDiscountCode: builder.query<{ code: string }, { id?: number }>({
      query: (params) => {
        return {
          url: `content/discount/${params.id}/`,
          method: 'GET',
        };
      },
    }),

    //!FAVORITES
    postCardFavorite: builder.mutation<void, POSTFavoriteParam>({
      query: (params: POSTFavoriteParam) => ({
        url: `content/favorites/add/`,
        method: 'POST',
        body: params.payload,
      }),

      invalidatesTags: ['Favorite'],

      async onQueryStarted({ payload }, { dispatch, queryFulfilled, getState }) {
        for (const { endpointName, originalArgs } of api.util.selectInvalidatedBy(getState(), [
          { type: 'Card' },
        ])) {
          if (
            endpointName !== 'getCards' &&
            endpointName !== 'getCardsDetails' &&
            endpointName !== 'getCardsWithPagination' &&
            endpointName !== 'getSimilarCards'
          )
            continue;
          const patchResult = dispatch(
            api.util.updateQueryData(endpointName, originalArgs, (draft) => {
              if (draft) {
                if ('results' in draft) {
                  const index = draft.results.findIndex((item) => item.id === payload.object_id);
                  if (index !== -1) {
                    draft.results[index].is_favorite = true;
                  }
                } else if ('id' in draft) {
                  draft.is_favorite = true;
                }
              }
            }),
          );
          try {
            await queryFulfilled;
          } catch {
            patchResult.undo();
          }
        }
      },
    }),

    putCardFavorite: builder.mutation<void, POSTFavoriteParam>({
      query: (params: POSTFavoriteParam) => ({
        url: `content/favorites/update/${params.payload.object_id}/`,
        method: 'PUT',
        params: {
          object_type: params.payload.object_type,
        },
        body: {
          bookmark: params.payload.bookmark,
        },
      }),

      invalidatesTags: ['Favorite'],
    }),

    deleteCardFavorite: builder.mutation<void, POSTFavoriteParam>({
      query: (params: POSTFavoriteParam) => ({
        url: `content/favorites/${params.payload.object_id}/`,
        method: 'DELETE',
        params: {
          object_type: params.payload.object_type,
        },
      }),

      invalidatesTags: ['Favorite'],

      async onQueryStarted({ payload }, { dispatch, queryFulfilled, getState }) {
        for (const { endpointName, originalArgs } of api.util.selectInvalidatedBy(getState(), [
          { type: 'Card' },
        ])) {
          if (
            endpointName !== 'getCards' &&
            endpointName !== 'getCardsDetails' &&
            endpointName !== 'getCardsWithPagination' &&
            endpointName !== 'getSimilarCards'
          )
            continue;
          const patchResult = dispatch(
            api.util.updateQueryData(endpointName, originalArgs, (draft) => {
              if (draft) {
                if ('results' in draft) {
                  const index = draft.results.findIndex((item) => item.id === payload.object_id);
                  if (index !== -1) {
                    draft.results[index].is_favorite = false;
                  }
                } else if ('id' in draft) {
                  draft.is_favorite = false;
                }
              }
            }),
          );
          try {
            await queryFulfilled;
          } catch {
            patchResult.undo();
          }
        }
      },
    }),
  }),
});
export const {
  useGetCardsQuery,
  useLazyGetCardsQuery,
  useGetSimilarCardsQuery,
  useGetCardsWithPaginationQuery,
  useLazyGetCardsWithPaginationQuery,
  useGetCardsDetailsQuery,
  useLazyGetCardsDetailsQuery,
  usePostCardFavoriteMutation,
  usePutCardFavoriteMutation,
  useDeleteCardFavoriteMutation,
  useLazyGetDiscountCodeQuery,
  useGetDiscountCodeQuery,
} = api;
