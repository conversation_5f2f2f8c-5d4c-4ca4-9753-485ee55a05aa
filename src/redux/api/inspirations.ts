import _isEqual from 'lodash/isEqual';

import { POSTFavoriteParam } from '../types/common';

import { contentApi } from './contentApi';

import { SERVER_URL } from '~/config.api';
import { axiosParamsSerializer, buildPrams } from '~/helpers/buildApiParam';
import { IInspirationCollections, IInspirationDetails } from '~/types/inspirations';
import { ParamsOptions } from '~/types/shared';
import axios from '~/utils/axios.config';

export const getInspirationCollectionApi = (payload?: ParamsOptions) => {
  const params: ParamsOptions = payload ? buildPrams(payload) : {};
  return axios<IInspirationCollections>({
    method: 'GET',
    url: `${SERVER_URL}/content/inspirations/`,
    headers: {
      Accept: 'application/json',
    },
    params,
    paramsSerializer: axiosParamsSerializer,
  });
};

export const getInspirationDetailApi = (payload: { id: number }) => {
  return axios<IInspirationDetails>({
    method: 'GET',
    url: `${SERVER_URL}/content/inspirations/${payload.id}/`,
    headers: {
      Accept: 'application/json',
    },
  });
};

export type GETInspirationsParams = ParamsOptions;
export interface WithTokenParams extends GETInspirationsParams {
  token?: string | null;
}

const api = contentApi.injectEndpoints({
  endpoints: (builder) => ({
    getInspirations: builder.query<IInspirationCollections, WithTokenParams>({
      query: (params: WithTokenParams) => {
        let headers: any;
        if (params.token) {
          headers = {
            Authorization: `Bearer ${params.token}`,
          };
        }

        return {
          url: 'content/inspirations/',
          method: 'GET',
          params,
          headers,
        };
      },
      providesTags: ['Inspiration'],
    }),

    getSimilarInspirations: builder.query<IInspirationCollections, GETInspirationsParams>({
      query: (params: GETInspirationsParams) => {
        return {
          url: 'content/inspirations/',
          method: 'GET',
          params,
        };
      },
      providesTags: ['Inspiration'],
    }),

    getInspirationsWithPagination: builder.query<IInspirationCollections, GETInspirationsParams>({
      query: (params: GETInspirationsParams) => {
        return {
          url: 'content/inspirations/',
          method: 'GET',
          params,
        };
      },

      serializeQueryArgs: ({ endpointName }) => {
        return endpointName;
      },

      merge: (currentCache, newItems, { arg }) => {
        if (arg?.page) {
          currentCache.next = newItems.next;
          currentCache.previous = newItems.previous;
          currentCache.count = newItems.count;
          currentCache.results.push(...newItems.results);
        } else {
          currentCache.next = newItems.next;
          currentCache.previous = newItems.previous;
          currentCache.count = newItems.count;
          currentCache.results = newItems.results;
        }
      },

      forceRefetch({ currentArg, previousArg }) {
        return !_isEqual(currentArg, previousArg);
      },
      providesTags: ['Inspiration'],
    }),

    getInspirationsDetails: builder.query<IInspirationDetails, { id: number }>({
      query: (params) => {
        return {
          url: `content/inspirations/${params.id}/`,
          method: 'GET',
        };
      },
      providesTags: ['Inspiration'],
    }),

    //!FAVORITES
    postInspirationFavorite: builder.mutation<void, POSTFavoriteParam>({
      query: (params: POSTFavoriteParam) => ({
        url: `content/favorites/add/`,
        method: 'POST',
        body: params.payload,
      }),

      invalidatesTags: ['Favorite'],

      async onQueryStarted({ payload }, { dispatch, queryFulfilled, getState }) {
        for (const { endpointName, originalArgs } of api.util.selectInvalidatedBy(getState(), [
          { type: 'Inspiration' },
        ])) {
          if (
            endpointName !== 'getInspirations' &&
            endpointName !== 'getInspirationsDetails' &&
            endpointName !== 'getInspirationsWithPagination' &&
            endpointName !== 'getSimilarInspirations'
          )
            continue;
          const patchResult = dispatch(
            api.util.updateQueryData(endpointName, originalArgs, (draft) => {
              if (draft) {
                if ('results' in draft) {
                  const index = draft.results.findIndex((item) => item.id === payload.object_id);
                  if (index !== -1) {
                    draft.results[index].is_favorite = true;
                  }
                } else if ('id' in draft) {
                  draft.is_favorite = true;
                }
              }
            }),
          );
          try {
            await queryFulfilled;
          } catch {
            patchResult.undo();
          }
        }
      },
    }),

    putInspirationFavorite: builder.mutation<void, POSTFavoriteParam>({
      query: (params: POSTFavoriteParam) => ({
        url: `content/favorites/update/${params.payload.object_id}/`,
        method: 'PUT',
        params: {
          object_type: params.payload.object_type,
        },
        body: {
          bookmark: params.payload.bookmark,
        },
      }),

      invalidatesTags: ['Favorite'],
    }),

    deleteInspirationFavorite: builder.mutation<void, POSTFavoriteParam>({
      query: (params: POSTFavoriteParam) => ({
        url: `content/favorites/${params.payload.object_id}/`,
        method: 'DELETE',
        params: {
          object_type: params.payload.object_type,
        },
      }),

      invalidatesTags: ['Favorite'],

      async onQueryStarted({ payload }, { dispatch, queryFulfilled, getState }) {
        for (const { endpointName, originalArgs } of api.util.selectInvalidatedBy(getState(), [
          { type: 'Inspiration' },
        ])) {
          if (
            endpointName !== 'getInspirations' &&
            endpointName !== 'getInspirationsDetails' &&
            endpointName !== 'getInspirationsWithPagination' &&
            endpointName !== 'getSimilarInspirations'
          )
            continue;
          const patchResult = dispatch(
            api.util.updateQueryData(endpointName, originalArgs, (draft) => {
              if (draft) {
                if ('results' in draft) {
                  const index = draft.results.findIndex((item) => item.id === payload.object_id);
                  if (index !== -1) {
                    draft.results[index].is_favorite = false;
                  }
                } else if ('id' in draft) {
                  draft.is_favorite = false;
                }
              }
            }),
          );
          try {
            await queryFulfilled;
          } catch {
            patchResult.undo();
          }
        }
      },
    }),
  }),
});
export const {
  useGetInspirationsQuery,
  useGetSimilarInspirationsQuery,
  useGetInspirationsDetailsQuery,
  useGetInspirationsWithPaginationQuery,
  usePostInspirationFavoriteMutation,
  usePutInspirationFavoriteMutation,
  useLazyGetInspirationsDetailsQuery,
  useLazyGetInspirationsQuery,
  useLazyGetInspirationsWithPaginationQuery,
  useDeleteInspirationFavoriteMutation,
} = api;
