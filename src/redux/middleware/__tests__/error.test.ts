import Bugsnag from '@bugsnag/react-native';

import { errorMiddleware } from '~/redux/middleware/error';
import { errorInit } from '~/redux/reducers/error/actions';

jest.mock('@bugsnag/react-native', () => ({
  notify: jest.fn(),
}));

describe('errorMiddleware', () => {
  let store: any;
  let next: jest.Mock;
  let action: any;

  beforeEach(() => {
    store = {
      dispatch: jest.fn(),
    };
    next = jest.fn();
    (Bugsnag.notify as jest.Mock).mockReset();
  });

  it('should call Bugsnag and dispatch errorInit on failure actions', () => {
    action = {
      type: 'some/failure',
      payload: {
        message: 'An error occurred',
        status: true,
      },
    };

    errorMiddleware(store)(next)(action);

    expect(Bugsnag.notify).toHaveBeenCalledWith(new Error('An error occurred'));
    expect(store.dispatch).toHaveBeenCalledWith(
      errorInit({ message: 'An error occurred', status: true }),
    );
    expect(next).toHaveBeenCalledWith(action);
  });

  it('should not call Bugsnag and dispatch errorInit on non-failure actions', () => {
    action = {
      type: 'some/success',
      payload: {},
    };

    errorMiddleware(store)(next)(action);
    expect(Bugsnag.notify).not.toHaveBeenCalled();
    expect(store.dispatch).not.toHaveBeenCalled();
    expect(next).toHaveBeenCalledWith(action);
  });
});
