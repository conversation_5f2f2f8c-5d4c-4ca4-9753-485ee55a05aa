import 'symbol-observable';

import { Dispatch, Middleware } from 'redux';

import { RootState } from '~/redux/store';

const blacklist = ['privacy', 'user'];

export const checkCacheLength: Middleware<{}, RootState> = (store) => (next) => (action) => {
  const state = store.getState();
  const reducerName = action.type.split('/') || {};

  if (!blacklist.includes(reducerName[0])) {
    if (
      state[reducerName[0]] &&
      Array.isArray(state[reducerName[0]]?.detail) &&
      action.type.includes('detail/success')
    ) {
      if (state[reducerName[0]]?.detail.length > 1) {
        const dispatch: Dispatch = store.dispatch;
        dispatch({ type: `${reducerName[0]}/detail/clear` });
      }
    }
  }
  return next(action);
};
