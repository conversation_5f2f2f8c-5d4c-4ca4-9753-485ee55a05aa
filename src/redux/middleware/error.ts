import Bugsnag from '@bugsnag/react-native';
import { Dispatch, Middleware } from 'redux';

import { errorInit } from '~/redux/reducers/error/actions';

interface Action {
  type: string;
  payload?: any;
}

export const errorMiddleware: Middleware =
  (store) => (next: Dispatch<Action>) => (action: Action) => {
    if (action.type.includes('failure')) {
      Bugsnag.notify(new Error(action.payload.message));
      store.dispatch(errorInit({ message: action.payload.message, status: action.payload.status }));
    }
    return next(action);
  };
