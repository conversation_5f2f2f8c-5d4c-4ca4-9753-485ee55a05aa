import { Middleware } from 'redux';

import { addDataToCache } from '../reducers/content/cache/actions';

import { RootState } from '~/redux/store';

const supportedTypes = [
  // ----DISCOUNTS----
  'discounts/collection/success',
  'discounts/detail/success',
  'discounts/code/success',

  // ----ARTICLES----
  'articles/collection/success',
  'articles/detail/success',

  // ----BRANDS----
  'brands/collection/success',
  'brands/detail/success',

  // ----NEWSPAPERS----
  'newspapers/collection/success',
  'newspapers/detail/success',

  // ----INSPIRATIONS----
  'inspirations/collection/success',
  'inspirations/detail/success',

  // ----COUPONS----
  'coupons/collection/success',
  'coupons/detail/success',
];

const supportedRetriveTypes = [
  // ----DISCOUNTS----
  'discounts/collection/failure',
  'discounts/detail/failure',
  '',

  // ----ARTICLES----
  'articles/collection/failure',
  'articles/detail/failure',

  // ----BRANDS----
  'brands/collection/failure',
  'brands/detail/failure',

  // ----NEWSPAPERS----
  'newspapers/collection/failure',
  'newspapers/detail/failure',

  // ----INSPIRATIONS----
  'inspirations/collection/failure',
  'inspirations/detail/failure',

  // ----COUPONS----
  'coupons/collection/failure',
  'coupons/detail/failure',
];

/**
 * @param data - data from api
 * @returns data - data from api in format: [{...},{...},{...}]
 */
const formatData = (data: any) => {
  if (data.results) {
    return data.results;
  }
  return data;
};

/**
 * @param actionType - type of action eg. discounts/collection/success
 * @returns reducer - name of reducer eg. discounts or articles or brands etc.
 * @returns type - type of data eg. collection or detail or code
 */
const returnActionType = (actionType: string) => {
  let type: string = '';
  const reducerType = actionType.split('/')[1];

  if (reducerType === 'detail') {
    type = 'detail';
  } else if (reducerType === 'code') {
    type = 'code';
  } else {
    type = 'collection';
  }
  return {
    reducer: actionType.split('/')[0],
    type: type,
  };
};

/**
 * @param store - redux store
 * @returns next - next action
 * @returns action - action from redux
 * @description
 * Middleware for caching data from api to redux store.
 * It's caching data only when network is connected.
 * When network is disconnected it's returning data from cache.
 * It's caching data only for supported types.
 * Supported types are in supportedTypes array.
 * It's returning data from cache only for supportedRetriveTypes.
 * Remember to keep right index in supportedRetriveTypes array.
 * Eg. supportedRetriveTypes[0] is for supportedTypes[0].
 */
export const cache: Middleware<{}, RootState> = (store) => (next) => (action) => {
  const { dispatch, getState } = store;
  const isNetwork = getState().network.isConnected;
  const { reducer, type } = returnActionType(action.type);

  if (supportedTypes.includes(action.type)) {
    const data = formatData(action.payload);
    dispatch(
      addDataToCache({
        key: reducer,
        type: type,
        data: data,
      }),
    );
  }

  if (supportedRetriveTypes.includes(action.type)) {
    const index = supportedRetriveTypes.indexOf(action.type);
    if (!isNetwork) {
      const cacheData = getState().cache[reducer][type];

      switch (type) {
        case 'collection':
          return next({
            type: supportedTypes[index],
            payload: {
              results: cacheData,
            },
          });

        case 'detail':
          const element = cacheData.find((item: any) => item.id === action.payload.id);
          const payload = element
            ? element
            : {
                ...element,
                error: {
                  message: 'NETWORK_ERROR',
                  status: true,
                },
              };
          return next({
            type: supportedTypes[index],
            payload: payload,
          });
      }
    }
  }
  return next(action);
};
