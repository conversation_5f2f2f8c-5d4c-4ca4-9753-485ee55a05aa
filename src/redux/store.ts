import AsyncStorage from "@react-native-async-storage/async-storage";
import { configureStore } from "@reduxjs/toolkit";
import { TypedUseSelectorHook, useDispatch, useSelector } from "react-redux";
import { combineReducers } from "redux";
import { persistReducer, persistStore } from "redux-persist";
import "symbol-observable";

import { cache } from "./middleware/cache";
import { errorReducer } from "./reducers/error/reducers";

//import { api as articleApi } from '~/redux/api/articles';
import { api as bannerApi } from "~/redux/api/banners";
import { contentApi } from "~/redux/api/contentApi";
// import { api as couponApi } from '~/redux/api/coupons';
// import { api as cardApi } from '~/redux/api/discounts';
// import { api as inspirationApi } from '~/redux/api/inspirations';
import { api as cityApi } from "~/redux/api/localizations";
import { checkCacheLength } from "~/redux/middleware/checkCacheLength";
import { errorMiddleware } from "~/redux/middleware/error";
import { reducer as articlesReducer } from "~/redux/reducers/content/articles/reducers";
import { reducer as bannersReducer } from "~/redux/reducers/content/banners/reducers";
import { reducer as brandsReducer } from "~/redux/reducers/content/brands/reducers";
import { reducer as cacheReducer } from "~/redux/reducers/content/cache/reducers";
import { reducer as categoriesReducer } from "~/redux/reducers/content/categories/reducers";
import { reducer as couponsReducer } from "~/redux/reducers/content/coupon/reducers";
import { reducer as discountsReducer } from "~/redux/reducers/content/discounts/reducers";
import { reducer as favoriteReducer } from "~/redux/reducers/content/favorites/reducers";
import { reducer as inspirationsReducer } from "~/redux/reducers/content/inspirations/reducers";
import { reducer as localizationReducer } from "~/redux/reducers/content/localization/reducers";
import { reducer as newspapersReducer } from "~/redux/reducers/content/newspapers/reducers";
import { reducer as notificationsReducer } from "~/redux/reducers/content/notifications/reducers";
import { reducer as searchReducer } from "~/redux/reducers/content/search/reducers";
import { reducer as activeSectionsReducer } from "~/redux/reducers/info/activeSections/reducers";
import { reducer as appVersionReducer } from "~/redux/reducers/info/appVersion/reducers";
import { reducer as contactReducer } from "~/redux/reducers/info/contact/reducers";
import { reducer as onboardingReducer } from "~/redux/reducers/info/onboarding/reducers";
import { reducer as infoPremiumPlan } from "~/redux/reducers/info/premiumPlan/reducers";
import { reducer as networkReducer } from "~/redux/reducers/network/reducers";
import { reducer as privacyReducer } from "~/redux/reducers/privacy/reducers";
import { reducer as sessionReducer } from "~/redux/reducers/states/reducers";
import { reducer as userReducer } from "~/redux/reducers/user/reducers";
import { reducer as userSearchesReducer } from "~/redux/reducers/content/userSearch/reducers";

const excludedClearReducers = [
  "network",
  "cache",
  "contentApi",
  "api/content/banners",
]; // List of reducers to exclude
export const appReducer = combineReducers({
  error: errorReducer,
  brands: brandsReducer,
  coupons: couponsReducer,
  discounts: discountsReducer,
  newspapers: newspapersReducer,
  articles: articlesReducer,
  privacy: privacyReducer,
  user: userReducer,
  inspirations: inspirationsReducer,
  banners: bannersReducer,
  categories: categoriesReducer,
  localization: localizationReducer,
  info: infoPremiumPlan,
  session: sessionReducer,
  notifications: notificationsReducer,
  onboarding: onboardingReducer,
  contact: contactReducer,
  search: searchReducer,
  favorites: favoriteReducer,
  activeSections: activeSectionsReducer,
  cache: cacheReducer,
  network: networkReducer,
  appVersion: appVersionReducer,
  userSearches: userSearchesReducer,

  //[brandApi.reducerPath]: brandApi.reducer,
  // [cardApi.reducerPath]: cardApi.reducer,
  [contentApi.reducerPath]: contentApi.reducer,
  //[couponApi.reducerPath]: couponApi.reducer,
  //[newspaperApi.reducerPath]: newspaperApi.reducer,
  //[inspirationApi.reducerPath]: inspirationApi.reducer,
  //[articleApi.reducerPath]: articleApi.reducer,
  [bannerApi.reducerPath]: bannerApi.reducer,
  [cityApi.reducerPath]: cityApi.reducer,
});

// Sprawdź czy wszystkie middleware są prawidłowo zdefiniowane
const apiMiddlewares = [
  contentApi.middleware,
  bannerApi.middleware,
  cityApi.middleware,
];

const customMiddlewares = [errorMiddleware, checkCacheLength, cache];

// Filtruj tylko prawidłowe middleware (funkcje)
const validMiddlewares = [...apiMiddlewares, ...customMiddlewares].filter(
  (middleware) => typeof middleware === "function"
);

const rootReducer = (state: any, action: any) => {
  if (action.type === "user/logout") {
    //return appReducer(undefined, action);
    const nextState = Object.assign(
      {},
      ...excludedClearReducers.map((key) => ({ [key]: state[key] }))
    );
    // Call appReducer to handle the logout action for other reducers
    return appReducer(nextState, action);
  }
  return appReducer(state, action);
};

const persistConfig = {
  key: "brum", // klucz, pod którym dane będą przechowywane w AsyncStorage
  storage: AsyncStorage,
  whitelist: ["user", "cache", "userSearches"], // nazwy reducery do trwałego przechowywania
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
      immutableCheck: false,
    }).concat(validMiddlewares),
});

export default store;

export const persistor = persistStore(store);
export type RootState = ReturnType<typeof rootReducer>;
export type AppDispatch = typeof store.dispatch;
export type AppThunk = (...args: any[]) => void;
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
