export interface IError {
  message: string;
  status: boolean;
  id?: number;
}

export interface IReducer {
  error: IError;
  loading: boolean;
}

export interface ICollection {
  count: 0;
  next: null;
  previous: null;
  results: [];
}

export type POSTFavoriteParam = {
  payload: {
    object_type: ObjectTypeEnum;
    object_id: number;
    bookmark?: string;
  };
  type: TContentApiTagType;
};
