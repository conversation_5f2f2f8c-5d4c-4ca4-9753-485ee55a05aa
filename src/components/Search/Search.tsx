import { TouchableOpacity } from 'react-native';
import { useNavigate } from '~/hooks/useNavigate';
import { Icon } from '../Icon/Icon';
import { assets } from '~/theme/assets';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';

interface Props {
  to: ObjectTypeEnum;
}
export const Search = ({ to }: Props) => {
  const { navigateToSearch } = useNavigate();

  return (
    <TouchableOpacity onPress={() => navigateToSearch(to)}>
      <Icon iconSVG={assets.icons.search_svg} width={24} height={24} />
    </TouchableOpacity>
  );
};
