import { useState } from 'react';
import { useController, useFormContext } from 'react-hook-form';

import * as Styled from './Select.styled';

import { Icon } from '~/components/Icon/Icon';
import { Label } from '~/components/Inputs/components/Label';
import { useLabelAnimation } from '~/components/Inputs/hooks/useLabelAnimation';
import { ISelect } from '~/components/Inputs/Select/types';
import { Typography } from '~/components/Typography/Typography';
import { assets } from '~/theme/assets';

export const Select = ({ placeholder, options, name }: ISelect) => {
  const { setValue, control } = useFormContext();
  const { field } = useController({
    control,
    name,
    defaultValue: '',
  });
  const [open, setOpen] = useState(false);
  const { translateY } = useLabelAnimation({ value: field.value });

  const handleChoose = (value: string) => {
    setValue(name, value);
    setOpen(false);
  };

  return (
    <Styled.Wrapper style={{ zIndex: 1 }}>
      <Label label={placeholder} translateY={translateY} />
      <Styled.Input onPress={() => setOpen(!open)}>
        <Typography fontSize={16} fontWeight="400">
          {field.value ? field.value : ''}
        </Typography>
        <Icon
          iconSVG={open ? assets.icons.arrow_up_svg : assets.icons.arrow_down_svg}
          width={24}
          height={24}
        />
      </Styled.Input>
      {open ? (
        <>
          <Styled.Select style={{ elevation: 1, zIndex: 1 }}>
            {options.map((option, index) => {
              return (
                <Styled.Item
                  onPress={() => handleChoose(option)}
                  key={index}
                  last={index === options.length - 1}>
                  <Typography fontSize={16} fontWeight="400">
                    {option}
                  </Typography>
                </Styled.Item>
              );
            })}
          </Styled.Select>
        </>
      ) : null}
    </Styled.Wrapper>
  );
};
