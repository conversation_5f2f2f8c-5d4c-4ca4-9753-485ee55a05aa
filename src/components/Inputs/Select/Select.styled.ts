import styled from '@emotion/native';

import { flexRowBetween } from '~/styles/flex.styled';
import theme from '~/theme';
import { shadow } from '~/theme/shadows';
import { WIDE_SCREEN } from '~/utils/constants/shared';

export const Wrapper = styled.View`
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  border-color: ${theme.colors.gray[1000]};
  border-bottom-width: 1px;
  border-left-width: 0;
  border-right-width: 0;
  border-top-width: 0;
  max-width: ${`${WIDE_SCREEN}px`};
`;

export const InputWrapper = styled.TouchableOpacity`
  width: 100%;
  height: 48px;
`;

export const Input = styled.TouchableOpacity`
  ${flexRowBetween};
  padding: 0 16px;
  width: 100%;
  height: 48px;
`;

export const Select = styled.View`
  position: absolute;
  z-index: 100;
  top: 56px;
  left: 0;
  right: 0;
  background-color: ${theme.colors.white[100]};
  gap: 12px;
  padding: 16px;
  border-radius: 8px;
  ${shadow.gray[200]};
`;

export const List = styled.FlatList`
  background-color: ${theme.colors.white[100]};
`;

export interface IItem {
  last?: boolean;
}

export const Item = styled.TouchableOpacity<IItem>`
  border-bottom-width: ${({ last }) => (last ? `${0}` : `${2}px`)};
  padding-bottom: ${({ last }) => (last ? `${0}` : `${8}px`)};
  border-color: ${theme.colors.gray[1000]};
`;
