import styled from '@emotion/native';

import theme from '~/theme';
import { WIDE_SCREEN } from '~/utils/constants/shared';

export const Container = styled.View`
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  width: 100%;
  max-width: ${`${WIDE_SCREEN}px`};
`;

export const Wrapper = styled.View`
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  border-color: ${theme.colors.gray[1000]};
  border-bottom-width: 1px;
  padding-right: 16px;
`;

export const Label = styled.Text`
  color: ${theme.colors.gray[700]};
`;

export const Input = styled.TextInput<{ editable?: boolean }>`
  flex: 8;
  padding: 8px 16px 8px 16px;
  font-size: 16px;
  font-weight: 400;
  color: ${({ editable }) => (editable ? theme.colors.black[100] : theme.colors.gray[700])};
`;

export const Error = styled.View`
  margin: 2px 0 0 16px;
`;
