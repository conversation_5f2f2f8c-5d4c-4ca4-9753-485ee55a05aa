import { FieldError, <PERSON><PERSON><PERSON>rsImpl, Merge } from 'react-hook-form';

import * as Styled from '~/components/Inputs/Input/Input.styled';
import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';

export const Error = ({
  error,
}: {
  error: FieldError | Merge<FieldError, FieldErrorsImpl<any>> | undefined;
}) => {
  return (
    <>
      {error ? (
        <Styled.Error>
          <Typography fontSize={12} color={theme.colors.red[100]}>
            {error?.message as string}
          </Typography>
        </Styled.Error>
      ) : null}
    </>
  );
};
