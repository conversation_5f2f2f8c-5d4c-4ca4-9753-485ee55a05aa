import { FC } from 'react';
import { TouchableOpacity } from 'react-native';
import { SvgProps } from 'react-native-svg';

import { Icon as IconItem } from '~/components/Icon/Icon';
import theme from '~/theme';

interface IIcon {
  icon?: FC<SvgProps>;
  secure: boolean;
  setSecure: (value: boolean) => void;
}

export const Icon = ({ icon, secure, setSecure }: IIcon) => {
  return (
    <>
      {icon ? (
        <TouchableOpacity onPress={() => setSecure(!secure)} hitSlop={16}>
          <IconItem
            iconSVG={icon}
            width={28}
            height={28}
            fill={!secure ? theme.colors.purple[100] : theme.colors.gray[900]}
            isFill={true}
            isStroke={false}
          />
        </TouchableOpacity>
      ) : null}
    </>
  );
};
