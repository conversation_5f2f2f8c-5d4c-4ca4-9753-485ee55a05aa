import * as Styled from '~/components/Inputs/Input/Input.styled';
import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';

export const CustomError = ({ error }: { error: string }) => {
  return (
    <>
      {error.length > 0 && error ? (
        <Styled.Error>
          <Typography fontSize={12} color={theme.colors.red[100]}>
            {error}
          </Typography>
        </Styled.Error>
      ) : null}
    </>
  );
};
