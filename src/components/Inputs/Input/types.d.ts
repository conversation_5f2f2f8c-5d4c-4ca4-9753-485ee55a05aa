import { FC } from 'react';
import { KeyboardTypeOptions } from 'react-native';
import { SvgProps } from 'react-native-svg';
interface IUseLabelAnimation {
  value: string;
}

export interface IInput {
  keyboardType?: KeyboardTypeOptions;
  editable?: boolean;
  placeholder?: string;
  icon?: FC<SvgProps>;
  label?: string;
  isPassword?: boolean;
  name: string;
  customError?: string;
  maxLength?: number;
  multiline?: boolean;
  onChangeText?: (text: string) => void;
  value?: string;
}
