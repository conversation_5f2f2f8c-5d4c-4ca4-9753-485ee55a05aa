import { useState } from 'react';
import { useController, useFormContext } from 'react-hook-form';

import * as Styled from './Input.styled';

import { Label } from '~/components/Inputs/components/Label';
import { useLabelAnimation } from '~/components/Inputs/hooks/useLabelAnimation';
import { CustomError } from '~/components/Inputs/Input/components/CustomError';
import { Error } from '~/components/Inputs/Input/components/Error';
import { Icon } from '~/components/Inputs/Input/components/Icon';
import { IInput } from '~/components/Inputs/Input/types';
import theme from '~/theme';

export const Input = ({
  placeholder,
  icon,
  label,
  isPassword = false,
  multiline = false,
  name,
  customError,
  maxLength,
  editable = true,
  keyboardType = 'default',
}: IInput) => {
  const [secure, setSecure] = useState<boolean>(isPassword);
  const {
    control,
    formState: { errors },
  } = useFormContext();
  const { field } = useController({
    control,
    name,
    defaultValue: '',
  });
  const { translateY } = useLabelAnimation({ value: field.value });

  return (
    <Styled.Container>
      <Styled.Wrapper>
        <Label label={label} translateY={translateY} multiline={multiline} />
        <Styled.Input
          editable={editable}
          maxLength={maxLength}
          multiline={multiline}
          autoCapitalize="none"
          placeholderTextColor={theme.colors.black[300]}
          secureTextEntry={secure}
          keyboardType={keyboardType}
          placeholder={placeholder}
          onChangeText={field.onChange}
          value={field.value}
        />
        <Icon secure={secure} setSecure={setSecure} icon={icon} />
      </Styled.Wrapper>
      {customError ? <CustomError error={customError} /> : <Error error={errors[name]} />}
    </Styled.Container>
  );
};

export const InputPasswordReset = ({
  placeholder,
  icon,
  label,
  editable = true,
  isPassword = false,
  multiline = false,
  maxLength,
  onChangeText,
  value,
}: IInput) => {
  const [secure, setSecure] = useState<boolean>(isPassword);
  const { translateY } = useLabelAnimation({ value: value! });
  return (
    <Styled.Container>
      <Styled.Wrapper>
        <Label label={label} translateY={translateY} multiline={multiline} />
        <Styled.Input
          editable={editable}
          maxLength={maxLength}
          multiline={multiline}
          autoCapitalize="none"
          placeholderTextColor={theme.colors.black[300]}
          secureTextEntry={secure}
          placeholder={placeholder}
          onChangeText={onChangeText}
          value={value}
        />
        <Icon secure={secure} setSecure={setSecure} icon={icon} />
      </Styled.Wrapper>
    </Styled.Container>
  );
};
