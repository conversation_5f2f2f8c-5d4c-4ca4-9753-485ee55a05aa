import { Animated } from 'react-native';

import * as Styled from '~/components/Inputs/Input/Input.styled';

export const Label = ({
  label,
  translateY,
  multiline = false,
}: {
  label?: string;
  translateY: Animated.Value;
  multiline?: boolean;
}) => {
  return (
    <>
      {label ? (
        <Animated.View
          style={[
            { position: 'absolute', left: 16 },
            multiline ? { top: 13 } : {},
            { transform: [{ translateY: translateY }] },
          ]}>
          <Styled.Label>{label}</Styled.Label>
        </Animated.View>
      ) : null}
    </>
  );
};
