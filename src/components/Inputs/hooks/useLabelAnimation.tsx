import { useEffect, useRef, useState } from 'react';
import { Animated } from 'react-native';

import { IUseLabelAnimation } from '~/components/Inputs/Input/types';

export const useLabelAnimation = ({ value }: IUseLabelAnimation) => {
  const [up, setUp] = useState<boolean>(false);

  const translateY = useRef(new Animated.Value(0)).current;

  const handleUp = () => {
    Animated.timing(translateY, {
      toValue: -24,
      duration: 500,
      useNativeDriver: true,
    }).start();
  };

  const handleDown = () => {
    Animated.timing(translateY, {
      toValue: 0,
      duration: 500,
      useNativeDriver: true,
    }).start();
  };

  useEffect(() => {
    if (value.length > 0 && !up) {
      setUp(true);
      handleUp();
    } else if (value.length === 0 && up) {
      setUp(false);
      handleDown();
    }
  }, [value]);

  return { translateY };
};
