import { TouchableOpacity } from 'react-native';

import * as Styled from './Control.styled';

import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { Typography } from '~/components/Typography/Typography';
import { IControl } from '~/pages/Onboarding/types';
import theme from '~/theme';

export const Control = ({ step, onPress, length = 3 }: IControl) => {
  return (
    <Styled.Control>
      {step >= length - 1 ? (
        <Button variant={ButtonVariant.FIFTH} size={ButtonSize.MEDIUM} onPress={onPress}>
          Wejdź
        </Button>
      ) : (
        <TouchableOpacity onPress={onPress} style={{ paddingHorizontal: 16, paddingVertical: 8 }}>
          <Typography
            fontSize={12}
            fontWeight="400"
            color={theme.colors.black[100]}
            style={{ textDecorationLine: 'underline' }}>
            Dalej
          </Typography>
        </TouchableOpacity>
      )}
    </Styled.Control>
  );
};
