import styled from '@emotion/native';

import { flexRowBetween } from '~/styles/flex.styled';
import theme from '~/theme';

export const Wrapper = styled.TouchableOpacity`
  ${flexRowBetween};
  gap: 10px;
  height: 24px;
`;

export interface IItem {
  active?: boolean;
}

export const Item = styled.View<IItem>`
  height: 4px;
  width: 25px;
  border-radius: 16px;
  background-color: ${({ active }) =>
    active ? theme.colors.purple['300'] : theme.colors.gray['900']};
`;
