import React, { FC } from 'react';

import * as Shop from './components';
import * as Styled from './ShopModal.styled';

import { ModalHeader } from '~/components/Modals/common/ModalHeader/ModalHeader';
import { IShopModalProps } from '~/components/Modals/ShopModal/types';
import * as Popup from '~/components/Popup';
import { Typography } from '~/components/Typography/Typography';
import { assets } from '~/theme/assets';

export const ShopModal: FC<IShopModalProps> = ({
  userCity,
  restShopCondition,
  onClose,
  userLocationAddresses,
  otherAddresses,
  onPhonePress,
  onLocalizationPress,
  onUserLocalizationChangePress,
}) => {
  return (
    <Popup.StaticHeight heightPercentage={0.95} onClose={() => onClose(false)}>
      <Styled.Container>
        <ModalHeader icon={assets.icons.shop_svg} title={'Sklepy objęte promocją'} />
        <Styled.LocalizationContainer>
          <Shop.Localization
            onLocalizationPress={onUserLocalizationChangePress}
            cityName={userCity}
          />
        </Styled.LocalizationContainer>

        <Styled.LocalizationContainer>
          {Object.entries(userLocationAddresses).map(([key, value]) => {
            return (
              <React.Fragment key={key}>
                <Shop.Title text={key} />
                {value.map((item, index) => {
                  return (
                    <Shop.ShopLocation
                      onLocalizationPress={onLocalizationPress}
                      onPhonePress={onPhonePress}
                      key={index}
                      data={item}
                    />
                  );
                })}
              </React.Fragment>
            );
          })}
          {restShopCondition ? (
            <Styled.RestShopWrapper>
              <Typography fontSize={24} fontWeight="600">
                Pozostałe sklepy
              </Typography>
            </Styled.RestShopWrapper>
          ) : null}
          <Styled.OthersShopsContainer>
            {Object.entries(otherAddresses).map(([key, value]) => {
              return (
                <React.Fragment key={key}>
                  <Styled.Wrapper>
                    <Shop.Title text={key} />
                    {value.map((item, index) => {
                      return (
                        <Shop.ShopLocation
                          onLocalizationPress={onLocalizationPress}
                          onPhonePress={onPhonePress}
                          key={index}
                          data={item}
                        />
                      );
                    })}
                  </Styled.Wrapper>
                </React.Fragment>
              );
            })}
          </Styled.OthersShopsContainer>
        </Styled.LocalizationContainer>
      </Styled.Container>
    </Popup.StaticHeight>
  );
};
