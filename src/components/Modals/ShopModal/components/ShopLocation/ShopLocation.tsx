import React, { FC, ReactElement } from 'react';
import { TouchableOpacity } from 'react-native';

import * as Styled from './ShopLocation.styled';

import { Icon } from '~/components/Icon/Icon';
import { IShopLocationProps } from '~/components/Modals/ShopModal/types';
import { Typography } from '~/components/Typography/Typography';
import { assets } from '~/theme/assets';

export const ShopLocation: FC<IShopLocationProps> = ({
  onPhonePress,
  onLocalizationPress,
  data,
}): ReactElement => {
  return (
    <Styled.Container>
      <Styled.AdressDetails>
        {data.street ? (
          <Typography fontSize={14} fontWeight="400">
            {`ul. ${data.street} ${data.house_number ? `nr. ${data.house_number}` : ''}`}
          </Typography>
        ) : null}
        {data.zip_code ? (
          <Typography fontSize={14} fontWeight="400">
            {`${data.zip_code} ${data.city}`}
          </Typography>
        ) : null}

        {data.phone_number1 ? (
          <Typography fontSize={14} fontWeight="400">
            {`tel. ${data.phone_number1}`}
          </Typography>
        ) : data.phone_number2 ? (
          <Typography fontSize={14} fontWeight="400">
            {`tel. ${data.phone_number2}`}
          </Typography>
        ) : null}
      </Styled.AdressDetails>
      <Styled.Icons>
        {data.phone_number1 || data.phone_number2 ? (
          <TouchableOpacity onPress={() => onPhonePress(data)} hitSlop={16}>
            <Icon iconSVG={assets.icons.phone_icon_svg} width={24} height={24} />
          </TouchableOpacity>
        ) : null}
        <TouchableOpacity onPress={() => onLocalizationPress(data)} hitSlop={16}>
          <Icon iconSVG={assets.icons.navigation_svg} width={24} height={24} />
        </TouchableOpacity>
      </Styled.Icons>
    </Styled.Container>
  );
};
