// -----interface for data (eg. mock)-----
import { IAddress } from '~/types/addresses';

// -----ShopModal.tsx-----
interface IShopModalProps {
  isOpen: boolean;
  onClose: (val: boolean) => void;
  userLocationAddresses: { [key: string]: IAddress[] };
  otherAddresses: { [key: string]: IAddress[] };
  restShopCondition: boolean;
  onLocalizationPress: (data: Partial<IAddress>) => void;
  onUserLocalizationChangePress: () => void;
  onPhonePress: (data: Partial<IAddress>) => void;
  userCity?: string | null;
}

// -----YourLocalization.tsx-----
export interface IYourLocalizationProps {
  city: string;
  onLocalizationChangePress: () => void;
}

// -----ShopLocation.tsx-----
interface IShopLocationProps {
  data: Partial<IAddress>;
  onPhonePress: (data: Partial<IAddress>) => void;
  onLocalizationPress: (data: Partial<IAddress>) => void;
}
