import { View } from 'react-native';

import * as Styled from './FavoriteModal.styled';

import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { ModalHeader } from '~/components/Modals/common/ModalHeader/ModalHeader';
import { Item } from '~/components/Modals/FavoriteModal/components/Item';
import { IBookmark } from '~/components/Modals/FavoriteModal/types';
import * as Popup from '~/components/Popup';
import { Separator } from '~/components/Separator/Separator';
import { useFavoritesModalQuery } from '~/hooks/useFavorites';
import { assets } from '~/theme/assets';
import { bookmarks } from '~/utils/constants/bookmarks';

export const FavoriteModal = () => {
  //TODO GET DATA FROM CONTEXT NOT PARAMS
  const { navigation, data, active, handlePutFavorite, handleDeleteFavorite } =
    useFavoritesModalQuery();
  //const { params, handleDelete, handleAdd, active, navigation } = useFavoritesModal();

  if (!data) return null;
  else
    return (
      <Popup.DynamicHeight onClose={() => navigation.goBack()}>
        <Styled.Wrapper>
          <ModalHeader
            icon={assets.icons.book_mark_modal_svg}
            title={data.isFavorite ? 'Przenieś do innej listy' : 'Dodaj do listy'}
          />
          <Styled.Items>
            {bookmarks.map((item: Pick<IBookmark, 'code' | 'name'>, index: number) => {
              return (
                <Item
                  {...item}
                  state={false}
                  active={active === item.code}
                  key={index}
                  onPress={() => {
                    handlePutFavorite(item.code);
                  }}
                />
              );
            })}
          </Styled.Items>
          {data.isFavorite ? (
            <Styled.Optional>
              <Separator />
              <View style={{ width: 204, alignSelf: 'center' }}>
                <Button
                  variant={ButtonVariant.FIFTH}
                  borderRadius={100}
                  size={ButtonSize.MEDIUM}
                  onPress={() => {
                    handleDeleteFavorite();
                  }}>
                  {`usuń z listy`.toUpperCase()}
                </Button>
              </View>
            </Styled.Optional>
          ) : null}
        </Styled.Wrapper>
      </Popup.DynamicHeight>
    );
};
