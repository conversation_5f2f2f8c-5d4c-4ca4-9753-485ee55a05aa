import * as Styled from './Item.styled';

import { IBookmark } from '~/components/Modals/FavoriteModal/types';
import { RadioButton } from '~/components/RadioButton/RadioButton';
import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';

export const Item = ({ name, onPress, active }: Omit<IBookmark, 'code'>) => {
  return (
    <Styled.Wrapper onPress={onPress}>
      <Typography fontSize={14} fontWeight="400" color={theme.colors.black[300]}>
        {name.toUpperCase()}
      </Typography>
      <RadioButton active={active} />
    </Styled.Wrapper>
  );
};
