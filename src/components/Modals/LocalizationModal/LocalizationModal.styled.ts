import styled from '@emotion/native';

import theme from '~/theme';

export const Container = styled.View`
  gap: 20px;
  padding: 0 16px 16px 16px;
  flex: 1;
`;

export const InputContainer = styled.View`
  border-radius: 100px;
  border: 1px solid ${theme.colors.gray[300]};
  flex-direction: row;
  padding-left: 24px;
  gap: 13px;
`;

export const Input = styled.TextInput`
  width: 80%;
  padding: 12px 12px 12px 0;
  color: ${theme.colors.black[100]};
  font-size: 14px;
`;
export const IconWrapper = styled.TouchableOpacity`
  align-items: center;
  justify-content: center;
  width: 10%;
`;
