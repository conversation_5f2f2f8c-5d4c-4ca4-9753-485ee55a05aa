import { FC, useEffect, useState } from 'react';
import { FlatList, Keyboard, Platform, TouchableOpacity, View } from 'react-native';

import * as Styled from './LocalizationModal.styled';

import { BoxBorder } from '~/components/BoxBorder/BoxBorder';
import { Icon } from '~/components/Icon/Icon';
import { Loader } from '~/components/Loader/Loader';
import { ModalHeader } from '~/components/Modals/common/ModalHeader/ModalHeader';
import { ILocalizationModalProps } from '~/components/Modals/LocalizationModal/types';
import * as Popup from '~/components/Popup';
import { Typography } from '~/components/Typography/Typography';
import { useLocalization } from '~/hooks/useLocalization';
import theme from '~/theme';
import { assets } from '~/theme/assets';

export const LocalizationModal: FC<ILocalizationModalProps> = ({ isOpen, onClose }) => {
  const [modalHeight, setModalHeight] = useState<number>(0.88);
  const { data, isFetching, onSelected, onEndReached, onChange, value } = useLocalization({
    onClose: onClose,
  });
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', keyboardDidShow);
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', keyboardDidHide);

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  const keyboardDidShow = () => {
    if (Platform.OS === 'ios') return;
    setModalHeight(0.74);
  };
  const keyboardDidHide = () => {
    setModalHeight(0.92);
  };

  return isOpen ? (
    <Popup.StaticHeight
      heightPercentage={modalHeight}
      onClose={() => onClose(false)}
      scrollable={false}>
      <Styled.Container>
        <ModalHeader
          width={30}
          height={30}
          icon={assets.icons.map_svg}
          title={'Wybierz Lokalizację'}
          uppercase={false}
        />
        <Styled.InputContainer>
          <Styled.Input
            autoCapitalize="none"
            onChangeText={onChange}
            value={value}
            placeholderTextColor={theme.colors.gray['700']}
            placeholder={'Wyszukaj miasto'}
          />
          <Styled.IconWrapper onPress={() => {}}>
            <Icon iconSVG={assets.icons.search_svg} width={24} height={24} />
          </Styled.IconWrapper>
        </Styled.InputContainer>
        <View>
          <FlatList
            ListFooterComponent={() => {
              if (isFetching) return <Loader />;
              return null;
            }}
            initialNumToRender={10}
            onEndReachedThreshold={0.2}
            onEndReached={() => onEndReached(isFetching, data?.next)}
            style={{ height: 430 }}
            contentContainerStyle={Platform.OS === 'ios' ? { paddingBottom: 120 } : {}}
            keyExtractor={(item, index) => index.toString()}
            data={data?.results}
            ListHeaderComponent={() => (
              <TouchableOpacity onPress={() => onSelected({ id: null, name: null })}>
                <BoxBorder borderBottom={true} borderTop={false}>
                  <View style={{ paddingHorizontal: 10, paddingVertical: 16 }}>
                    <Typography fontSize={14} fontWeight="600">
                      Cała Polska
                    </Typography>
                  </View>
                </BoxBorder>
              </TouchableOpacity>
            )}
            renderItem={({ item }) => {
              return (
                <TouchableOpacity onPress={() => onSelected(item)}>
                  <BoxBorder borderBottom={true} borderTop={false}>
                    <View style={{ paddingHorizontal: 10, paddingVertical: 16 }}>
                      <Typography fontSize={14} fontWeight="400">
                        {item.name ? item.name : ''}
                      </Typography>
                    </View>
                  </BoxBorder>
                </TouchableOpacity>
              );
            }}
          />
        </View>
      </Styled.Container>
    </Popup.StaticHeight>
  ) : null;
};
