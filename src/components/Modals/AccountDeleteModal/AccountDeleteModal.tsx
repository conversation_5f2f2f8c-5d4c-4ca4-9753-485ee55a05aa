import React from 'react';

import * as Styled from './AccountDeleteModal.styled';

import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { Loader } from '~/components/Loader/Loader';
import { ModalHeader } from '~/components/Modals/common/ModalHeader/ModalHeader';
import * as Popup from '~/components/Popup';
import { Typography } from '~/components/Typography/Typography';
import { useAccountDelete } from '~/hooks/useAccountDelete';
import { useNavigate } from '~/hooks/useNavigate';
import theme from '~/theme';
import { assets } from '~/theme/assets';

export const AccountDeleteModal = () => {
  const { navigation } = useNavigate();
  const { handleDelete, loading, status, handleBack } = useAccountDelete();

  return (
    <Popup.DynamicHeight onClose={handleBack}>
      <Styled.Wrapper>
        <ModalHeader icon={assets.icons.map_svg} title={'Usuń konto'} />
        {loading ? <Loader /> : null}
        {!loading && !status ? (
          <>
            <Typography
              fontSize={12}
              fontWeight="300"
              color={'black'}
              style={{ textAlign: 'center' }}>
              <Typography fontSize={12} fontWeight="600">
                UWAGA!{' '}
              </Typography>
              Dostęp do aplikacji zostanie natychmiast zablokowany. Twoje dane zostaną trwale
              usunięte w ciągu 48 h. Zmiany są nieodwracalne. Potwierdź usunięcie konta
            </Typography>
            <Typography fontSize={14} fontWeight="600" color={'black'}>
              Potwierdź usunięcie konta
            </Typography>
            <Styled.ButtonWrapper>
              <Button
                variant={ButtonVariant.FOURTH}
                size={ButtonSize.MEDIUM}
                borderRadius={24}
                typographyStyles={{
                  fontSize: 12,
                  fontWeight: '600',
                  color: theme.colors.purple['100'],
                }}
                onPress={() => navigation.goBack()}>
                {`Anuluj`.toUpperCase()}
              </Button>
              <Button
                onPress={() => handleDelete()}
                variant={ButtonVariant.FIFTH}
                typographyStyles={{
                  fontSize: 12,
                  fontWeight: '600',
                  color: theme.colors.white['100'],
                }}
                size={ButtonSize.MEDIUM}
                borderRadius={24}>
                {`Usuń`.toUpperCase()}
              </Button>
            </Styled.ButtonWrapper>
          </>
        ) : null}
        {status ? (
          <>
            <Styled.Wrapper>
              <Typography fontSize={16} fontWeight="600" color={theme.colors.purple[100]}>
                {status.message}
              </Typography>
            </Styled.Wrapper>
            <Button
              onPress={handleBack}
              variant={ButtonVariant.FIFTH}
              typographyStyles={{
                fontSize: 12,
                fontWeight: '600',
                color: theme.colors.white['100'],
              }}
              size={ButtonSize.MEDIUM}
              borderRadius={24}>
              {`${status.status === 'success' ? 'Wyloguj' : 'Wróć'}`.toUpperCase()}
            </Button>
          </>
        ) : null}
      </Styled.Wrapper>
    </Popup.DynamicHeight>
  );
};
