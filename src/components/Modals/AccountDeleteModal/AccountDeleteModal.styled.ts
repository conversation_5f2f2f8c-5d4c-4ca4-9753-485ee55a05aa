import styled from '@emotion/native';

import { LAYOUT_PADDING } from '~/helpers/layoutPadding';
import { flexColCenter, flexRowCenter } from '~/styles/flex.styled';

export const Wrapper = styled.View`
  ${flexColCenter};
  gap: 16px;
  padding: 0 ${`${LAYOUT_PADDING}px`};
`;

export const ButtonWrapper = styled.View`
  ${flexRowCenter};
  gap: 16px;
  flex: 1;
  width: 150px;
  margin-top: 20px;
`;
