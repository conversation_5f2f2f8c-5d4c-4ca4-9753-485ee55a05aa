import { useFocusEffect } from '@react-navigation/native';
import { useCallback, useContext } from 'react';

import * as Styled from './ContactModal.styled';

import { Typography } from '~/components/Typography/Typography';
import { contactModalContext } from '~/context/contactModalContext';
import theme from '~/theme';

export const ContactModal = () => {
  const { show, setShow } = useContext(contactModalContext);

  useFocusEffect(
    useCallback(() => {
      if (show) {
        setTimeout(() => {
          setShow(false);
        }, 6000);
      }
    }, [show]),
  );

  if (!show) return null;
  return (
    <Styled.Wrapper>
      <Styled.Content>
        <Typography
          fontSize={14}
          fontWeight="400"
          color={theme.colors.white[100]}
          style={{ textAlign: 'center' }}>
          {'Wiadomość została wysłana'}
        </Typography>
      </Styled.Content>
    </Styled.Wrapper>
  );
};
