import styled from '@emotion/native';

export const Container = styled.View`
  padding: 0 20px 10px 20px;
  gap: 20px;
`;
export const Content = styled.View`
  flex-direction: column;
  gap: 35px;
  align-items: center;
`;
export const DescWrapper = styled.View``;
export const FirstLineWrapper = styled.View`
  flex-direction: row;
  align-items: center;
  gap: 4px;
`;
export const SecondLineWrapper = styled.View``;
export const InputWrapper = styled.View`
  width: 295px;
`;
