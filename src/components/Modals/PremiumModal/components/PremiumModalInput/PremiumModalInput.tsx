import { FormProvider, UseFormReturn } from 'react-hook-form';

import * as Styled from './PremiumModalInput.styled';

import { Button } from '~/components/Button/Button';
import { ButtonVariant } from '~/components/Button/enum';
import { Loader } from '~/components/Loader/Loader';
import { PremiumInput } from '~/components/Modals/PremiumModal/components/PremiumInput/PremiumInput';
import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';
import { TForm } from '~/validation/premiumCode/types';
import { IStatus } from '~/types/errors';

interface Props {
  onSubmit: (data: TForm) => void;
  loading: boolean;
  form: UseFormReturn<TForm, any, undefined>;
  status: IStatus | undefined;
}

export const PremiumModalInput = ({ onSubmit, loading, form, status }: Props) => {
  return (
    <Styled.Container>
      {loading ? <Loader /> : null}
      {!loading && status?.status !== 'success' ? (
        <Styled.InputWrapper>
          <FormProvider {...form}>
            <PremiumInput />
            <Styled.ButtonWrapper>
              <Button
                typographyStyles={{
                  fontSize: 14,
                  fontWeight: '600',
                  color: theme.colors.purple['100'],
                }}
                variant={ButtonVariant.FOURTH}
                borderRadius={100}
                onPress={form.handleSubmit(onSubmit)}>
                AKTYWUJ
              </Button>
            </Styled.ButtonWrapper>
          </FormProvider>
        </Styled.InputWrapper>
      ) : null}
      {status ? (
        <Typography
          fontSize={20}
          fontWeight="600"
          color={status.status === 'error' ? 'red' : theme.colors.purple[100]}
          style={{ textAlign: 'center', paddingTop: 12 }}>
          {status.message}
        </Typography>
      ) : null}
    </Styled.Container>
  );
};
