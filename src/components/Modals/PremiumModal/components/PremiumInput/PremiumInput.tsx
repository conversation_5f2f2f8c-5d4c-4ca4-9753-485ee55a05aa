import React, { useEffect, useState } from 'react';
import { useController, useFormContext } from 'react-hook-form';

import * as Styled from './PremiumInput.styled';

import theme from '~/theme';

export const PremiumInput = () => {
  const [active, setActive] = useState<boolean>(false);
  const { control } = useFormContext();
  const { field } = useController({
    control,
    name: 'code',
    defaultValue: '',
  });

  useEffect(() => {
    if (field.value === '') {
      setActive(false);
    } else {
      setActive(true);
    }
  }, [field.value]);

  return (
    <Styled.Input
      textAlign={active ? 'center' : 'left'}
      autoCapitalize={'characters'}
      active={active}
      maxLength={16}
      onChangeText={field.onChange}
      value={field.value}
      placeholderTextColor={theme.colors.gray['700']}
      placeholder={'Wpisz kod'}
    />
  );
};
