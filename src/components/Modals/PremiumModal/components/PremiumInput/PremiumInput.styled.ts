import styled, { css } from '@emotion/native/dist/emotion-native.cjs';

import theme from '~/theme';

export const Input = styled.TextInput<{ active: boolean }>`
  padding-left: 16px;
  height: 50px;
  font-family: ${theme.fonts.primary['400']};
  color: ${theme.colors.black['100']};
  width: 60%;
  ${({ active }) =>
    active
      ? css`
          font-size: 18px;
        `
      : css`
          font-size: 14px;
        `};
`;
