import { FC, useEffect, useState } from 'react';
import { KeyboardAvoidingView, Linking, Platform, View } from 'react-native';

import * as Styled from './PremiumModal.styled';

import { Button } from '~/components/Button/Button';
import { ButtonVariant } from '~/components/Button/enum';
import { ModalHeader } from '~/components/Modals/common/ModalHeader/ModalHeader';
import { PremiumModalInput } from '~/components/Modals/PremiumModal/components/PremiumModalInput/PremiumModalInput';
import * as Popup from '~/components/Popup';
import { Typography } from '~/components/Typography/Typography';
import { getBuyPremiumUrl } from '~/redux/api/buyPremium';
import theme from '~/theme';
import { assets } from '~/theme/assets';
import { useAppSelector } from '~/redux/store';
import KeyboardManager from 'react-native-keyboard-manager';
import { usePremium } from '~/hooks/usePremium';

export const PremiumModal: FC<IPremiumModalProps> = ({ onClose }) => {
  const [buyLink, setBuyLink] = useState<string | undefined>();
  const { me } = useAppSelector((state) => state.user);
  const { loading, status, onSubmit, form } = usePremium();

  useEffect(() => {
    getBuyPremiumUrl()
      .then((response) => {
        setBuyLink(response.data.value);
      })
      .catch((error) => console.log(error));
  }, []);

  useEffect(() => {
    const disableKeyboardManager = () => {
      if (Platform.OS === 'ios') {
        KeyboardManager.setEnable(false);
      }
    };

    const enableKeyboardManager = () => {
      if (Platform.OS === 'ios') {
        KeyboardManager.setEnable(true);
      }
    };

    disableKeyboardManager();

    return () => {
      enableKeyboardManager();
    };
  }, []);

  const handleBuyPremium = () => {
    const link = `${buyLink}?email=${me?.email}`;
    Linking.openURL(link);
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      style={{ flex: 1 }}>
      <Popup.DynamicHeight
        onClose={() => {
          onClose(false);
        }}>
        <Styled.Container>
          <ModalHeader
            width={32}
            height={32}
            icon={assets.icons.modal_star_svg}
            title="Konto premium"
            uppercase={false}
          />
          <Styled.Content>
            <Styled.DescWrapper>
              <Styled.FirstLineWrapper>
                <Typography fontSize={14} fontWeight="400">
                  Aktywuj konto
                </Typography>
                <Typography fontSize={14} fontWeight="600">
                  PREMIUM
                </Typography>
              </Styled.FirstLineWrapper>
              <Styled.SecondLineWrapper />
              <Typography fontSize={14} fontWeight="400" style={{ textAlign: 'center' }}>
                i zyskaj jeszcze więcej
              </Typography>
            </Styled.DescWrapper>
            <Styled.InputWrapper>
              <PremiumModalInput
                form={form}
                loading={loading}
                status={status}
                onSubmit={onSubmit}
              />
            </Styled.InputWrapper>
          </Styled.Content>
          {buyLink && status?.status !== 'success' ? (
            <View style={{ justifyContent: 'center', alignItems: 'center', gap: 16 }}>
              <Typography
                fontSize={16}
                color={theme.colors.gray[100]}
                style={{ textAlign: 'center' }}>
                lub
              </Typography>
              <Button
                variant={ButtonVariant.FIFTH}
                onPress={handleBuyPremium}
                style={{ maxWidth: 220 }}>
                {'Wykup dostęp'.toUpperCase()}
              </Button>
            </View>
          ) : null}
        </Styled.Container>
      </Popup.DynamicHeight>
    </KeyboardAvoidingView>
  );
};
