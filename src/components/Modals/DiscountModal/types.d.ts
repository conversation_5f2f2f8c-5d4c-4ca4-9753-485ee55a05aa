import { TDiscountModal } from '~/routes/pageTypes';
import { IDiscountCode } from '~/types/discount';

export interface IDiscountModal extends IDiscountCode {
  data: TDiscountModal;
  isOpen: boolean;
  onClose: (val: boolean) => void;
}

export interface IModal extends IDiscountCode {
  buttonText?: string | null;
  instruction?: string | null;
  phoneNumber?: string | null;
  affiliate_link?: string | null;
  buttonLink?: string | null;
}
