import { ModalHeaderGif } from '../common/ModalHeader/ModalHeaderGif';

import * as Styled from './DiscountModal.styled';

import { BarModal } from '~/components/Modals/DiscountModal/components/BarModal';
import { CopyModal } from '~/components/Modals/DiscountModal/components/CopyModal';
import { InstructionModal } from '~/components/Modals/DiscountModal/components/InstructionModal';
import { PhoneModal } from '~/components/Modals/DiscountModal/components/PhoneModal';
import { QrModal } from '~/components/Modals/DiscountModal/components/QrModal';
import { IDiscountModal } from '~/components/Modals/DiscountModal/types.d';
import * as Popup from '~/components/Popup';
import { Typography } from '~/components/Typography/Typography';
import { DiscountTypeEnum } from '~/enums/shared';

export const DiscountModal = ({ onClose, data, code }: IDiscountModal) => {
  //const { data: code, isLoading } = useGetDiscountCodeQuery({ id: data.discount_id });

  const text = {
    title: data.instruction_title ? data.instruction_title : 'Twój kod rabatowy',
    value: data.discount_value,
    description: data.info ? data.info : 'Kod obowiązuje na wszystkie produkty nieprzecenione.',
    bottomText: 'Pokaż ekran telefonu sprzedawcy',
    buttonText: data.button_text ? data.button_text : '',
  };

  //if(!code) return null;

  const renderModal = {
    [DiscountTypeEnum.Bar]: <BarModal code={code} />,
    [DiscountTypeEnum.Txt]: (
      <CopyModal
        brand_id={data.brand_id}
        object_id={data.object_id}
        object_type={data.object_type}
        code={code}
        buttonText={data.button_text}
        instruction={data.instruction}
        affiliate_link={data.affiliate_link}
        buttonLink={data.button_url}
      />
    ),
    [DiscountTypeEnum.Cal]: (
      <PhoneModal
        code={code}
        buttonText={data.button_text}
        instruction={data.instruction}
        phoneNumber={data.phone_number}
      />
    ),
    [DiscountTypeEnum.Qrc]: <QrModal code={code} instruction={data.instruction} />,
    [DiscountTypeEnum.Ins]: (
      <InstructionModal
        code={data.instruction ? data.instruction : ''}
        buttonText={data.button_text}
        affiliate_link={data.affiliate_link}
        buttonLink={data.button_url}
      />
    ),
  };

  return (
    <Popup.DynamicHeight onClose={() => onClose(false)}>
      <Styled.Wrapper>
        <ModalHeaderGif title={text.title} />
        <>
          {code ? (
            <>
              <Styled.ValueWrapper>
                <Typography fontSize={36} fontWeight="700">
                  {text.value}
                </Typography>
                {/* <Typography
                  fontSize={14}
                  style={{ textAlign: 'center', maxWidth: 310 }}
                  fontWeight="400">
                  {text.description}
                </Typography> */}
              </Styled.ValueWrapper>
              <Styled.Content>
                {code ? <>{renderModal[data.discount_type]}</> : null}
              </Styled.Content>
            </>
          ) : (
            <Typography
              fontSize={14}
              style={{ textAlign: 'center', maxWidth: 310 }}
              fontWeight="400">
              {'Brak kodu rabatowego do wyświetlenia. Spróbuj ponownie później.'}
            </Typography>
          )}
        </>
      </Styled.Wrapper>
    </Popup.DynamicHeight>
  );
};
