import React, { useState } from 'react';

import { Button } from '~/components/Button/Button';
import { ButtonVariant } from '~/components/Button/enum';
import * as Styled from '~/components/Modals/DiscountModal/DiscountModal.styled';
import { IModal } from '~/components/Modals/DiscountModal/types';
import { Typography } from '~/components/Typography/Typography';
import { useLinking } from '~/hooks/useLinking';
import theme from '~/theme';

/**
 * @description
 * ```tsx
 * Linking.openURL(`tel:${phone}`).then();
 * ```tsx
 * Ta funkcja odpowiada za przekierowanie do aplikacji telefonu i wybranie numeru.
 */

export const PhoneModal = ({ code, buttonText, instruction, phoneNumber }: IModal) => {
  const { openPhone } = useLinking();
  const [error, setError] = useState({ isError: false, msg: '' });

  const handlePress = () => {
    if (phoneNumber) {
      openPhone(phoneNumber)
        .then()
        .catch(() => {
          setError({ isError: true, msg: 'Nie można otworzyć aplikacji telefonu' });
        });
    } else {
      setError({ isError: true, msg: 'Brak numeru tel. skontaktuj się z supportem' });
    }
  };
  return (
    <Styled.ContentWrapper>
      <Typography fontSize={20} fontWeight="600" color={theme.colors.purple[100]}>
        {code}
      </Typography>
      <Typography
        fontSize={12}
        style={{ textAlign: 'center', maxWidth: 250 }}
        fontWeight="400"
        color={theme.colors.gray['800']}>
        {instruction ? instruction : 'Zadzwoń na infolinię i podaj powyższy kod autoryzacyjny'}
      </Typography>
      <Button
        typographyStyles={{ color: theme.colors.white['100'], fontSize: 14, fontWeight: '600' }}
        variant={ButtonVariant.SECONDARY}
        backgroundColor={theme.colors.purple['100']}
        onPress={handlePress}>
        {`${buttonText ? buttonText : 'zadzwoń'}`.toUpperCase()}
      </Button>
      {error.isError ? (
        <Typography
          fontSize={12}
          style={{ textAlign: 'center', maxWidth: 250 }}
          fontWeight="400"
          color={theme.colors.purple['800']}>
          {error.msg}
        </Typography>
      ) : null}
    </Styled.ContentWrapper>
  );
};
