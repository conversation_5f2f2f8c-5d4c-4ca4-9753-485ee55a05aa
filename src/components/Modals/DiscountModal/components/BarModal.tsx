import Barcode from '@kichiyaki/react-native-barcode-generator';
import QRCode from 'react-native-qrcode-svg';

import * as Styled from './BardModal.styled';

import { IModal } from '~/components/Modals/DiscountModal/types';
import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';

export const BarModal = ({ code, instruction }: IModal) => {
  const BARCODE_FORMAT = 'EAN13';
  // TODO: change to code from props
  //const BARCODE = '5901234123457';
  const checksum = (number: string) => {
    const res = number
      .substr(0, 12)
      .split('')
      .map((n) => +n)
      .reduce((sum, a, idx) => (idx % 2 ? sum + a * 3 : sum + a), 0);

    return (10 - (res % 10)) % 10;
  };
  const valid = (number: string) => {
    return number.search(/^[0-9]{13}$/) !== -1 && +number[12] === checksum(number);
  };

  const isEAN13 = (number: string) => {
    return valid(number);
  };

  return (
    <Styled.Container>
      <Styled.BarContainer>
        {isEAN13(code) ? (
          <Barcode
            textStyle={{ color: theme.colors.black['300'] }}
            format={BARCODE_FORMAT}
            value={code}
            text={code}
            width={2}
            maxWidth={300}
            height={42}
          />
        ) : (
          <QRCode value={code} size={200} logoBackgroundColor="transparent" />
        )}
      </Styled.BarContainer>
      <Typography
        fontSize={12}
        style={{ textAlign: 'center' }}
        fontWeight="400"
        color={theme.colors.gray['800']}>
        {instruction ? instruction : 'Pokaż ekran telefonu sprzedawcy'}
      </Typography>
    </Styled.Container>
  );
};
