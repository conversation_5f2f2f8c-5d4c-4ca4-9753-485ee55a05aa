import { View } from 'react-native';

import { Button } from '~/components/Button/Button';
import { ButtonVariant } from '~/components/Button/enum';
import * as Styled from '~/components/Modals/DiscountModal/DiscountModal.styled';
import { IModal } from '~/components/Modals/DiscountModal/types';
import { Typography } from '~/components/Typography/Typography';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useRedirectionUrl } from '~/events/hooks';
import { useLinking } from '~/hooks/useLinking';
import { useNavigate } from '~/hooks/useNavigate';
import theme from '~/theme';

/**
 * @description
 * Use effect z setTimeout służy do zamknięcia modalu z informacją o poprawności skopiowania.
 */
type Props = {
  brand_id?: number;
  object_id?: number;
  object_type?: ObjectTypeEnum;
};
export const InstructionModal = ({
  code,
  buttonText,
  affiliate_link,
  buttonLink,
  brand_id,
  object_id,
  object_type,
}: IModal & Props) => {
  //----EVENTS----
  //----EVENTS----
  const _fnUrlEventButtonLink = useRedirectionUrl(() => openUrl(buttonLink));
  const _fnUrlEventAffiliateLink = useRedirectionUrl(() => openUrl(affiliate_link));
  //--X--EVENTS--X--
  //--X--EVENTS--X--
  const { navigation } = useNavigate();
  const { openUrl } = useLinking();
  const handlePress = () => {
    if (affiliate_link) {
      _fnUrlEventAffiliateLink(brand_id, object_id, object_type, affiliate_link);
    } else if (buttonLink) {
      _fnUrlEventButtonLink(brand_id, object_id, object_type, buttonLink);
    } else {
      navigation.goBack();
    }
  };
  return (
    <Styled.ContentWrapperView>
      {code !== null ? (
        <View
          style={{
            alignItems: 'center',
            gap: 15,
            maxWidth: 358,
            paddingHorizontal: 16,
          }}>
          <Styled.Instruction>
            <Typography
              fontSize={12}
              fontWeight="400"
              color={theme.colors.purple[100]}
              style={{ textAlign: 'center' }}>
              {code}
            </Typography>
          </Styled.Instruction>
        </View>
      ) : null}
      {affiliate_link || buttonLink ? (
        <Button
          style={{ width: 200 }}
          typographyStyles={{ color: theme.colors.white['100'], fontSize: 14, fontWeight: '600' }}
          variant={ButtonVariant.SECONDARY}
          backgroundColor={theme.colors.purple['100']}
          onPress={handlePress}>
          {`${buttonText ? buttonText : 'przejdź do sklepu'}`.toUpperCase()}
        </Button>
      ) : null}
    </Styled.ContentWrapperView>
  );
};
