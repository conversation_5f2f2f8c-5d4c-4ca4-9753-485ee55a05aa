import QRCode from 'react-native-qrcode-svg';

import * as Styled from './QrModal.styled';

import { IModal } from '~/components/Modals/DiscountModal/types';
import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';

export const QrModal = ({ code, instruction }: IModal) => {
  return (
    <Styled.Container>
      <Styled.QrContainer>
        <QRCode value={code} size={200} logoBackgroundColor="transparent" />
      </Styled.QrContainer>
      <Typography
        fontSize={12}
        style={{ textAlign: 'center', maxWidth: 250 }}
        fontWeight="400"
        color={theme.colors.gray['800']}>
        {instruction ? instruction : 'Pokaż ekran telefonu sprzedawcy'}
      </Typography>
    </Styled.Container>
  );
};
