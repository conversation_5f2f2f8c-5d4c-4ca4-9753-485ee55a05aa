import Clipboard from '@react-native-clipboard/clipboard';
import { useEffect, useState } from 'react';

import { Button } from '~/components/Button/Button';
import { ButtonVariant } from '~/components/Button/enum';
import { Icon } from '~/components/Icon/Icon';
import * as Styled from '~/components/Modals/DiscountModal/DiscountModal.styled';
import { IModal } from '~/components/Modals/DiscountModal/types';
import { Typography } from '~/components/Typography/Typography';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { useRedirectionUrl } from '~/events/hooks';
import { useCopyCodeClipboard } from '~/events/hooks/useCopyCodeClipboard';
import { useLinking } from '~/hooks/useLinking';
import { useNavigate } from '~/hooks/useNavigate';
import theme from '~/theme';
import { assets } from '~/theme/assets';

/**
 * @description
 * Use effect z setTimeout służy do zamknięcia modalu z informacją o poprawności skopiowania.
 */
type Props = {
  brand_id?: number;
  object_id?: number;
  object_type?: ObjectTypeEnum;
};
export const CopyModal = ({
  code,
  buttonText,
  instruction,
  affiliate_link,
  buttonLink,
  brand_id,
  object_id,
  object_type,
}: IModal & Props) => {
  //----EVENTS----
  const _fnUrlEvent = useRedirectionUrl((link) => openUrl(link));
  const _fnCopyEvent = useCopyCodeClipboard();
  //--X--EVENTS--X--
  const { openUrl } = useLinking();
  const { navigation } = useNavigate();
  const [copyModal, setCopyModal] = useState<boolean>(false);

  useEffect(() => {
    if (copyModal) {
      setTimeout(() => {
        setCopyModal(false);
      }, 2000);
    }
  }, [copyModal]);

  const handlePress = () => {
    if (affiliate_link) {
      _fnUrlEvent(brand_id, object_id, object_type, affiliate_link);
    } else if (buttonLink) {
      _fnUrlEvent(brand_id, object_id, object_type, buttonLink);
    } else {
      navigation.goBack();
    }
  };

  return (
    <Styled.ContentWrapper>
      {code !== null ? (
        <>
          <Styled.Copy
            onPress={() => {
              setCopyModal(true);
              Clipboard.setString(code);
              _fnCopyEvent(brand_id, object_id, object_type);
            }}>
            {copyModal ? (
              <Styled.CopyModal>
                <Typography fontSize={14} color={theme.colors.white[100]}>
                  Kod został skopiowany!
                </Typography>
              </Styled.CopyModal>
            ) : null}
            <Typography fontSize={16} fontWeight="600" color={theme.colors.purple[100]}>
              {code}
            </Typography>
            <Icon iconSVG={assets.icons.copy_svg} height={32} width={32} />
          </Styled.Copy>
          <Typography
            fontSize={12}
            style={{ textAlign: 'center', maxWidth: 250 }}
            fontWeight="400"
            color={theme.colors.gray['800']}>
            {instruction ? instruction : 'Skopiuj powyższy kod i wklej go w koszyku'}
          </Typography>
        </>
      ) : null}
      {affiliate_link || buttonLink ? (
        <Button
          typographyStyles={{ color: theme.colors.white['100'], fontSize: 14, fontWeight: '600' }}
          variant={ButtonVariant.SECONDARY}
          backgroundColor={theme.colors.purple['100']}
          onPress={handlePress}>
          {`${buttonText ? buttonText : 'przejdź do sklepu'}`.toUpperCase()}
        </Button>
      ) : null}
    </Styled.ContentWrapper>
  );
};
