import styled from '@emotion/native';

import theme from '~/theme';

export const Wrapper = styled.View`
  flex: 1;
  align-items: center;
  gap: 20px;
`;
export const ValueWrapper = styled.View`
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
`;
export const ButtonWrapper = styled.View`
  width: 260px;
`;

export const Content = styled.View``;

export const ContentWrapper = styled.View`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  gap: 22px;
  max-width: 300px;
`;
export const ContentWrapperView = styled.View`
  align-items: center;
  justify-content: space-between;
  gap: 22px;
`;
export const Instruction = styled.View`
  flex-direction: row;
`;
export const Copy = styled.TouchableOpacity`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-evenly;
  position: relative;

  gap: 16px;
  border-radius: 300px;
  border: 1px solid ${theme.colors.gray['1000']};
  padding: 10px 32px;
`;

export const CopyModal = styled.View`
  height: 32px;
  position: absolute;
  top: -40px;
  left: 0;
  right: 0;
  background-color: ${theme.colors.purple[100]};
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  border-radius: 300px;
`;
