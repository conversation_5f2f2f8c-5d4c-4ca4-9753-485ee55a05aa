import { FC } from 'react';

import * as Styled from './Localization.styled';
import { ILocalizationProps } from './types';

import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { Typography } from '~/components/Typography/Typography';

export const Localization: FC<ILocalizationProps> = ({ onLocalizationPress, cityName }) => {
  return (
    <Styled.Container>
      <Styled.TextWrapper>
        <Typography fontSize={14} fontWeight="400">
          Moja lokalizacja:
        </Typography>
        <Typography fontSize={14} fontWeight="600">
          {cityName ? cityName : 'Cała Polska'}
        </Typography>
      </Styled.TextWrapper>
      <Styled.ButtonWrapper>
        <Button
          variant={ButtonVariant.FOURTH}
          size={ButtonSize.SMALL}
          onPress={onLocalizationPress}>
          ZMIEŃ
        </Button>
      </Styled.ButtonWrapper>
    </Styled.Container>
  );
};
