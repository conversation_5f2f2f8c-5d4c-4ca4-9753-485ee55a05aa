import styled from '@emotion/native';

import { shadow } from '~/theme/shadows';

export const Container = styled.View`
  ${shadow.gray['100']}
  width: 100%;
  flex-direction: row;
  justify-content: space-between;
  padding: 10px 10px 10px 28px;
  align-items: center;
  border-radius: 10px;
`;

export const TextWrapper = styled.View`
  flex-direction: column;
`;
export const ButtonWrapper = styled.View`
  width: 120px;
`;
