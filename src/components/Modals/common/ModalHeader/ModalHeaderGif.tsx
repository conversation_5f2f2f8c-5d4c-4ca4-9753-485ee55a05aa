import { FC } from 'react';
import { Image } from 'react-native';

import * as Styled from './ModalHeader.styled';

import { IModalHeaderProps } from '~/components/Modals/common/ModalHeader/types';
import { Typography } from '~/components/Typography/Typography';
import { assets } from '~/theme/assets';

export const ModalHeaderGif: FC<Pick<IModalHeaderProps, 'uppercase' | 'title'>> = ({
  title,
  uppercase = false,
}) => {
  return (
    <Styled.Container style={{ gap: 8 }}>
      {/* <Styled.IconWrapper> */}
      <Image
        source={assets.gif.animated_hp_gif}
        style={{ width: '100%', height: 86 }}
        resizeMode={'contain'}
        testID="default-image"
      />
      {/* </Styled.IconWrapper> */}

      {title ? (
        <Typography fontSize={24} fontWeight="600" style={{ textAlign: 'center' }}>
          {uppercase ? title.toUpperCase() : title}
        </Typography>
      ) : null}
    </Styled.Container>
  );
};
