import React, { FC } from 'react';

import * as Styled from './ModalHeader.styled';

import { Icon } from '~/components/Icon/Icon';
import { IModalHeaderProps } from '~/components/Modals/common/ModalHeader/types';
import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';

export const ModalHeader: FC<IModalHeaderProps> = ({
  width = 39,
  height = 39,
  icon,
  title,
  uppercase = false,
}) => {
  return (
    <Styled.Container>
      <Styled.IconWrapper>
        <Icon
          iconSVG={icon}
          width={width}
          height={height}
          fill={theme.colors.white[100]}
          isFill={true}
        />
      </Styled.IconWrapper>

      {title ? (
        <Typography fontSize={24} fontWeight="600" style={{ textAlign: 'center' }}>
          {uppercase ? title.toUpperCase() : title}
        </Typography>
      ) : null}
    </Styled.Container>
  );
};
