import Markdown, { MarkdownIt } from '@jonasmerlin/react-native-markdown-display';
import React from 'react';

import * as Styled from './NotificationModal.styled';

import { ModalHeader } from '~/components/Modals/common/ModalHeader/ModalHeader';
import { Label } from '~/components/Notification/Label/Label';
import * as Popup from '~/components/Popup';
import { Typography } from '~/components/Typography/Typography';
import { useNavigate } from '~/hooks/useNavigate';
import { markdownStyles } from '~/styles/markdown.styled';
import theme from '~/theme';
import { assets } from '~/theme/assets';
import { INotification } from '~/types/notification';

export const NotificationModal = ({ text, type, title }: Omit<INotification, 'created_at'>) => {
  const { navigation } = useNavigate();

  return (
    <Popup.DynamicHeight onClose={() => navigation.goBack()}>
      <Styled.Wrapper>
        <ModalHeader icon={assets.icons.notification_modal_svg} title={''} />
        <Typography
          fontSize={20}
          fontWeight="600"
          color={theme.colors.black[100]}
          style={{ textAlign: 'center' }}>
          {title}
        </Typography>
        <Label type={type} />
        <Markdown
          style={markdownStyles}
          markdownit={MarkdownIt({ typographer: true }).disable(['image'])}>
          {text}
        </Markdown>
      </Styled.Wrapper>
    </Popup.DynamicHeight>
  );
};
