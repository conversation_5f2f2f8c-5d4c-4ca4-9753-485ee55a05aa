import React from 'react';

import * as Styled from './PremiumDeleteModal.styled';

import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { Loader } from '~/components/Loader/Loader';
import { ModalHeader } from '~/components/Modals/common/ModalHeader/ModalHeader';
import * as Popup from '~/components/Popup';
import { Typography } from '~/components/Typography/Typography';
import { SERVER_URL } from '~/config.api';
import { useNavigate } from '~/hooks/useNavigate';
import { getPremium } from '~/redux/reducers/user/thunks';
import { useAppDispatch, useAppSelector } from '~/redux/store';
import theme from '~/theme';
import { assets } from '~/theme/assets';
import { IStatus } from '~/types/errors';
import axios from '~/utils/axios.config';

export const PremiumDeleteModal = () => {
  const { navigation } = useNavigate();
  const dispatch = useAppDispatch();
  const { token } = useAppSelector((state) => state.user);
  const [loading, setLoading] = React.useState<boolean>(false);
  const [status, setStatus] = React.useState<IStatus | undefined>(undefined);

  const handleDelete = () => {
    setLoading(true);
    axios({
      method: 'DELETE',
      url: `${SERVER_URL}/content/premium/`,
      headers: {
        Accept: 'application/json',
        Authorization: `Bearer ${token?.access}`,
      },
    })
      .then(() => {
        dispatch(getPremium({ token: token?.access! })).catch(() => {});
        setStatus({
          status: 'success',
          message: 'Kod został usunięty',
        });
      })
      .catch(() => {
        setStatus({
          status: 'error',
          message: 'Wystąpił błąd podczas usuwania kodu',
        });
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <Popup.DynamicHeight onClose={() => navigation.goBack()}>
      <Styled.Wrapper>
        <ModalHeader icon={assets.icons.star_svg} title={'Usuń kod premium'} uppercase={false} />
        {loading ? <Loader /> : null}
        {!loading && status?.status !== 'success' ? (
          <>
            <Typography fontSize={14} fontWeight="600" color={'black'}>
              Czy na pewno chcesz usunąć swój kod premium?
            </Typography>
            <Styled.ButtonWrapper>
              <Button
                variant={ButtonVariant.FOURTH}
                size={ButtonSize.SMALL}
                borderRadius={24}
                onPress={() => navigation.goBack()}>
                {`Anuluj`.toUpperCase()}
              </Button>
              <Button
                variant={ButtonVariant.FIFTH}
                size={ButtonSize.SMALL}
                borderRadius={24}
                onPress={handleDelete}>
                {`Usuń`.toUpperCase()}
              </Button>
            </Styled.ButtonWrapper>
          </>
        ) : null}
        {status ? (
          <Typography
            fontSize={20}
            fontWeight="600"
            color={status.status === 'error' ? 'red' : theme.colors.purple[100]}
            style={{ textAlign: 'center', paddingTop: 12 }}>
            {status.message}
          </Typography>
        ) : null}
      </Styled.Wrapper>
    </Popup.DynamicHeight>
  );
};
