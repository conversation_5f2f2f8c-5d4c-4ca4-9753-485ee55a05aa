import { useFocusEffect } from '@react-navigation/native';
import { useCallback, useContext } from 'react';
import { TouchableOpacity } from 'react-native';

import * as Styled from './FavoriteChangeModal.styled';

import { Typography } from '~/components/Typography/Typography';
import { favoriteContext } from '~/context/favoriteContext';
import { useNavigate } from '~/hooks/useNavigate';
import { RouteEnum } from '~/routes/routes';
import theme from '~/theme';

export const FavoriteChangeModal = () => {
  const { navigation } = useNavigate();
  const { show, setShow, data } = useContext(favoriteContext);

  const handleChange = () => {
    if (data) {
      setShow(false);
      navigation.navigate(RouteEnum.FAVORITE_MODAL_SCREEN);
    }
  };

  useFocusEffect(
    useCallback(() => {
      if (show) {
        setTimeout(() => {
          setShow(false);
        }, 5000);
      }
    }, [show]),
  );

  if (!show) return null;
  return (
    <Styled.Wrapper>
      <Styled.Content>
        <Typography
          fontSize={14}
          fontWeight="400"
          color={theme.colors.white[100]}>{`Dodano do listy "WSZYSTKIE"`}</Typography>
        <TouchableOpacity onPress={() => handleChange()}>
          <Typography fontSize={14} fontWeight="500" color={theme.colors.white[100]}>
            {`Zmień`.toUpperCase()}
          </Typography>
        </TouchableOpacity>
      </Styled.Content>
    </Styled.Wrapper>
  );
};
