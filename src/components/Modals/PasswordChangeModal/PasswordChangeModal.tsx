import React from 'react';
import { FormProvider } from 'react-hook-form';

import * as Styled from './PasswordChangeModal.styled';

import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { Input } from '~/components/Inputs/Input/Input';
import { Loader } from '~/components/Loader/Loader';
import { ModalHeader } from '~/components/Modals/common/ModalHeader/ModalHeader';
import * as Popup from '~/components/Popup';
import { Typography } from '~/components/Typography/Typography';
import { usePasswordChange } from '~/hooks/usePasswordChange';
import theme from '~/theme';
import { assets } from '~/theme/assets';

export const PasswordChangeModal = () => {
  const { navigation, status, form, onSubmit, loading } = usePasswordChange();

  return (
    <Popup.DynamicHeight onClose={() => navigation.goBack()}>
      <Styled.Wrapper>
        <ModalHeader icon={assets.icons.map_svg} title={'<PERSON>mie<PERSON> hasło'} />
        {!loading && status?.status !== 'success' ? (
          <FormProvider {...form}>
            <Styled.Form>
              <Input
                name={'password'}
                isPassword={true}
                label={'Stare hasło'}
                icon={assets.icons.eye_password_icon_svg}
              />
              <Input
                name={'newPassword'}
                isPassword={true}
                label={'Nowe hasło'}
                icon={assets.icons.eye_password_icon_svg}
              />
              <Input
                name={'confirmPassword'}
                isPassword={true}
                label={'Powtórz hasło'}
                icon={assets.icons.eye_password_icon_svg}
              />
            </Styled.Form>
            <Styled.Button>
              <Button
                onPress={form.handleSubmit(onSubmit)}
                variant={ButtonVariant.FIFTH}
                size={ButtonSize.MEDIUM}
                borderRadius={24}>
                {`Zapisz`.toUpperCase()}
              </Button>
            </Styled.Button>
          </FormProvider>
        ) : null}
        {loading ? <Loader /> : null}
        {status ? (
          <Styled.Status>
            <Typography
              fontSize={14}
              color={status.status === 'error' ? theme.colors.red[100] : theme.colors.purple[100]}
              style={{ textAlign: 'center' }}>
              {status.message}
            </Typography>
            <Button
              onPress={() => navigation.goBack()}
              variant={ButtonVariant.FIFTH}
              size={ButtonSize.SMALL}
              borderRadius={24}>
              {`Powrót`.toUpperCase()}
            </Button>
          </Styled.Status>
        ) : null}
      </Styled.Wrapper>
    </Popup.DynamicHeight>
  );
};
