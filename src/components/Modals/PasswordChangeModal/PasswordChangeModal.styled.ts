import styled from '@emotion/native';

import { flexColCenter } from '~/styles/flex.styled';
import theme from '~/theme';

export const Wrapper = styled.View`
  gap: 16px;
  margin: 0 16px;
`;

export const Icon = styled.View`
  background-color: ${theme.colors.black[100]};
  width: 48px;
  height: 48px;
  border-radius: 32px;
  ${flexColCenter}
`;

export const Form = styled.View`
  gap: 16px;
  margin-top: 30px;
  ${flexColCenter}
`;

export const Button = styled.SafeAreaView`
  flex: 1;
  align-items: center;
  align-content: center;
  width: 50%;
  margin: 20px auto;
  ${flexColCenter};
`;

export const Status = styled.View`
  margin: 0 16px;
  height: 100px;
  ${flexColCenter};
  gap: 16px;
`;
