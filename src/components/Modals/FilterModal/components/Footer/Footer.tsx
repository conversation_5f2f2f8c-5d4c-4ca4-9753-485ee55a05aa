import { FC } from 'react';

import * as Styled from './Footer.styled';

import { Button } from '~/components/Button/Button';
import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { IFooterProps } from '~/components/Modals/FilterModal/components/Footer/types';
import theme from '~/theme';

export const Footer: FC<IFooterProps> = ({ clear, submit }) => {
  return (
    <Styled.Container>
      <Button
        alignSelf={'flex-start'}
        variant={ButtonVariant.FOURTH}
        size={ButtonSize.MEDIUM}
        onPress={clear}
        typographyStyles={{ fontSize: 14, fontWeight: '600', color: theme.colors.purple['100'] }}>
        WYCZYŚĆ FILTRY
      </Button>
      <Button
        alignSelf={'flex-start'}
        variant={ButtonVariant.FIFTH}
        size={ButtonSize.MEDIUM}
        onPress={submit}
        typographyStyles={{ fontSize: 14, fontWeight: '600', color: theme.colors.white['100'] }}>
        POKAŻ WYNIKI
      </Button>
    </Styled.Container>
  );
};
