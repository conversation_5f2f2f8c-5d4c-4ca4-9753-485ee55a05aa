import { FC, useRef, useState } from 'react';
import { Animated, Platform } from 'react-native';

import * as Styled from './Dropdown.styled';

import { FilterCheckbox } from '~/components/Checkbox/Checkbox';
import { Icon } from '~/components/Icon/Icon';
import { IDropdownProps } from '~/components/Modals/FilterModal/components/Dropdown/types';
import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';
import { assets } from '~/theme/assets';
import { IChildrenCategory, IParentCategory } from '~/types/category';

export const Dropdown: FC<IDropdownProps> = ({ data, onChildPress, onAllSelect }) => {
  const [isVisible, setIsVisible] = useState(false);
  const animationValue = useRef(new Animated.Value(0)).current;

  const handleSelect = (item: IChildrenCategory) => {
    onChildPress(item);
  };
  const handleCheckboxPress = (child: IChildrenCategory) => {
    handleSelect(child);
  };
  const handleAllSelect = (parent: IParentCategory) => {
    onAllSelect(parent);
  };

  /*
   * 61 height for IOS and 65 for Android is height of one element in dropdown
   * +1 is for "Wszystkie" element
   *
   */
  const value = Platform.OS === 'ios' ? 60 : 62;
  const toggleDropdown = () => {
    Animated.timing(animationValue, {
      toValue: isVisible ? 0 : (data.children.length + 1) * value,
      duration: 300,
      useNativeDriver: false,
    }).start();
    setIsVisible(!isVisible);
  };

  return (
    <Styled.Container>
      <Styled.Dropdown isActive={false} onPress={toggleDropdown}>
        <Typography fontSize={14} fontWeight="400" style={{ letterSpacing: 1 }}>
          {data.name}
        </Typography>
        <Styled.Arrow>
          <Icon
            iconSVG={isVisible ? assets.icons.arrow_up_svg : assets.icons.arrow_down_svg}
            width={32}
            height={32}
          />
        </Styled.Arrow>
      </Styled.Dropdown>

      {/*ANIMATED DROPDOWN*/}
      <Styled.ItemsContainer style={{ height: animationValue }}>
        <Styled.Item onPress={() => handleAllSelect(data)}>
          <Styled.TextWrapper>
            <Typography fontSize={16} fontWeight="400">
              Wszystkie:
            </Typography>
            <Typography fontSize={12} fontWeight="400">
              {data.name}
            </Typography>
          </Styled.TextWrapper>
          <FilterCheckbox
            onStateChange={() => {}}
            onPress={() => handleAllSelect(data)}
            isChecked={data.selected}
          />
        </Styled.Item>

        {data.children.map((child) => {
          return (
            <Styled.Item key={child.id} onPress={() => handleCheckboxPress(child)}>
              <Styled.ChildWrapper>
                <Typography
                  fontSize={14}
                  fontWeight="400"
                  style={{ lineHeight: 32 }}
                  color={theme.colors.gray['800']}>
                  {child.name}
                </Typography>
                <Typography
                  fontSize={14}
                  fontWeight="600"
                  style={{ lineHeight: 32 }}
                  color={theme.colors.gray['800']}>
                  {`[${child.count !== null ? child.count.toString() : '0'}]`}
                </Typography>
              </Styled.ChildWrapper>

              <FilterCheckbox
                onStateChange={() => {}}
                onPress={() => handleCheckboxPress(child)}
                isChecked={child.selected || false}
              />
            </Styled.Item>
          );
        })}
      </Styled.ItemsContainer>
    </Styled.Container>
  );
};
