import styled, { css } from '@emotion/native';
import { Animated, Dimensions } from 'react-native';

import theme from '~/theme';

export const Container = styled.View``;

export const Dropdown = styled.TouchableOpacity<{ isActive: boolean }>`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 12px 16px 12px 16px;

  ${({ isActive }) =>
    !isActive
      ? css`
          border-bottom-width: 1px;
          border-bottom-color: ${theme.colors.gray['1000']};
        `
      : ''};
`;

export const TextWrapper = styled.View`
  ${() => {
    if (Dimensions.get('window').width >= 360) {
      return css`
        flex-direction: row;
        align-items: center;
      `;
    } else {
      return css`
        align-items: flex-start;
        justify-content: flex-start;
      `;
    }
  }}
  gap: 5px;
`;
export const Arrow = styled.View``;
export const ItemsContainer = styled(Animated.View)`
  overflow: hidden;
`;
export const Item = styled.TouchableOpacity`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 14px 16px;
  background-color: ${theme.colors.white['200']};
  border-bottom-width: 1px;
  border-bottom-color: ${theme.colors.gray['1000']};
`;
export const ChildWrapper = styled.View`
  gap: 5px;
  flex-direction: row;
  align-items: center;
`;
