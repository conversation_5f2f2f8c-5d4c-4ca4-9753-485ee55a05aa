import { FC } from 'react';

import * as Styled from './Label.styled';

import { IFilterLabel } from '~/components/Modals/FilterModal/components/Label/types';
import { Typography } from '~/components/Typography/Typography';

export const Label: FC<IFilterLabel> = ({ label }) => {
  return (
    <Styled.Container>
      <Typography fontSize={16} fontWeight="600">
        {label}
      </Typography>
    </Styled.Container>
  );
};
