import { FC } from 'react';

import * as Styled from './Tile.styled';

import { ITile } from '~/components/Modals/FilterModal/components/Tile/types';
import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';

export const Tile: FC<ITile> = ({ text, onPress, isActive }) => {
  return (
    <Styled.Container onPress={onPress} isActive={isActive}>
      <Typography
        fontSize={14}
        fontWeight="500"
        color={isActive ? theme.colors.white['100'] : theme.colors.black['300']}>
        {text.toUpperCase()}
      </Typography>
    </Styled.Container>
  );
};
