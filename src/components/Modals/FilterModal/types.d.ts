import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { ICategoryReducer } from '~/redux/reducers/content/categories/types';
import { ICategoryShop, ICategorySort, IChildrenCategory, IParentCategory } from '~/types/category';

type TSelectedFilter = {
  selectedSortFilter: ICategorySort | null;
  selectedShopFilter: ICategoryShop | null;
  selectedCategoryFilter: IParentCategory[] | undefined;
};
export interface IFilterModal {
  isOpen: boolean;
  onClose: (val: boolean) => void;
  submit: () => void;
  clear: () => void;
  handleAll: (data: IParentCategory) => void;
  handleSort: (item: ICategorySort) => void;
  handleShop: (item: ICategoryShop) => void;
  handleCategory: (data: IChildrenCategory) => void;
  data: Pick<ICategoryReducer, 'shopCollection' | 'sortCollection'> & TSelectedFilter;
  screenType: ObjectTypeEnum;
  onUserLocalizationChangePress: () => void;
  cityId: number | null;
  cityName: string | null;
}
