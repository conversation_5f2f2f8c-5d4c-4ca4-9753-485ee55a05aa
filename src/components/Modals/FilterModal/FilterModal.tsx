import { FC, useRef } from 'react';
import { View } from 'react-native';

import * as Filter from './components';
import * as Styled from './FilterModal.styled';

import { ModalHeader } from '~/components/Modals/common/ModalHeader/ModalHeader';
import { IFilterModal } from '~/components/Modals/FilterModal/types';
import * as Popup from '~/components/Popup';
import { Slider } from '~/components/Slider/Slider';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { assets } from '~/theme/assets';

export const FilterModal: FC<IFilterModal> = ({
  handleSort,
  handleShop,
  handleCategory,
  handleAll,
  isOpen,
  onClose,
  data,
  submit,
  clear,
  screenType,
  cityName,
  onUserLocalizationChangePress,
}) => {
  const clearInvocationCountRef = useRef(0);

  const enhancedClear = () => {
    clearInvocationCountRef.current += 1;
    clear();
  };
  const hideLocalization = () => {
    return (
      screenType === ObjectTypeEnum.Leaflet ||
      screenType === ObjectTypeEnum.Inspiration ||
      screenType === ObjectTypeEnum.Article ||
      screenType === ObjectTypeEnum.Brand
    );
  };
  return isOpen ? (
    <Popup.StaticHeight
      onClose={() => onClose(false)}
      scrollable={true}
      heightPercentage={0.9}
      FooterComponent={() => <Filter.Footer clear={enhancedClear} submit={submit} />}>
      <Styled.Container>
        <ModalHeader icon={assets.icons.filter_modal_svg} title={'Filtry'} />
        <Styled.Content>
          <Styled.Section>
            <Filter.Label label={'Sortuj'} />
            <Styled.TileWrapper>
              <Slider
                itemWidth={1}
                ItemSeparatorComponent={() => <View style={{ width: 5 }} />}
                data={data.sortCollection}
                renderItem={({ item }) => {
                  if (item.displayFor.includes(screenType)) {
                    return (
                      <Filter.Tile
                        text={item.name}
                        onPress={() => handleSort(item)}
                        isActive={item.id === data.selectedSortFilter?.id}
                      />
                    );
                  } else {
                    return null;
                  }
                }}
              />
            </Styled.TileWrapper>
          </Styled.Section>
          {screenType !== ObjectTypeEnum.Article && screenType !== ObjectTypeEnum.Inspiration ? (
            <Styled.Section>
              <Filter.Label label={'Sklepy'} />
              <Styled.TileWrapper>
                <Slider
                  itemWidth={1}
                  ItemSeparatorComponent={() => <View style={{ width: 5 }} />}
                  data={data.shopCollection}
                  renderItem={({ item }) => {
                    if (item.displayFor.includes(screenType)) {
                      return (
                        <Filter.Tile
                          text={item.name}
                          onPress={() => handleShop(item)}
                          isActive={item.id === data.selectedShopFilter?.id}
                        />
                      );
                    } else {
                      return null;
                    }
                  }}
                />
              </Styled.TileWrapper>
            </Styled.Section>
          ) : null}
          {hideLocalization() ? null : (
            <Styled.Section>
              <Filter.Localization
                onLocalizationPress={onUserLocalizationChangePress}
                cityName={cityName}
              />
            </Styled.Section>
          )}
          <Styled.Section>
            <Filter.Label label={'Kategorie'} />
            {data.selectedCategoryFilter?.map((item, index) => {
              return (
                <Filter.Dropdown
                  onAllSelect={(parent) => handleAll(parent)}
                  onChildPress={(children) => handleCategory(children)}
                  data={item}
                  key={index}
                />
              );
            })}
          </Styled.Section>
        </Styled.Content>
      </Styled.Container>
    </Popup.StaticHeight>
  ) : null;
};
