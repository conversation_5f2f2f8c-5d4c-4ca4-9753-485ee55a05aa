import { createContext, ReactNode } from 'react';

import { ISearchProps } from '~/components/Modals/SearchModal/types';
import { ISearchResultData } from '~/types/search';

type TSearchModalContext = Omit<
  ISearchProps,
  'onValueChange' | 'onSubmit' | 'searchResult' | 'value' | 'onNavigate'
>;

interface ISearchModalContext extends TSearchModalContext {
  onCategorySelect: (item: ISearchResultData) => void;
}

export interface ISearchModalContextContextProvider {
  providerValues: ISearchModalContext;
  children: ReactNode;
}

const defaultContext: ISearchModalContext = {
  placeholder: '',
  category: '',
  mayInterestChildren: null,
  popularSearches: [],

  onCategorySelect: () => {},
};
export const SearchModalContext = createContext<ISearchModalContext>(defaultContext);

export const SearchModalContextContextProvider = ({
  providerValues,
  children,
}: ISearchModalContextContextProvider) => {
  return (
    <SearchModalContext.Provider value={providerValues}>{children}</SearchModalContext.Provider>
  );
};
