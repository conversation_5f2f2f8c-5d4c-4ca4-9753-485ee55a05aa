import { FC, useEffect } from 'react';

import * as Search from './components';
import * as Styled from './SearchModal.styled';

import { Icon } from '~/components/Icon/Icon';
import { SearchModalContextContextProvider } from '~/components/Modals/SearchModal/context/SearchModalContext';
import { useSearchModal } from '~/components/Modals/SearchModal/hooks/useSearchModal';
import { ISearchProps } from '~/components/Modals/SearchModal/types';
import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';
import { assets } from '~/theme/assets';

export const SearchModal: FC<ISearchProps> = ({
  placeholder,
  popularSearches,
  category,
  searchResult,
  mayInterestChildren,
  onValueChange,
  onSubmit,
  value,
  onNavigate,
}) => {
  const { onChange, clear, onFocus, onEndEditing, onCategorySelect } = useSearchModal();

  useEffect(() => {
    onValueChange(value);
  }, [value]);

  return (
    <SearchModalContextContextProvider
      providerValues={{
        popularSearches,
        placeholder,
        mayInterestChildren,
        category,
        onCategorySelect: (val) => {
          onCategorySelect(val);
          onSubmit(val);
        },
      }}>
      <Styled.Container>
        <Styled.InputContainer>
          <Styled.IconWrapper onPress={onNavigate}>
            <Icon
              iconSVG={assets.icons.cross_without_background_svg}
              isStroke={true}
              fill={theme.colors.gray['600']}
              width={24}
              height={24}
            />
          </Styled.IconWrapper>
          <Styled.Input
            onFocus={onFocus}
            onSubmitEditing={() => {
              onEndEditing();
              onSubmit(value);
            }}
            autoCapitalize="none"
            onChangeText={onChange}
            value={value}
            placeholderTextColor={theme.colors.gray['1100']}
            placeholder={placeholder}
          />
          <Styled.IconWrapper onPress={clear}>
            <Icon
              iconSVG={assets.icons.cross_circle_svg}
              isFill={true}
              isStroke={true}
              fill={theme.colors.gray['1100']}
              width={24}
              height={24}
            />
          </Styled.IconWrapper>
        </Styled.InputContainer>
        {category ? <Search.Category /> : null}
        {popularSearches.length > 0 && value.length < 3 ? (
          <Search.Result
            withCount={false}
            onSubmit={onSubmit}
            result={popularSearches}
            categoryText={'Ostatnie wyszukiwania: '}
          />
        ) : searchResult.length > 0 ? (
          <Search.Result result={searchResult} onSubmit={onSubmit} />
        ) : (
          <Typography
            fontSize={14}
            fontWeight="400"
            style={{ alignSelf: 'center' }}
            color={theme.colors.gray['800']}>
            Brak wyników
          </Typography>
        )}
        {mayInterestChildren ? (
          <Search.Interest title={'Może Cię zainteresować:'} children={mayInterestChildren} />
        ) : null}
      </Styled.Container>
    </SearchModalContextContextProvider>
  );
};
