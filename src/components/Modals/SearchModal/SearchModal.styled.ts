import styled from '@emotion/native';
import { View } from 'react-native';

import theme from '~/theme';

export const Container = styled(View)`
  flex-direction: column;
  background-color: ${theme.colors.white['100']};
  height: 100%;
  padding-top: 16px;
  gap: 22px;
`;

export const InputContainer = styled.View`
  flex-direction: row;
`;

export const Input = styled.TextInput`
  width: 80%;
  padding: 16px 16px 16px 5px;
  color: ${theme.colors.black[100]};
  font-family: ${theme.fonts.primary['400']};
  font-size: 14px;
`;
export const IconWrapper = styled.TouchableOpacity`
  align-items: center;
  justify-content: center;
  width: 10%;
`;
