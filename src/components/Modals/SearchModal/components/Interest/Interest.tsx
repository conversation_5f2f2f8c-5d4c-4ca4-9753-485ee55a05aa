import { FC } from 'react';

import * as Styled from './Interest.styled';

import { ISearchInterestProps } from '~/components/Modals/SearchModal/types';
import { Typography } from '~/components/Typography/Typography';

export const Interest: FC<ISearchInterestProps> = ({ title, children }) => {
  return (
    <Styled.Container>
      <Styled.HeaderContainer>
        <Typography fontSize={14} fontWeight="600">
          {title}
        </Typography>
      </Styled.HeaderContainer>
      <Styled.ChildrenContainer>{children}</Styled.ChildrenContainer>
    </Styled.Container>
  );
};
