import styled from '@emotion/native';

import theme from '~/theme';

export const Container = styled.View`
  width: 100%;
  gap: 11px;
  flex-direction: column;
`;
export const Title = styled.View`
  padding-left: 16px;
`;

export const ResultWrapper = styled.View``;

export const Result = styled.TouchableOpacity`
  width: 100%;
  padding: 0 0 10px 0;
  border-top-width: 1px;
  border-top-color: ${theme.colors.gray['1000']};
`;
export const ResultInnerWrapper = styled.View`
  padding: 16px 0 10px 16px;
  flex-direction: row;
  gap: 5px;
  align-items: center;
`;
