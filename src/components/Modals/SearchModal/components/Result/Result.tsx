import { FC } from 'react';

import * as Styled from './Result.styled';

import { ISearchResultProps } from '~/components/Modals/SearchModal/types';
import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';
import { ISearchResultData } from '~/types/search';

interface Props extends ISearchResultProps {
  onSubmit: (value: ISearchResultData | string) => void;
}

export const Result: FC<Props> = ({ withCount = true, categoryText, result, onSubmit }) => {
  return (
    <Styled.Container>
      {categoryText ? (
        <Styled.Title>
          <Typography fontSize={14} fontWeight="600">
            {categoryText}
          </Typography>
        </Styled.Title>
      ) : null}
      <Styled.ResultWrapper>
        {result.map((item, index) => {
          return (
            <Styled.Result
              key={index}
              onPress={() => {
                onSubmit(item.name);
              }}>
              <Styled.ResultInnerWrapper>
                <Typography fontSize={14} fontWeight="400">
                  {item.name}
                </Typography>
                {withCount ? (
                  <Typography fontSize={14} fontWeight="600" color={theme.colors.gray['800']}>
                    {`[${item.count !== null ? item.count.toString() : '0'}]`}
                  </Typography>
                ) : null}
              </Styled.ResultInnerWrapper>
            </Styled.Result>
          );
        })}
      </Styled.ResultWrapper>
    </Styled.Container>
  );
};
