import styled from '@emotion/native';

import theme from '~/theme';

export const Container = styled.View`
  padding: 0 10px 0 10px;
`;
export const Button = styled.View`
  padding: 10px 15px 10px 15px;
  align-items: center;
  justify-content: center;
  border-radius: 100px;
  background-color: ${theme.colors.purple['200']};
  align-self: flex-start;
  flex-direction: row;
  gap: 2px;
`;

export const Title = styled.Text`
  font-weight: 400;
  font-size: 10;
  color: ${theme.colors.purple['100']};
`;

export const Label = styled.Text`
  font-weight: 700;
  font-size: 10;
  color: ${theme.colors.purple['100']};
`;
