import { FC, useContext } from 'react';

import * as Styled from './Category.styled';

import { SearchModalContext } from '~/components/Modals/SearchModal/context/SearchModalContext';

export const Category: FC = () => {
  const context = useContext(SearchModalContext);

  return (
    <Styled.Container>
      <Styled.Button>
        <Styled.Title>WYSZUKIWANIE DLA:</Styled.Title>
        <Styled.Label>{context.category.toUpperCase()}</Styled.Label>
      </Styled.Button>
    </Styled.Container>
  );
};
