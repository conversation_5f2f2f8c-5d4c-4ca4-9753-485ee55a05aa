import { useState } from 'react';

import { setPopularSearchesValue } from '~/redux/reducers/content/search/actions';
import { useAppDispatch } from '~/redux/store';
import { ISearchResultData } from '~/types/search';

export const useSearchModal = () => {
  const dispatch = useAppDispatch();
  const [active, setActive] = useState<boolean>(false);
  const [isFocused, setIsFocused] = useState<boolean>(true);

  const onChange = (text: string) => {
    dispatch(setPopularSearchesValue(text));
    setActive(true);
    if (text === '') {
      setActive(false);
    }
  };
  const clear = () => {
    dispatch(setPopularSearchesValue(''));
    setActive(false);
  };
  const onFocus = () => {
    setIsFocused(true);
  };
  const onEndEditing = () => {
    setIsFocused(false);
    setActive(false);
  };
  const onCategorySelect = (item: ISearchResultData) => {
    dispatch(setPopularSearchesValue(item.name));
  };

  return {
    active,
    isFocused,
    onChange,
    clear,
    onFocus,
    onEndEditing,
    onCategorySelect,
  };
};
