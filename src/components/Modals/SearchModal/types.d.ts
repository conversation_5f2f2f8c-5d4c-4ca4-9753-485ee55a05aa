import { ReactElement } from 'react';

import { ISearchResultData } from '~/types/search';

interface ISearchProps {
  popularSearches: ISearchResultData[];
  searchResult: ISearchResultData[];
  category: string;
  mayInterestChildren?: ReactElement | null;
  placeholder: string;
  onValueChange: (value: string) => void;
  onSubmit: (value: ISearchResultData | string) => void;
  value: string;
  onNavigate: () => void;
}

// Results.tsx props:
interface ISearchResultProps {
  categoryText?: string;
  result: ISearchResultData[];
  withCount?: boolean;
}

// Interest.tsx props:
interface ISearchInterestProps {
  children: ReactElement;
  title: string;
}

// Category.tsx props:
interface ISearchCategoryProps {
  text: string;
}
