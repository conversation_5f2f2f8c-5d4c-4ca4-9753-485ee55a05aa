import { FC } from 'react';

import * as Styled from './InspirationImage.styled';

import { CardFavourite } from '~/components/Cards/components/CardFavourite';
import { ImageComponent } from '~/components/ImageComponent/ImageComponent';
import { IInspirationData } from '~/components/Inspiration/types';

export const InspirationImage: FC<IInspirationData> = ({
  onFavoritePress,
  data,
  onPress,
  resizeMode = 'cover',
}) => {
  return (
    <Styled.Container onPress={onPress}>
      <Styled.IconWrapper>
        <CardFavourite
          active={data.is_favorite}
          onPress={(isFavorite) => {
            onFavoritePress(data.id, isFavorite);
          }}
        />
      </Styled.IconWrapper>
      <ImageComponent
        noCache={true}
        borderRadius={4}
        uri={data.first_image}
        resizeMode={resizeMode}
      />
    </Styled.Container>
  );
};
