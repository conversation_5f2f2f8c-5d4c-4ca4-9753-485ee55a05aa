import { IInspiration } from '~/types/inspirations';
import { TOnFavoritePress } from '~/types/onFavoritePress';

export interface IInspirationData {
  data: IInspiration;
  onPress: () => void;
  onFavoritePress: TOnFavoritePress;
  resizeMode?: 'contain' | 'cover' | 'stretch' | 'center';
}

export interface IInspirationProps {
  data?: IInspiration[];
  onPress: (id: number, is_rec?: boolean) => void;
  onFavoritePress: TOnFavoritePress;
}
