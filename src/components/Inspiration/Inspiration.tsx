import { FC } from 'react';
import { StyleSheet, View } from 'react-native';

import { sizes } from './constants';

import { InspirationImage } from '~/components/Inspiration/components/InspirationImage/InspirationImage';
import { IInspirationProps } from '~/components/Inspiration/types';
import theme from '~/theme';

export const LofiInspiration = () => {
  return (
    <View
      style={[styles.container, { backgroundColor: theme.colors.gray['300'], height: sizes[1] }]}>
      <View style={styles.column} />
    </View>
  );
};
export const Inspiration: FC<IInspirationProps> = ({ onFavoritePress, data, onPress }) => {
  if (!data) {
    return <LofiInspiration />;
  } else {
    return (
      <View style={[styles.container, { height: data.length > 4 ? sizes[4] : sizes[data.length] }]}>
        <View style={styles.column}>
          {/*FIRST ROW*/}
          <View style={styles.row}>
            <View style={styles.column}>
              <InspirationImage
                resizeMode="cover"
                onFavoritePress={onFavoritePress}
                data={data[0]}
                onPress={() => onPress(data[0].id, data[0].is_rec)}
              />
            </View>

            {/*RIGHT COLUMN*/}
            {data[1] ? (
              <View style={styles.column}>
                <View style={styles.row}>
                  <InspirationImage
                    resizeMode="cover"
                    onFavoritePress={onFavoritePress}
                    data={data[1]}
                    onPress={() => onPress(data[1].id, data[1].is_rec)}
                  />
                </View>

                {data[2] ? (
                  <View style={styles.row}>
                    <InspirationImage
                      resizeMode="cover"
                      onFavoritePress={onFavoritePress}
                      data={data[2]}
                      onPress={() => onPress(data[2].id, data[2].is_rec)}
                    />
                  </View>
                ) : null}
              </View>
            ) : null}
          </View>

          {/*SECOND ROW*/}
          {data[3] ? (
            <View style={styles.row}>
              <InspirationImage
                resizeMode="cover"
                onFavoritePress={onFavoritePress}
                data={data[3]}
                onPress={() => onPress(data[3].id, data[3].is_rec)}
              />
            </View>
          ) : null}
        </View>
      </View>
    );
  }
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    borderRadius: 8,
  },
  column: {
    flex: 10,
    flexDirection: 'column',
    gap: 20,
  },
  row: {
    flex: 2,
    flexDirection: 'row',
    gap: 20,
  },
});
