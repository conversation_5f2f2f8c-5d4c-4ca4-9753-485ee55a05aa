import styled from '@emotion/native';

import { IMain } from '~/components/Layout/types.d';
import { LAYOUT_PADDING } from '~/helpers/layoutPadding';
import theme from '~/theme';

export const Area = styled.SafeAreaView`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  gap: 0;
  background-color: ${theme.colors.white[100]};
`;

export const Main = styled.View<IMain>`
  flex: 24;
  ${({ homeScreen, padding }) =>
    homeScreen
      ? `
          padding-left: ${padding !== undefined ? `${padding}px` : `${LAYOUT_PADDING}px`};
          padding-right: ${padding !== undefined ? `${padding}px` : `${LAYOUT_PADDING}px`};
          padding-bottom: ${padding !== undefined ? `${padding}px` : `${LAYOUT_PADDING}px`};
      `
      : `
          padding: ${padding !== undefined ? `${padding}px` : `${LAYOUT_PADDING}px`};
  `}
  flex-direction: column;
  justify-content: flex-start;
`;

export interface IFooter {
  padding: number;
}

export const Footer = styled.View<IFooter>`
  border-color: ${theme.colors.gray[300]};
  border-top-width: 1px;
  padding-top: 10px;
  padding-bottom: ${({ padding }) => `${padding}px`};
`;
