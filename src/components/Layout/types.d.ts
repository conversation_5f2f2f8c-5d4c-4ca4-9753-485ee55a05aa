import { ReactElement } from 'react';

import { IHeaderProps } from '~/components/Header/types';

export interface ILayoutProps extends IMain {
  children: ReactElement | ReactElement[];
  headerOptions?: Omit<IHeaderProps, 'onLeftIconPress'>;
  headerShown?: boolean;
  footerShown?: boolean;
  isLoading?: boolean;
  refreshControl?: React.Element;
  onGoBack?: () => void;
  error?: any;
}

export interface IMain {
  padding?: number;
  homeScreen?: boolean;
  isIOS?: boolean;
}
