import { useNetInfo } from '@react-native-community/netinfo';
import { useFocusEffect } from '@react-navigation/native';
import { FC, ReactElement, useCallback, useMemo, useRef } from 'react';
import { Animated, Dimensions, Platform, ScrollView, StyleSheet, View } from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';

import { HomeLoader } from '../Loader/HomeLoader';

import * as Styled from './Layout.styled';

import * as Error from '~/components/Error';
import * as Header from '~/components/Header';
import { Shadow } from '~/components/Layout/Style';
import { ILayoutProps } from '~/components/Layout/types.d';
import { menuMock, menuMoreMock } from '~/components/Menu/data';
import { Menu } from '~/components/Menu/Menu';

const deviceHeight = Dimensions.get('window').height;
export const Layout: FC<ILayoutProps> = ({
  children,
  headerShown = true,
  padding,
  headerOptions,
  footerShown = true,
  isLoading = false,
  refreshControl,
  homeScreen = false,
  onGoBack,
  error,
}): ReactElement => {
  const { isConnected } = useNetInfo();

  const scrollY = useRef(new Animated.Value(0)).current;
  const diffClamp = Animated.diffClamp(scrollY, 0, 128);
  const translateY = useMemo(
    () =>
      diffClamp.interpolate({
        inputRange: [0, 128],
        outputRange: [0, -128],
        extrapolate: 'clamp',
      }),
    [],
  );

  /**
   * @description Reset scrollY value when the screen is unmouted
   * @description this solution should fix hidden navbar
   */
  useFocusEffect(
    useCallback(() => {
      return () => {
        scrollY.setValue(0);
      };
    }, []),
  );

  const insets = useSafeAreaInsets();

  return (
    <SafeAreaView style={styled.area} edges={['top', 'right', 'left', 'bottom']}>
      {homeScreen ? (
        <Header.AnimatedHeader
          translateY={translateY}
          {...(isConnected && { rightItem: headerOptions?.rightItem })}
        />
      ) : headerShown ? (
        <Header.Header
          label={headerOptions?.label}
          labelStyle={headerOptions?.labelStyle}
          leftIcon={headerOptions?.leftIcon}
          rightItem={isConnected ? headerOptions?.rightItem : undefined}
          onRightIconPress={headerOptions?.onRightIconPress}
          homeScreen={headerOptions?.homeScreen}
          resultCounter={headerOptions?.resultCounter}
          isIOS={Platform.OS === 'ios'}
          onLeftIconPress={() => {
            if (onGoBack) {
              onGoBack();
            }
          }}
        />
      ) : null}

      <Styled.Main
        homeScreen={headerOptions?.homeScreen}
        padding={padding}
        isIOS={Platform.OS === 'ios'}>
        {isLoading ? <HomeLoader /> : null}
        {homeScreen ? (
          <ScrollView
            contentContainerStyle={{
              paddingTop: 70,
              paddingBottom: insets.bottom > 0 ? insets.bottom : 0,
            }}
            scrollEventThrottle={10}
            refreshControl={refreshControl}
            onScroll={(e) => {
              let y = e.nativeEvent.contentOffset.y;
              if (y > deviceHeight * 0.1) {
                scrollY.setValue(e.nativeEvent.contentOffset.y);
              }
            }}>
            {children}
          </ScrollView>
        ) : error && error.status === 'FETCH_ERROR' ? (
          <Error.NetworkError />
        ) : (
          children
        )}
      </Styled.Main>
      {footerShown && isConnected && (
        <Styled.Footer
          style={[
            Shadow.footer,
            {
              // paddingBottom: Platform.OS === 'ios' ? (insets.bottom > 0 ? insets.bottom : 10) : 0,
            },
          ]}
          padding={Platform.OS === 'android' ? 10 : 0}>
          <Menu menuCollection={menuMock} menuMoreCollection={menuMoreMock} />
        </Styled.Footer>
      )}
    </SafeAreaView>
  );
};

const styled = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  area: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
    gap: 0,
    backgroundColor: 'white',
    // Ensure content doesn't overlap with system UI
    flex: 1,
  },
});
