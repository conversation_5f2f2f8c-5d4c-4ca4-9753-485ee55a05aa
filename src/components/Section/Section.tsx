import React, { useEffect, useState } from 'react';
import { View } from 'react-native';

import * as Styled from './Section.styled';

import { Loader } from '~/components/Loader/Loader';
import { SectionHeader } from '~/components/Section/components/SectionHeader/SectionHeader';
import { ISection } from '~/components/Section/types';
import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';

const Line = () => {
  return (
    <View
      style={{
        flex: 1,
        height: 1,
        width: '100%',
        marginVertical: 5,
        backgroundColor: theme.colors.gray['1000'],
        alignSelf: 'center',
      }}
    />
  );
};
export const Section = ({
  title,
  counter,
  onSectionHeaderPress,
  children,
  style,
  isLoading = false,
  showBottomLine = true,
  bgColor,
}: ISection) => {
  const [hasLoadingTimedOut, setHasLoadingTimedOut] = useState(false);

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    if (isLoading) {
      timeoutId = setTimeout(() => {
        setHasLoadingTimedOut(true);
      }, 2000);
    } else {
      setHasLoadingTimedOut(false);
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [isLoading]);

  return (
    <Styled.Wrapper style={[style]} bgColor={bgColor}>
      {title ? (
        <SectionHeader
          title={title}
          counter={counter}
          onSectionHeaderPress={onSectionHeaderPress}
        />
      ) : null}

      {isLoading ? (
        hasLoadingTimedOut ? (
          <Typography
            fontSize={12}
            style={{ alignSelf: 'center' }}
            color={theme.colors.gray['100']}>
            Brak danych w obecnej lokalizacji
          </Typography>
        ) : (
          <Loader />
        )
      ) : (
        <Styled.Content>
          <>{children}</>
          {showBottomLine && (
            <View style={{ paddingHorizontal: 16 }}>
              <Line />
            </View>
          )}
        </Styled.Content>
      )}
    </Styled.Wrapper>
  );
};
