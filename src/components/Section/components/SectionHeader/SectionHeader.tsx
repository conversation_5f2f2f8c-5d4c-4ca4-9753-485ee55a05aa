import React from 'react';

import * as Styled from './SectionHeader.styled';

import { Icon } from '~/components/Icon/Icon';
import { TSectionHeader } from '~/components/Section/components/SectionHeader/types';
import { Typography } from '~/components/Typography/Typography';
import { LAYOUT_PADDING } from '~/helpers/layoutPadding';
import theme from '~/theme';
import { assets } from '~/theme/assets';

export const SectionHeader = ({ title, counter, onSectionHeaderPress }: TSectionHeader) => {
  return (
    <Styled.Wrapper style={{ paddingHorizontal: LAYOUT_PADDING + 4 }}>
      {title && (
        <Typography fontSize={18} fontWeight="600" color={theme.colors.black[300]}>
          {title}
        </Typography>
      )}

      <Styled.WrapperNav onPress={onSectionHeaderPress}>
        {counter ? (
          <Typography fontSize={14} fontWeight="400" color={theme.colors.gray[800]}>
            {`${counter}`}
          </Typography>
        ) : null}
        {onSectionHeaderPress ? (
          <Icon iconSVG={assets.icons.arrow_right_svg} width={32} fill={theme.colors.gray[800]} />
        ) : null}
      </Styled.WrapperNav>
    </Styled.Wrapper>
  );
};
