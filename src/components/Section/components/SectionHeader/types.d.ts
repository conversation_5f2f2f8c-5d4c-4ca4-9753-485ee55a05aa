export type TSectionHeader = {
  title?: string;
  counter?: number | string;
  onSectionHeaderPress?: () => void;
};
// }
// | {
//
//     // navigateTo?:
//     //   | RouteEnum.ARTICLE
//     //   | RouteEnum.BRAND
//     //   | RouteEnum.INSPIRATION
//     //   | RouteEnum.NEWSPAPER
//     //   | RouteEnum.COUPON
//     //   | RouteEnum.DISCOUNT
//     //   | RouteEnum.PROFILE
//     //   | RouteEnum.PREMIUM
//     //   | RouteEnum.HOME;
//   }
// | {
//     title?: never;
//     counter?: never;
//     navigateTo?: never;
//   };
