import React from 'react';
import { View } from 'react-native';

import { Item } from '~/components/Item/Item';
import { ISectionSlider } from '~/components/Section/components/SectionSlider/types';
import { Section } from '~/components/Section/Section';
import { Slider } from '~/components/Slider/Slider';
import { RouteEnum } from '~/routes/routes';

export const SectionSlider = ({ data, title, itemWidth, width, onPress }: ISectionSlider) => {
  return (
    <Section title={title} counter={123} navigateTo={RouteEnum.HOME}>
      <Slider
        ItemSeparatorComponent={() => <View style={{ width: 5 }} />}
        data={data}
        itemWidth={itemWidth}
        renderItem={({ item, index }) => (
          <Item
            id={item.id}
            onPress={() => onPress(item)}
            width={width}
            key={index}
            itemContentProps={item.itemContentProps}
            itemHeaderProps={item.itemHeaderProps}
          />
        )}
      />
    </Section>
  );
};
