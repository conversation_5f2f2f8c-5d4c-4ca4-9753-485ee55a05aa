import React, { FC } from 'react';

import { IIconProps } from '~/components/Icon/types.d';

export const Icon: FC<IIconProps> = ({
  onPress,
  rotation,
  iconSVG,
  width,
  height,
  fill,
  isStroke = true,
  isFill = false,
}) => {
  let IconComponent = iconSVG;
  // TODO: for test purposes
  // if (typeof IconComponent !== 'function') {
  //   console.error('Invalid SVG component');
  //   return null;
  // }
  return (
    <IconComponent
      onPress={onPress}
      rotation={rotation}
      color={fill}
      stroke={isStroke ? fill : 'none'}
      fill={isFill ? fill : 'none'}
      width={width}
      height={height}
    />
  );
};
