import React, { ReactElement, useCallback, useEffect, useRef } from 'react';
import {
  FlatList,
  ListRenderItem,
  NativeScrollEvent,
  NativeSyntheticEvent,
  StyleProp,
  StyleSheet,
  ViewStyle,
} from 'react-native';

import { DiscountCardLofi } from '../Cards/DiscountCard/DiscountCard';

import { useFlatListHandlers } from '~/components/Slider/hooks/useFlatListHandlers';
import { LAYOUT_PADDING } from '~/helpers/layoutPadding';

interface ICarouselGalleryProps<T> {
  containerStyle?: StyleProp<ViewStyle>;
  data: T[] | undefined;
  renderItem: ListRenderItem<T>;
  initialNumToRender?: number;
  ItemSeparatorComponent?: () => ReactElement;
  ListFooterComponent?: () => ReactElement;
  ListEmptyComponent?: () => ReactElement;
  snapToInterval?: number;
  scrollToIndex?: number;
  snapToAlignment?: 'start' | 'center' | 'end';
  currentIndex?: number;
  itemWidth?: number;
  handleIndex?: (index: number) => void;
}

export const Slider = <T,>({
  data,
  renderItem,
  ItemSeparatorComponent,
  snapToInterval,
  scrollToIndex,
  initialNumToRender = 4,
  snapToAlignment = 'start',
  currentIndex,
  itemWidth,
  handleIndex,
}: ICarouselGalleryProps<T>): ReactElement => {
  const flatListRef = useRef<FlatList>(null);
  const { scrollToOffset } = useFlatListHandlers({ ref: flatListRef });

  const scrollToItem = (index: number) => {
    if (
      scrollToIndex !== undefined &&
      flatListRef.current !== undefined &&
      currentIndex !== undefined &&
      itemWidth !== undefined
    ) {
      const offset = itemWidth * index;
      scrollToOffset(offset);
    }
  };

  useEffect(() => {
    if (currentIndex !== undefined) {
      scrollToItem(currentIndex);
    }
  }, [currentIndex]);

  const handleCurrentVisibleIndex = (e: NativeSyntheticEvent<NativeScrollEvent>) => {
    if (handleIndex && itemWidth) {
      const index = Math.round(e.nativeEvent.contentOffset.x / itemWidth);
      handleIndex(index);
    }
  };

  return (
    <FlatList
      initialNumToRender={initialNumToRender}
      onScrollToIndexFailed={() => {}}
      snapToAlignment={snapToAlignment}
      pagingEnabled={true}
      ref={flatListRef}
      data={data}
      renderItem={renderItem}
      snapToInterval={snapToInterval}
      decelerationRate="fast"
      ItemSeparatorComponent={ItemSeparatorComponent}
      showsHorizontalScrollIndicator={false}
      showsVerticalScrollIndicator={false}
      onMomentumScrollEnd={handleCurrentVisibleIndex}
      horizontal
      keyExtractor={(item, index) => index.toString()}
    />
  );
};

export const ContentSlider = <T,>({
  data,
  renderItem,
  ItemSeparatorComponent,
  containerStyle,
  ListFooterComponent,
  ListEmptyComponent,
}: ICarouselGalleryProps<T>): ReactElement => {
  const renderItemFunction = useCallback(renderItem, []);
  return (
    <FlatList
      horizontal
      ItemSeparatorComponent={ItemSeparatorComponent}
      ListFooterComponent={ListFooterComponent}
      data={data}
      ListEmptyComponent={ListEmptyComponent}
      renderItem={renderItemFunction}
      keyExtractor={(_, index) => index.toString()}
      contentContainerStyle={[styles.flatList, containerStyle]}
      showsVerticalScrollIndicator={false}
      showsHorizontalScrollIndicator={false}
    />
  );
};
// SLIDER EDYTOWANE NA HOME
const styles = StyleSheet.create({
  flatList: {
    paddingHorizontal: LAYOUT_PADDING,
    paddingVertical: LAYOUT_PADDING,
    gap: 10,
    // backgroundColor: 'forestgreen',
  },
});
