import { RefObject } from 'react';
import { FlatList } from 'react-native';

interface IFlatListHandlers {
  ref: RefObject<FlatList>;
}

type ReturnFlatListHandlers = {
  scrollToIndex: (index: number) => void;
  scrollToOffset: (offset: number) => void;
};
export const useFlatListHandlers = ({ ref }: IFlatListHandlers): ReturnFlatListHandlers => {
  const scrollToIndex = (index: number) => {
    if (ref.current) ref.current.scrollToIndex({ index });
  };
  const scrollToOffset = (offset: number) => {
    if (ref.current) ref.current.scrollToOffset({ offset });
  };
  return { scrollToIndex, scrollToOffset };
};
