import styled from '@emotion/native';

import theme from '~/theme';

export const Container = styled.View`
  flex: 1;
`;
export const TextWrapper = styled.View`
  flex: 2;
  justify-content: center;
  align-items: center;
  background-color: ${theme.colors.white['100']};
`;
export const Result = styled.View<{ flex?: number }>`
  background-color: ${theme.colors.white['200']};
  padding-top: 16px;
  flex: ${({ flex = 3 }) => flex};
`;
