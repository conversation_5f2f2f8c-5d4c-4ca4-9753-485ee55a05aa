import React, { ReactElement } from 'react';
import { ListRenderItem } from 'react-native';

import * as Styled from './EmptyData.styled';

import { Section } from '~/components/Section/Section';
import { ContentSlider } from '~/components/Slider/Slider';
import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';

interface Props<T> {
  data?: T[];
  renderItem: ListRenderItem<T>;
  onSectionHeaderPress: () => void;
  counter?: number;
  ItemSeparatorComponent?: () => ReactElement;
  flex?: number;
}

export const EmptyData = <T,>({
  ItemSeparatorComponent,
  data,
  renderItem,
  onSectionHeaderPress,
  counter,
  flex,
}: Props<T>) => {
  return (
    <Styled.Container>
      <Styled.TextWrapper>
        <Typography
          fontSize={18}
          fontWeight="400"
          style={{ textAlign: 'center', width: 250 }}
          color={theme.colors.gray['800']}>
          <PERSON><PERSON> wyników wyszukiwania dla wybranych filtrów
        </Typography>
      </Styled.TextWrapper>

      <Styled.Result flex={flex}>
        <Section
          title="Może Cię zainteresować"
          counter={counter}
          onSectionHeaderPress={onSectionHeaderPress}>
          <ContentSlider
            data={data}
            renderItem={renderItem}
            ItemSeparatorComponent={ItemSeparatorComponent}
          />
        </Section>
      </Styled.Result>
    </Styled.Container>
  );
};
