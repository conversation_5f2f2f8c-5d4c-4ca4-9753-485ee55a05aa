import { StyleSheet } from 'react-native';

import theme from '~/theme';
import { shadow } from '~/theme/shadows';

export const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  wrapper: {
    flex: 1,
  },
  container: {
    position: 'absolute',
    width: '100%',
    bottom: 0,
    backgroundColor: theme.colors.white['100'],
    borderTopStartRadius: 20,
    borderTopEndRadius: 20,
    gap: 0,
    paddingBottom: 16,
    zIndex: 999,
  },
  line: {
    alignSelf: 'center',
    marginTop: 14,
    marginBottom: 30,
  },
  disabledPress: {
    height: '100%',
    flex: 1,
  },
  content: {
    flex: 1,
  },
  footer: {
    ...shadow.gray['100'],
    width: '100%',
    padding: 16,
    position: 'absolute',
    bottom: 0,
    zIndex: 100,
  },
});
