import React, { ReactElement, useEffect, useRef } from 'react';
import {
  Animated,
  Dimensions,
  PanResponder,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';

import { styles } from './styles';

import { Icon } from '~/components/Icon/Icon';
import { Content } from '~/components/Popup/components/Content';
import { assets } from '~/theme/assets';

interface Props {
  children: ReactElement;
  onClose: () => void;
  FooterComponent?: () => ReactElement;
  scrollable?: boolean;
}

export const DynamicHeight = ({
  children,
  onClose,
  FooterComponent,
  scrollable = true,
}: Props): ReactElement => {
  const modalPosition = useRef({ x: 0, y: 0 });
  const _animatedValue = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (e, gesture) => {
        if (gesture.moveY < modalPosition.current.y + 100) {
          return true;
        }
        return false;
      },
      onMoveShouldSetPanResponderCapture: () => false,
      onPanResponderGrant: () => {
        _animatedValue.setValue(0);
      },
      onPanResponderMove: (_, gesture) => {
        if (gesture.dy >= 0) {
          _animatedValue.setValue(gesture.dy);
        }
      },
      onPanResponderRelease: (_, gesture) => {
        if (gesture.dy > Dimensions.get('window').height * 0.2) {
          Animated.timing(_animatedValue, {
            toValue: Dimensions.get('window').height,
            duration: 300,
            useNativeDriver: true,
          }).start();
        } else {
          Animated.timing(_animatedValue, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }).start();
        }
      },
    }),
  ).current;

  const translateY = _animatedValue.interpolate({
    inputRange: [0, Dimensions.get('window').height],
    outputRange: [0, Dimensions.get('window').height],
    extrapolate: 'clamp',
  });

  const handleClose = () => {
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      onClose();
    });
  };
  translateY.addListener(({ value }) => {
    if (value === Dimensions.get('window').height) {
      handleClose();
    }
  });

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, []);

  return (
    <Animated.View style={[{ opacity: fadeAnim }, styles.root]}>
      <TouchableOpacity
        onPress={() => {
          handleClose();
        }}
        style={styles.wrapper}
        activeOpacity={1}>
        <Animated.View
          {...panResponder.panHandlers}
          ref={modalPosition}
          onLayout={(e) => {
            const { x, y } = e.nativeEvent.layout;
            modalPosition.current = { x, y };
          }}
          style={[styles.container, { transform: [{ translateY }] }]}>
          <TouchableOpacity style={styles.disabledPress} activeOpacity={1}>
            <View style={styles.line}>
              <Icon iconSVG={assets.icons.popup_line_svg} width={34} height={10} />
            </View>
          </TouchableOpacity>
          {scrollable ? (
            <ScrollView style={{ maxHeight: Dimensions.get('screen').height / 1.25 }}>
              <Content>{children}</Content>
            </ScrollView>
          ) : (
            <Content>{children}</Content>
          )}

          {FooterComponent ? (
            <View style={styles.footer}>
              <FooterComponent />
            </View>
          ) : null}
        </Animated.View>
      </TouchableOpacity>
    </Animated.View>
  );
};
