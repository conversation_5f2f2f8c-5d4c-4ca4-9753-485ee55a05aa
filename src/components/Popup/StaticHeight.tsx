import React, { ReactElement, useEffect, useRef } from 'react';
import {
  Animated,
  Dimensions,
  PanResponder,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';

import { styles } from './styles';

import { Icon } from '~/components/Icon/Icon';
import { Content } from '~/components/Popup/components/Content';
import { assets } from '~/theme/assets';

type Props = {
  children: ReactElement;
  onClose: () => void;
  heightPercentage: number;
  FooterComponent?: () => ReactElement;
  scrollable?: boolean;
};

export const StaticHeight = ({
  children,
  onClose,
  heightPercentage = 1,
  FooterComponent,
  scrollable = true,
}: Props): ReactElement => {
  const screenHeight = Dimensions.get('window').height;
  const maxContainerHeight = screenHeight * heightPercentage;

  const position = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onPanResponderMove: Animated.event([null, { dy: position }], {
        useNativeDriver: false,
      }),
      onPanResponderRelease: (e, gesture) => {
        if (gesture.dy > 150 || gesture.vy > 0.5) {
          Animated.timing(position, {
            toValue: maxContainerHeight,
            duration: 200,
            useNativeDriver: false,
          }).start();
        } else {
          Animated.spring(position, {
            toValue: 0,
            useNativeDriver: false,
          }).start();
        }
      },
    }),
  ).current;

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, []);
  const handleClose = () => {
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      onClose();
    });
  };

  const containerHeight = position.interpolate({
    inputRange: [0, maxContainerHeight],
    outputRange: [maxContainerHeight, 0],
    extrapolate: 'clamp',
  });

  containerHeight.addListener(({ value }) => {
    if (value === 0) {
      handleClose();
    }
  });

  return (
    <Animated.View style={[{ opacity: fadeAnim }, styles.root]}>
      <TouchableOpacity
        onPress={() => {
          onClose();
        }}
        style={styles.wrapper}
        activeOpacity={1}>
        <Animated.View
          {...panResponder.panHandlers}
          style={[{ height: containerHeight }, styles.container]}>
          <View style={styles.line}>
            <Icon iconSVG={assets.icons.popup_line_svg} width={34} height={10} />
          </View>
          <TouchableOpacity style={styles.disabledPress} activeOpacity={1}>
            {scrollable ? (
              <ScrollView contentContainerStyle={{ paddingBottom: 30 }}>
                <Content>{children}</Content>
              </ScrollView>
            ) : (
              <Content>{children}</Content>
            )}
          </TouchableOpacity>

          {FooterComponent ? (
            <View style={styles.footer}>
              <FooterComponent />
            </View>
          ) : null}
        </Animated.View>
      </TouchableOpacity>
    </Animated.View>
  );
};
