import React, { useCallback } from 'react';
import { ActivityIndicator, FlatList, View } from 'react-native';

import { IList } from '~/components/List/types';
import { LAYOUT_PADDING } from '~/helpers/layoutPadding';

export const List = <T,>({
  renderItem,
  data,
  columnsSwitch,
  itemSeparator,
  onEndReached,
  loading,
  contentContainerStyle,
  ListEmptyComponent,
  refreshControl,
}: IList<T>) => {
  const renderItemFunction = useCallback(renderItem, [columnsSwitch]);
  return (
    <FlatList
      windowSize={100}
      key={columnsSwitch}
      refreshControl={refreshControl}
      onEndReached={onEndReached}
      ListEmptyComponent={ListEmptyComponent}
      ListFooterComponent={loading ? <ActivityIndicator size="large" /> : null}
      contentContainerStyle={[
        {
          paddingHorizontal: LAYOUT_PADDING,
          paddingTop: LAYOUT_PADDING,
          paddingBottom: 40,
        },
        contentContainerStyle,
      ]}
      data={data}
      keyExtractor={(item, index) => index.toString()}
      ItemSeparatorComponent={() => {
        if (itemSeparator) return <>{itemSeparator}</>;
        else return <View style={columnsSwitch > 1 ? { height: 8 } : { height: 16 }} />;
      }}
      columnWrapperStyle={
        columnsSwitch > 1 ? { justifyContent: 'space-between', gap: 18 } : undefined
      }
      numColumns={columnsSwitch > 1 ? 2 : 1}
      renderItem={renderItem}
    />
  );
};
