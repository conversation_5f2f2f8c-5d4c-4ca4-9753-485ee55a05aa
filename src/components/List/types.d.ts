import { ReactElement, ReactNode } from 'react';
import { ListRenderItem, StyleProp, ViewStyle } from 'react-native';

export interface IList<T> {
  data?: T[];
  renderItem: ListRenderItem<T>;
  columnsSwitch: number;
  itemSeparator?: ReactNode;
  onEndReached?: () => void;
  loading: boolean;
  contentContainerStyle?: StyleProp<ViewStyle>;
  ListEmptyComponent?: () => ReactElement;
  refreshControl?: ReactElement;
}

export interface IListItem {
  children: ReactNode;
  columnsSwitch: number;
  gap: number;
  screenWidth: number;
}
