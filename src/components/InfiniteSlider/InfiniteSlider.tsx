import React, { FC, ReactElement } from 'react';
import { Dimensions, View } from 'react-native';
import Swiper from 'react-native-swiper';

import * as Styled from './InfinieteSlider.styled';

import { Gallery } from '~/components/CarouselGallery/components/Gallery/Gallery';
import { Icon } from '~/components/Icon/Icon';
import { getItemWidth } from '~/helpers/getItemWidth';
import { assets } from '~/theme/assets';
import { IBanner } from '~/types/banner';
import { useBanners } from '~/hooks/useBanners';

interface IInfiniteSliderProps {
  data: IBanner[];
}

export const InfiniteSlider: FC<IInfiniteSliderProps> = ({ data }): ReactElement => {
  const { width } = Dimensions.get('window');
  const { handleDetails } = useBanners();

  const ITEM_WIDTH = getItemWidth();
  //2 / 8
  return (
    <Styled.Container>
      <Swiper
        style={{
          height: width > 600 ? 420 : 230,
          gap: 0,
          paddingHorizontal: 0,
          paddingVertical: 0,
          paddingTop: 8,
        }}
        pagingEnabled={true}
        showsButtons={false}
        showAdjacentViews={true}
        adjacentViewsPadding={0.5}
        adjacentViewsWidth={10}
        snapToAlignment={'center'}
        decelerationRate={0.7}
        loop={true}
        bounces={false}
        showsPagination={true}
        scrollEnabled={true}
        removeClippedSubviews={false}
        paginationStyle={{ bottom: 14 }}
        dot={
          <View style={{ marginHorizontal: 5 }}>
            <Icon iconSVG={assets.icons.line_svg} width={25} height={10} onPress={() => {}} />
          </View>
        }
        autoplay={true}
        autoplayTimeout={5}
        activeDot={
          <View style={{ marginHorizontal: 5 }}>
            <Icon
              iconSVG={assets.icons.line_active_svg}
              width={25}
              height={10}
              onPress={() => {}}
            />
          </View>
        }>
        {data.map((item, index) => {
          return (
            <Gallery
              key={index}
              width={ITEM_WIDTH}
              img={item.image}
              onPress={() => handleDetails(item)}
            />
          );
        })}
      </Swiper>
    </Styled.Container>
  );
};
