import styled from '@emotion/native';

import { flexColCenter } from '~/styles/flex.styled';
import { shadow } from '~/theme/shadows';

const sizes = {
  small: '80px',
  large: '120px',
};

export const Layout = styled.View<{ size: 'small' | 'large' }>`
  width: ${({ size }) => sizes[size]};
  height: ${({ size }) => sizes[size]};
  border-radius: 100px;
  margin: 0 5px 10px 5px;
  ${flexColCenter};
  ${shadow.gray[100]};
`;

export const Wrapper = styled.View<{ size: 'small' | 'large' }>`
  ${flexColCenter};
  width: ${({ size }) => sizes[size]};
  height: ${({ size }) => sizes[size]};
  ${shadow.gray['100']}
  border-radius: 100px;
  overflow: hidden;
`;
