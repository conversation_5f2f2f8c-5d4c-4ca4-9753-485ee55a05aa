import { Icon } from '../Icon/Icon';

import * as Styled from './BrandIcon.styled';

import { ImageComponent } from '~/components/ImageComponent/ImageComponent';
import { assets } from '~/theme/assets';

export interface IBrandIcon {
  size: 'small' | 'large';
  resizeMode?: 'contain' | 'cover' | 'stretch' | 'center';
  src: string;
  isActive?: boolean;
}

const sizesImage = {
  small: 56,
  large: 86,
};

export const BrandIcon = ({ size, src, isActive, resizeMode = 'contain' }: IBrandIcon) => {
  return (
    <Styled.Layout size={size}>
      {isActive ? (
        <ImageComponent
          borderRadius={8}
          uri={src}
          resizeMode={resizeMode}
          width={sizesImage[size]}
          height={sizesImage[size]}
        />
      ) : (
        <Icon iconSVG={assets.icons.klepsydry_szary_svg} width={48} height={48} />
      )}
    </Styled.Layout>
  );
};
