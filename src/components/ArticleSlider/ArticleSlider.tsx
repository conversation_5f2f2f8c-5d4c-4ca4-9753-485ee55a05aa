import React from 'react';
import { View } from 'react-native';

import { Item } from '~/components/Item/Item';
import { Slider } from '~/components/Slider/Slider';
import { getItemWidth } from '~/helpers/getItemWidth';
import { IHomeSubPage } from '~/types/home';

export const ArticleSlider = ({ data }: IHomeSubPage) => {
  return (
    <Slider
      ItemSeparatorComponent={() => <View style={{ width: 5 }} />}
      itemWidth={getItemWidth()}
      data={data}
      snapToAlignment={'center'}
      renderItem={({ item, index }) => (
        <Item
          id={item.id}
          width={getItemWidth()}
          key={index}
          itemContentProps={item.itemContentProps}
          itemHeaderProps={item.itemHeaderProps}
        />
      )}
    />
  );
};
