import { ReactElement } from 'react';
import { View } from 'react-native';

import { Typography } from '~/components/Typography/Typography';
import { convertEndDate, convertStartDate } from '~/helpers/convertDate';
import theme from '~/theme';

type Props = {
  brand?: string | null;
  title: string;
  duration?: {
    startDate: string;
    endDate?: string | null;
  };
  children?: ReactElement;
  size: 'small' | 'large';
  numberOfLines?: number;
};

const END_DATE_TEXT = 'DO ODWOŁANIA';
export const CardDesc = (props: Props) => {
  const sizes = {
    brand: {
      small: 10,
      large: 12,
    },
    title: {
      small: 15,
      large: 20,
    },
    duration: {
      small: 10,
      large: 12,
    },
  };

  const renderDuration = () => {
    if (props.duration) {
      const startDate = convertStartDate(props.duration.startDate);
      const endDate = props.duration.endDate
        ? convertEndDate(props.duration.endDate)
        : END_DATE_TEXT;

      return (
        <Typography
          fontSize={sizes.duration[props.size]}
          fontWeight="400"
          color={theme.colors.gray['800']}>
          {endDate ? `${startDate} - ${endDate}` : `${startDate}`}
        </Typography>
      );
    }
  };

  return (
    <View style={{ height: '100%', flex: 1, justifyContent: 'space-between' }}>
      <View>
        {props.brand ? (
          <Typography
            numberOfLines={1}
            fontSize={sizes.brand[props.size]}
            fontWeight="500"
            color={theme.colors.gray['800']}>
            {props.brand}
          </Typography>
        ) : null}

        <Typography
          numberOfLines={props.numberOfLines}
          fontSize={sizes.title[props.size]}
          fontWeight="600"
          color={theme.colors.black['300']}>
          {props.title}
        </Typography>
      </View>
      <View
        style={{
          justifyContent: 'flex-end',
        }}>
        {renderDuration()}
      </View>
      {props.children}
    </View>
  );
};
