import { FC } from 'react';
import { Dimensions, View } from 'react-native';

import * as Styled from './CardBgImage.styled';

import { ICardBgImage } from '~/components/Cards/components/types';
import { ImageComponent } from '~/components/ImageComponent/ImageComponent';
import theme from '~/theme';

export const CardBgImage: FC<ICardBgImage> = ({
  uri,
  triangle,
  borderRadius,
  bgColor = theme.colors.white['100'],
  isActive,
}) => {
  return (
    <Styled.Wrapper borderRadius={borderRadius}>
      {triangle ? (
        <Styled.Triangle
          trianglePosition={triangle.trianglePosition}
          triangleSize={triangle.triangleSize}
        />
      ) : null}
      <View
        style={{
          width: Dimensions.get('screen').width,
          height: '100%',
        }}>
        {isActive ? (
          <ImageComponent
            style={{
              flex: 1,
            }}
            borderRadius={5}
            uri={uri}
            bgColor={bgColor}
            resizeMode={'cover'}
          />
        ) : (
          <ImageComponent
            style={{
              flex: 1,
            }}
            bgColor="#bcbdc0"
            borderRadius={5}
            resizeMode={'cover'}
          />
        )}
      </View>
    </Styled.Wrapper>
  );
};
