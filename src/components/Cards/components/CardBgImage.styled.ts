import styled from '@emotion/native';

const triangleSizes = {
  small: '50px',
  medium: '150px',
  large: '200px',
};

const trianglePositions = {
  small: '-34px',
  medium: '-100px',
  large: '-180px',
};

export const Wrapper = styled.View<{ borderRadius?: number }>`
  height: 100%;
  width: 100%;
  overflow: hidden;
  border-radius: ${({ borderRadius }) => (borderRadius ? `${borderRadius}px` : '4px')};
  position: relative;
`;

export const Triangle = styled.View<{
  trianglePosition: 'top' | 'bottom';
  triangleSize: 'small' | 'medium' | 'large';
}>`
  z-index: 10;
  width: ${({ triangleSize }) => triangleSizes[triangleSize]};
  height: ${({ triangleSize }) => triangleSizes[triangleSize]};
  position: absolute;
  bottom: ${({ trianglePosition }) => (trianglePosition === 'top' ? 'auto' : '-100px')};
  top: ${({ trianglePosition }) => (trianglePosition === 'top' ? '-100px' : 'auto')};
  left: ${({ triangleSize }) => trianglePositions[triangleSize]};
  background-color: white;
  transform: rotate(45deg);
`;
