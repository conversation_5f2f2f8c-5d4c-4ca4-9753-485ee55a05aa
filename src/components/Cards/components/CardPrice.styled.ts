import styled from '@emotion/native';

import theme from '~/theme';

export const Wrapper = styled.View`
  display: flex;
  flex-direction: row;
  gap: 3px;
`;

export const Range = styled.View`
  display: flex;
`;
export const Minus = styled.View`
  width: 100%;
  height: 4px;
  background-color: ${theme.colors.white['100']};
  margin: 0;
  padding: 0;
`;
export const Currency = styled.View`
  display: flex;
  flex-direction: row;
  align-items: flex-end;
`;
export const CurrencySymbol = styled.View<{ type: boolean }>`
  padding-bottom: ${({ type }) => (type ? '0px' : '5px')};
`;
