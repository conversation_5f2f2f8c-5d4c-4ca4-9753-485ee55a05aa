import React, { FC } from 'react';

import * as Styled from './CardFavourite.styled';

import { ICardFavourite } from '~/components/Cards/components/types';
import { Icon } from '~/components/Icon/Icon';
import { useNavigate } from '~/hooks/useNavigate';
import { useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import theme from '~/theme';
import { assets } from '~/theme/assets';

export const CardFavourite: FC<ICardFavourite> = ({ active, onPress, size = 'large' }) => {
  const { token } = useAppSelector((state) => state.user);
  const { isConnected } = useAppSelector((state) => state.network);
  const { navigation } = useNavigate();

  return (
    <Styled.Wrapper
      onPress={() => {
        if (!isConnected) return;
        if (!token) navigation.navigate(RouteEnum.AUTH_MODAL);
        else onPress(active);
      }}
      isFavourite={active}
      size={size}>
      <Styled.Rotate>
        <Icon
          iconSVG={active ? assets.icons.bookmark_rotate_svg : assets.icons.bookmark_svg}
          width={24}
          height={24}
          isFill={true}
          isStroke={true}
          fill={active ? theme.colors.purple['100'] : theme.colors.gray[400]}
        />
      </Styled.Rotate>
    </Styled.Wrapper>
  );
};
