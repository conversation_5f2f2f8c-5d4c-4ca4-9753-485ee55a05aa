import { StyleProp, StyleSheet, Text, TextStyle, View } from 'react-native';

import { Typography } from '~/components/Typography/Typography';

type Props = {
  style1?: StyleProp<TextStyle>;
  style2?: StyleProp<TextStyle>;
};
export const CardExpiredTitle = ({ style1, style2 }: Props) => {
  return (
    <View style={styles.container}>
      <Typography fontSize={12} style={[styles.text1, style1]}>
        Oferta
      </Typography>
      <Typography fontSize={12} style={[styles.text2, style2]}>
        Wygasła
      </Typography>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {},
  text1: {
    fontSize: 24,
    color: 'white',
    fontWeight: '700',
  },
  text2: {
    fontSize: 24,
    color: 'white',
    fontWeight: '600',
  },
});
