import { FC } from 'react';

import * as Styled from './CardPrice.styled';

import { ICardPrice } from '~/components/Cards/components/types';
import { prepareCouponPrice } from '~/components/Cards/helpers/prepareData';
import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';
import { percentageValues } from '~/utils/constants/shared';

export const CardPrice: FC<ICardPrice> = ({
  discountValue,
  cutString,
  valueType,
  size = 'small',
}) => {
  if (!valueType && valueType !== 0) return null;
  const IS_PERCENTAGE = percentageValues.includes(valueType);
  const data = prepareCouponPrice(valueType, discountValue, cutString);

  return (
    <Styled.Wrapper>
      <Styled.Range>
        {data.range ? (
          <Typography
            fontSize={size === 'small' ? 15 : 25}
            fontWeight={size === 'small' ? '500' : '400'}
            color={theme.colors.white['100']}>
            {data.range}
          </Typography>
        ) : null}
        {IS_PERCENTAGE ? <Styled.Minus /> : null}
      </Styled.Range>
      <Styled.Currency>
        <Typography
          fontSize={size === 'small' ? 30 : 50}
          fontWeight="500"
          color={theme.colors.white['100']}>
          {data.price}
        </Typography>
        <Styled.CurrencySymbol type={IS_PERCENTAGE}>
          {data.unit ? (
            <Typography
              fontSize={size === 'small' ? (IS_PERCENTAGE ? 30 : 15) : IS_PERCENTAGE ? 50 : 25}
              fontWeight="500"
              color={theme.colors.white['100']}>
              {data.unit}
            </Typography>
          ) : null}
        </Styled.CurrencySymbol>
      </Styled.Currency>
    </Styled.Wrapper>
  );
};
