import { ReactNode } from 'react';

import { ValueTypeEnum } from '~/enums/shared';
import { IBaseBrand } from '~/types/brands';

export interface ICardLayout {
  children: ReactNode;
  onPress?: () => void;
  isShadow?: boolean;
}

export interface ICardPrice {
  valueType?: ValueTypeEnum;
  discountValue: string;
  cutString?: boolean;
  size?: 'small' | 'large';
}

export interface ICardBgImage {
  size?: 'small' | 'large';
  uri?: string | null;
  bgColor: string | null;
  borderRadius?: number;
  isActive?: boolean;
  triangle?: {
    trianglePosition: 'top' | 'bottom';
    triangleSize: 'medium' | 'large';
  };
}

export interface ICardFavourite {
  active: boolean | null;
  onPress: (isFavorite: boolean | null) => void;
  size?: 'small' | 'large';
}

export interface ICardImage {
  brand?: IBaseBrand;
  customLogo?: string | null;
  size: 'small' | 'large';
  isActive?: boolean;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'center';
}
