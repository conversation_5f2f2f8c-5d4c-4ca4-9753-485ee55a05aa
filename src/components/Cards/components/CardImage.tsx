import { FC } from 'react';

import { BrandIcon } from '~/components/BrandIcon/BrandIcon';
import { ICardImage } from '~/components/Cards/components/types';

export const CardImage: FC<ICardImage> = ({
  resizeMode = 'contain',
  brand,
  isActive,
  customLogo,
  size,
}) => {
  if (customLogo) {
    return <BrandIcon isActive={isActive} size={size} src={customLogo} resizeMode={resizeMode} />;
  } else if (brand && brand.logo) {
    return <BrandIcon isActive={isActive} size={size} src={brand.logo} resizeMode={resizeMode} />;
  } else {
    return null;
  }
};
