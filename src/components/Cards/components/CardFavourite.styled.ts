import styled from '@emotion/native';

import theme from '~/theme';

interface IWrapper {
  isFavourite: boolean | null;
  size: 'small' | 'large';
}

export const Wrapper = styled.TouchableOpacity<IWrapper>`
  border-radius: 5px;
  width: ${({ size }) => (size === 'small' ? '18px' : '35px')};
  height: ${({ size }) => (size === 'small' ? '18px' : '35px')};
  background-color: ${theme.colors.white['100']};
  align-items: center;
  justify-content: center;
`;

export const Rotate = styled.View`
  rotate: 45deg;
`;
