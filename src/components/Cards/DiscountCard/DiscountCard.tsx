import { FC } from 'react';
import { StyleSheet, View } from 'react-native';

import { CardExpiredTitle } from '../components/CardExpiredTitle';

import { bgImageSizes, brandPosition, descWrapperSizes, wrapperSizes } from './consts';

import { CardBgImage } from '~/components/Cards/components/CardBgImage';
import { CardDesc } from '~/components/Cards/components/CardDesc';
import { CardFavourite } from '~/components/Cards/components/CardFavourite';
import { CardImage } from '~/components/Cards/components/CardImage';
import { CardLayout } from '~/components/Cards/components/CardLayout';
import { CardPrice } from '~/components/Cards/components/CardPrice';
import { IDiscountCard } from '~/components/Cards/DiscountCard/types';
import * as Lofi from '~/components/Loader/Lofi';
import { validate } from '~/helpers/convertDate';

export const DiscountCardLofi = ({ width = 170 }: { width?: number }) => {
  return (
    <Lofi.Card width={width}>
      <Lofi.Favorite />
      <View
        style={{
          flex: 1,
          justifyContent: 'flex-end',
          alignItems: 'flex-end',
          gap: 10,
          paddingBottom: 10,
        }}>
        <Lofi.Text width="60%" />
        <Lofi.Text width="80%" />
        <Lofi.Text width="100%" />
      </View>
    </Lofi.Card>
  );
};

export const DiscountCard: FC<IDiscountCard> = ({
  data,
  isActive = true,
  onPress,
  onFavoritePress,
  size = 'small',
}) => {
  return (
    <CardLayout onPress={onPress}>
      <View style={[styles.wrapper, { height: wrapperSizes[size] }]}>
        <View style={[styles.imageWrapper, { height: bgImageSizes[size] }]}>
          <View style={styles.iconPriceWrapper}>
            {/*------FAVOURITE ROW WITH PRICE------*/}
            {isActive ? (
              <>
                <CardFavourite
                  active={data.is_favorite}
                  onPress={(isFavorite) => {
                    onFavoritePress(data.id, isFavorite, data.categories, data.tags);
                  }}
                  size="small"
                />
                <CardPrice
                  valueType={data.value_type}
                  discountValue={data.discount_value}
                  size={size}
                />
              </>
            ) : (
              <CardExpiredTitle />
            )}
          </View>
          <CardBgImage
            isActive={isActive}
            borderRadius={4}
            uri={data.bg_image ? data.bg_image : undefined}
            bgColor={data.bg_color}
            triangle={
              isActive
                ? {
                    triangleSize: 'large',
                    trianglePosition: 'top',
                  }
                : undefined
            }
          />
          <View style={[styles.brandWrapper, { bottom: brandPosition[size] }]}>
            <CardImage isActive={isActive} brand={data.brand} customLogo={data.logo} size={size} />
          </View>
        </View>
        <View style={[styles.descWrapper, { paddingTop: descWrapperSizes.padding[size] }]}>
          <CardDesc
            numberOfLines={2}
            brand={
              data.custom_brand_name ? data.custom_brand_name : data.brand ? data.brand.name : null
            }
            title={data.name}
            size={size}
          />
        </View>
      </View>
    </CardLayout>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    paddingHorizontal: 5,
    paddingTop: 5,
    paddingBottom: 15,
  },
  imageWrapper: {
    position: 'relative',
    width: '100%',
  },
  iconPriceWrapper: {
    position: 'absolute',
    zIndex: 10,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 6,
    paddingLeft: 6,
    paddingRight: 8,
    paddingBottom: 0,
  },
  brandWrapper: {
    position: 'absolute',
    padding: 7,
    right: 0,
    zIndex: 100,
  },
  descWrapper: {
    height: '100%',
    paddingLeft: 5,
    paddingRight: 5,
  },
});
