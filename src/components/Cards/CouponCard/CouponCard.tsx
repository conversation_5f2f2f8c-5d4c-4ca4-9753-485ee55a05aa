import { FC } from 'react';
import { StyleSheet, View } from 'react-native';

import { CardExpiredTitle } from '../components/CardExpiredTitle';

import { bgImageSizes, brandPosition, descWrapperSizes, wrapperSizes } from './consts';

import { CardBgImage } from '~/components/Cards/components/CardBgImage';
import { CardDesc } from '~/components/Cards/components/CardDesc';
import { CardFavourite } from '~/components/Cards/components/CardFavourite';
import { CardImage } from '~/components/Cards/components/CardImage';
import { CardLayout } from '~/components/Cards/components/CardLayout';
import { CardPrice } from '~/components/Cards/components/CardPrice';
import { ICouponCard } from '~/components/Cards/CouponCard/types';
import * as Lofi from '~/components/Loader/Lofi';
import { ValueTypeEnum } from '~/enums/shared';

export const CouponCardLofi = ({ width = 170 }: { width?: number }) => {
  return (
    <Lofi.Card width={width}>
      <Lofi.Favorite />
      <View
        style={{
          flex: 1,
          justifyContent: 'flex-end',
          alignItems: 'flex-end',
          gap: 10,
          paddingBottom: 10,
        }}>
        <Lofi.Text width={'60%'} />
        <Lofi.Text width={'80%'} />
        <Lofi.Text width={'100%'} />
      </View>
    </Lofi.Card>
  );
};

export const CouponCard: FC<ICouponCard> = ({
  isActive = true,
  size = 'small',
  onFavoritePress,
  data,
  onPress,
}) => {
  return (
    <CardLayout onPress={onPress}>
      <View style={[styles.wrapper, { height: wrapperSizes[size] }]}>
        <View style={[styles.imageWrapper, { height: bgImageSizes[size] }]}>
          <View style={styles.iconPriceWrapper}>
            {/*------FAVOURITE ROW WITH PRICE------*/}
            {isActive ? (
              <>
                <CardFavourite
                  active={data.is_favorite}
                  onPress={(isFavorite) => {
                    onFavoritePress(data.id, isFavorite, data.categories, data.tags);
                  }}
                />
                <CardPrice
                  size={size}
                  valueType={data.value_type ?? ValueTypeEnum.OTHER}
                  discountValue={data.discount_value}
                />
              </>
            ) : (
              <CardExpiredTitle />
            )}
          </View>
          <View style={{ borderRadius: 4, overflow: 'hidden' }}>
            <CardBgImage
              isActive={isActive}
              uri={data.bg_image ? data.bg_image : undefined}
              bgColor={data.bg_color}
            />
          </View>
          <View style={[styles.brandWrapper, { bottom: brandPosition[size] }]}>
            <CardImage isActive={isActive} brand={data.brand} customLogo={data.logo} size={size} />
          </View>
        </View>
        <View
          style={[
            styles.descWrapper,
            {
              paddingTop: descWrapperSizes.padding[size],
              width: descWrapperSizes.width[size],
            },
          ]}>
          <CardDesc
            numberOfLines={2}
            brand={
              data.custom_brand_name ? data.custom_brand_name : data.brand ? data.brand.name : null
            }
            title={data.title}
            duration={{
              startDate: data.start_date,
              endDate: data.end_date,
            }}
            size={size}
          />
        </View>
      </View>
    </CardLayout>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    paddingHorizontal: 5,
    paddingTop: 5,
  },
  imageWrapper: {
    position: 'relative',
    width: '100%',
  },
  iconPriceWrapper: {
    position: 'absolute',
    zIndex: 10,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 8,
    paddingTop: 8,
  },
  brandWrapper: {
    position: 'absolute',
    padding: 8,
    right: 0,
    zIndex: 10,
  },
  descWrapper: {
    paddingLeft: 5,
    paddingRight: 5,
    paddingBottom: 8,
    flex: 1,
  },
});
