import { FC } from 'react';
import { StyleSheet, View } from 'react-native';

import { CardBgImage } from '~/components/Cards/components/CardBgImage';
import { CardDesc } from '~/components/Cards/components/CardDesc';
import { CardLayout } from '~/components/Cards/components/CardLayout';
import { ProductPrice } from '~/components/Cards/ProductCard/components/ProductPrice';
import { IProductCard } from '~/components/Cards/ProductCard/types';
import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';

export const ProductCard: FC<IProductCard> = ({ data, onPress }) => {
  return (
    <CardLayout onPress={onPress}>
      <View style={styles.wrapper}>
        <View>
          <View style={styles.imageWrapper}>
            <CardBgImage
              borderRadius={4}
              uri={data.bg_image ? data.bg_image : undefined}
              bgColor={'#fff'}
            />
          </View>
          <View style={styles.descWrapper}>
            <CardDesc
              brand={data.producer ? data.producer : null}
              title={data.producer ? data.producer : ''}
              size={'small'}>
              {data.parameters ? (
                <Typography fontSize={12} color={theme.colors.black['300']} fontWeight="400">
                  {data.parameters}
                </Typography>
              ) : (
                <></>
              )}
            </CardDesc>
            <ProductPrice price={data.price} promotion_price={data.promotion_price} />
          </View>
        </View>
      </View>
    </CardLayout>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    width: '100%',
    height: 250,
    paddingHorizontal: 5,
    paddingTop: 5,
    paddingBottom: 15,
  },
  imageWrapper: {
    position: 'relative',
    width: '100%',
    height: 125,
    borderRadius: 4,
  },
  descWrapper: {
    paddingLeft: 5,
    paddingRight: 5,
    paddingTop: 25,
  },
});
