import { StyleSheet, View } from 'react-native';

import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';
import { IProduct } from '~/types/product';

export const ProductPrice = (props: Pick<IProduct, 'price' | 'promotion_price'>) => {
  return (
    <View style={styles.productPrice}>
      {props.price ? (
        <Typography fontSize={16} fontWeight="600" color={theme.colors.black['300']}>
          {props.price}
        </Typography>
      ) : null}

      {props.promotion_price ? (
        <Typography
          style={{
            textDecorationLine: 'line-through',
            textDecorationStyle: 'solid',
            textDecorationColor: theme.colors.black['100'],
          }}
          fontSize={14}
          fontWeight="400"
          color={theme.colors.black['300']}>
          {props.promotion_price}
        </Typography>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  productPrice: {
    paddingTop: 10,
    gap: 10,
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
});
