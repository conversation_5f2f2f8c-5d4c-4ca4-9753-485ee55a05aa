import { DiscountValueTypeEnum, ValueTypeEnum } from '~/enums/shared';
import { range, unit } from '~/utils/constants/shared';

type OutputPrice = {
  range: string | null;
  unit: string | null;
  price: string;
};

export const prepareCouponPrice = (
  valueType: ValueTypeEnum,
  discountValue: string,
  cutString?: boolean,
): OutputPrice => {
  if (valueType === ValueTypeEnum.OTHER) {
    return {
      range: '',
      unit: '',
      price:
        cutString && discountValue.length > 7
          ? discountValue.slice(0, 7).concat('...')
          : discountValue,
    };
  } else {
    return {
      range: range[valueType],
      unit: unit[valueType].toUpperCase(),
      price: discountValue,
    };
  }
};

export const prepareDiscountPrice = (
  valueType: DiscountValueTypeEnum,
  discountValue: string,
): OutputPrice => {
  if (valueType === DiscountValueTypeEnum.PERCENTAGE) {
    return {
      range: '',
      unit: '%',
      price: discountValue,
    };
  } else {
    return {
      range: '',
      unit: 'ZŁ',
      price: discountValue,
    };
  }
};

export const prepareTitle = (title: string) => {
  if (title.length > 40) {
    return title.slice(0, 40);
  } else {
    return title;
  }
};
