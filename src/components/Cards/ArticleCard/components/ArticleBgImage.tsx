import { FC } from 'react';

import * as Styled from '../../components/CardBgImage.styled';

import { ICardBgImage } from '~/components/Cards/components/types';
import { ImageComponent } from '~/components/ImageComponent/ImageComponent';
import theme from '~/theme';

export const ArticleBgImage: FC<ICardBgImage> = ({
  uri,
  triangle,
  borderRadius,
  bgColor = theme.colors.white['100'],
}) => {
  return (
    <Styled.Wrapper borderRadius={borderRadius}>
      {triangle ? (
        <Styled.Triangle
          trianglePosition={triangle.trianglePosition}
          triangleSize={triangle.triangleSize}
        />
      ) : null}
      <ImageComponent borderRadius={5} uri={uri} bgColor={bgColor} resizeMode={'cover'} />
    </Styled.Wrapper>
  );
};
