import { StyleSheet, View } from 'react-native';

import { bgImageSizes, wrapperSizes } from './consts';

import { ArticleBgImage } from '~/components/Cards/ArticleCard/components/ArticleBgImage';
import { IArticleCard } from '~/components/Cards/ArticleCard/types';
import { CardFavourite } from '~/components/Cards/components/CardFavourite';
import { CardLayout } from '~/components/Cards/components/CardLayout';
import * as Lofi from '~/components/Loader/Lofi';
import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';

export const ArticleCardLofi = ({ width }: { width: number }) => {
  return (
    <Lofi.Card width={width}>
      <Lofi.Favorite />
      <View
        style={{
          flex: 1,
          justifyContent: 'flex-end',
          alignItems: 'flex-end',
          gap: 10,
          paddingBottom: 10,
        }}>
        <Lofi.Text width={'60%'} />
        <Lofi.Text width={'80%'} />
        <Lofi.Text width={'100%'} />
      </View>
    </Lofi.Card>
  );
};

export const ArticleCard = ({ size = 'small', data, onPress, onFavoritePress }: IArticleCard) => {
  return (
    <CardLayout isShadow={false} onPress={onPress}>
      <View
        style={[
          styles.wrapper,
          {
            height: wrapperSizes[size],
          },
        ]}>
        <View
          style={[
            styles.imageWrapper,
            {
              height: bgImageSizes[size],
            },
          ]}>
          <ArticleBgImage
            borderRadius={8}
            uri={data.first_image ? data.first_image : undefined}
            bgColor={theme.colors.white['100']}
          />
          <View style={styles.iconWrapper}>
            <CardFavourite
              active={data.is_favorite}
              onPress={(isFavorite) => {
                onFavoritePress(data.id, isFavorite, data.categories, data.tags);
              }}
            />
          </View>
        </View>
        <View
          style={[
            styles.desc,
            {
              gap: 10,
              paddingHorizontal: 5,
              paddingVertical: 0,
            },
          ]}>
          <View style={{ justifyContent: 'center', marginTop: 10 }}>
            <Typography
              fontSize={size === 'small' ? 15 : 17}
              fontWeight="600"
              numberOfLines={3}
              color={theme.colors.black['300']}>
              {data.title}
            </Typography>
          </View>
          <View style={{ marginTop: 'auto' }}>
            <Typography
              numberOfLines={1}
              fontSize={12}
              fontWeight="500"
              color={theme.colors.gray['800']}>
              {data.brand?.name}
            </Typography>
          </View>
        </View>
      </View>
    </CardLayout>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    width: '100%',
  },
  imageWrapper: {
    position: 'relative',
    overflow: 'hidden',
  },
  iconWrapper: {
    position: 'absolute',
    top: 7,
    left: 7,
  },
  desc: {
    justifyContent: 'space-between',
    flex: 0.9,
  },
});
