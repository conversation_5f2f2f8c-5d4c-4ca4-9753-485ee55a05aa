import { StyleSheet, View } from 'react-native';

import { CardExpiredTitle } from '../components/CardExpiredTitle';

import { bgImageSizes, brandPosition, descWrapperSizes, wrapperSizes } from './consts';

import { CardDesc } from '~/components/Cards/components/CardDesc';
import { CardFavourite } from '~/components/Cards/components/CardFavourite';
import { CardImage } from '~/components/Cards/components/CardImage';
import { CardLayout } from '~/components/Cards/components/CardLayout';
import { NewspaperBackground } from '~/components/Cards/NewspaperCard/components/NewspaperBackground';
import { INewspaperCard } from '~/components/Cards/NewspaperCard/types';
import * as Lofi from '~/components/Loader/Lofi';
import { validate } from '~/helpers/convertDate';

export const NewspaperCardLofi = ({ width = 170 }: { width?: number }) => {
  return (
    <Lofi.Card width={width} height={350}>
      <Lofi.Favorite height={'70%'} />
      <View
        style={{
          flex: 1,
          justifyContent: 'flex-end',
          alignItems: 'flex-end',
          gap: 10,
          paddingBottom: 10,
        }}>
        <Lofi.Text width={'60%'} />
        <Lofi.Text width={'80%'} />
        <Lofi.Text width={'100%'} />
      </View>
    </Lofi.Card>
  );
};

export const NewspaperCard = ({
  size = 'small',
  data,
  isActive = true,
  onFavoritePress,
  onPress,
}: INewspaperCard) => {
  return (
    <CardLayout onPress={onPress}>
      <View
        style={[
          styles.wrapper,
          {
            height: wrapperSizes[size],
          },
        ]}>
        <View
          style={[
            styles.iconWrapper,
            {
              height: bgImageSizes[size],
            },
          ]}>
          {isActive ? (
            <NewspaperBackground uri={data?.image ? data.image : undefined} bgColor={'#fff'} />
          ) : (
            <NewspaperBackground bgColor="#bcbdc0" />
          )}

          <View style={styles.favouriteWrapper}>
            {isActive && (
              <CardFavourite
                active={data.is_favorite}
                onPress={(isFavorite) => {
                  onFavoritePress(data.id, isFavorite, data.categories, data.tags);
                }}
              />
            )}
          </View>
          {isActive ? null : (
            <View
              style={{
                position: 'absolute',
                alignSelf: 'center',
                top: 60,
              }}>
              <CardExpiredTitle style1={{ textAlign: 'center' }} />
            </View>
          )}
        </View>
        {data?.brand?.logo ? (
          <View
            style={[
              styles.imageWrapper,
              {
                bottom: brandPosition[size],
              },
            ]}>
            <CardImage isActive={isActive} brand={data.brand} size={size} />
          </View>
        ) : null}
        <View
          style={[
            styles.descWrapper,
            {
              paddingTop: descWrapperSizes.padding[size],
              width: descWrapperSizes.width[size],
            },
          ]}>
          <CardDesc
            numberOfLines={2}
            brand={data.brand ? data.brand.name : null}
            title={data.title}
            duration={{
              startDate: data.valid_from,
              endDate: data.valid_to,
            }}
            size={size}
          />
        </View>
      </View>
    </CardLayout>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    width: '100%',
    paddingHorizontal: 5,
    paddingTop: 5,
    paddingBottom: 10,
  },
  iconWrapper: {
    position: 'relative',
  },
  favouriteWrapper: {
    position: 'absolute',
    top: 7,
    left: 7,
  },
  imageWrapper: {
    position: 'absolute',
    right: 12,
  },
  descWrapper: {
    gap: 2,
    flex: 1,
    paddingHorizontal: 5,
  },
});
