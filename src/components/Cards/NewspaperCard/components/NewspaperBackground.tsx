import { FC } from 'react';
import { StyleSheet, View } from 'react-native';

import { INewspaperBackground } from '~/components/Cards/NewspaperCard/types';
import { ImageComponent } from '~/components/ImageComponent/ImageComponent';
import theme from '~/theme';

const triangleSizes = {
  small: '50px',
  medium: '150px',
  large: '200px',
};

const trianglePositions = {
  small: '-34px',
  medium: '-100px',
  large: '-180px',
};

export const NewspaperBackground: FC<INewspaperBackground> = ({
  uri,
  triangle,
  bgColor = theme.colors.white['100'],
}) => {
  return (
    <View style={styles.wrapper}>
      {triangle ? (
        <View
          style={[
            styles.triangle,
            {
              width: triangleSizes[triangle.triangleSize],
              height: triangleSizes[triangle.triangleSize],
              bottom: triangle.trianglePosition === 'top' ? 'auto' : '-100px',
              top: triangle.trianglePosition === 'top' ? '-100px' : 'auto',
              left: trianglePositions[triangle.triangleSize],
              transform: [{ rotate: '45deg' }],
            },
          ]}
        />
      ) : null}
      <ImageComponent borderRadius={4} uri={uri} bgColor={bgColor} resizeMode="cover" />
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    height: '100%',
    width: '100%',
    overflow: 'hidden',
    position: 'relative',
  },
  triangle: {
    zIndex: 10,
    position: 'absolute',
    backgroundColor: 'white',
  },
});
