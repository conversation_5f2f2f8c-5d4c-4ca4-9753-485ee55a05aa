import { INewspaper } from '~/types/newspaper';
import { TOnFavoritePress } from '~/types/onFavoritePress';

export interface INewspaperBackground {
  uri?: string | null;
  bgColor: string | null;
  triangle?: {
    trianglePosition: 'top' | 'bottom';
    triangleSize: 'medium' | 'large';
  };
}

export interface INewspaperCard {
  size?: 'small' | 'large';
  data: INewspaper;
  height?: number;
  isActive?: boolean;
  onPress: () => void;
  onFavoritePress: TOnFavoritePress;
}
