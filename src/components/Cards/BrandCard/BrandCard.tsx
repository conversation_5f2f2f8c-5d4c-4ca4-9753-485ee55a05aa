import { StyleSheet, TouchableOpacity, View } from 'react-native';

import { IBrandCard } from '~/components/Cards/BrandCard/types';
import { CardFavourite } from '~/components/Cards/components/CardFavourite';
import { ImageComponent } from '~/components/ImageComponent/ImageComponent';
import * as Lofi from '~/components/Loader/Lofi';
import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';
import { shadow } from '~/theme/shadows';

export const BrandCardLofi = () => {
  return (
    <Lofi.Card>
      <Lofi.Circle />
    </Lofi.Card>
  );
};

export const BrandCard = ({ data, onPress, onFavoritePress }: IBrandCard) => {
  if (!data) return <BrandCardLofi />;
  return (
    <TouchableOpacity style={[styles.layout, shadow.gray[100]]} onPress={onPress}>
      <View style={styles.wrapper}>
        <View style={styles.section}>
          <View style={styles.favourite}>
            <CardFavourite
              active={data.is_favorite}
              onPress={(isFavorite) => {
                onFavoritePress(data.id, isFavorite, data.categories, data.tags);
              }}
            />
          </View>
          {data.logo ? (
            <View style={[styles.image, shadow.gray[100]]}>
              <ImageComponent
                uri={data.logo}
                width={86}
                height={86}
                resizeMode={'contain'}
                style={{ aspectRatio: 1 }}
              />
            </View>
          ) : null}
        </View>
        <View
          style={{
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <Typography
            numberOfLines={2}
            fontSize={15}
            color={theme.colors.black[300]}
            style={{ textAlign: 'center' }}
            fontWeight="600">
            {data.name.toUpperCase()}
          </Typography>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  layout: {
    height: 220,
    borderRadius: 5,
  },
  wrapper: {
    backgroundColor: theme.colors.white[200],
    paddingHorizontal: 4,
    paddingTop: 4,
    paddingBottom: 12,
    borderRadius: 5,
    height: '100%',
  },
  section: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  favourite: {
    width: '100%',
  },
  image: {
    width: 120,
    height: 120,
    borderRadius: 120,
    marginTop: 0,
    marginHorizontal: 5,
    marginBottom: 10,
    overflow: 'hidden',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
