import { IMenuItem, IMenuMoreItem } from '~/components/Menu/types';
import { RouteEnum } from '~/routes/routes';
import { assets } from '~/theme/assets';

export enum MenuEnum {
  HOME = 'Home',
  PREMIUM = 'Strefa Premium',
  PROFILE = 'Mój Profit',
  MORE = 'Więcej',
}

export const menuMock: IMenuItem[] = [
  {
    id: 1,
    title: MenuEnum.HOME,
    path: RouteEnum.HOME,
    icon: assets.icons.bookmark_svg,
    size: 24,
  },
  {
    id: 2,
    title: MenuEnum.PREMIUM,
    path: RouteEnum.PREMIUM,
    icon: assets.icons.star_svg,
    size: 28,
  },
  {
    id: 3,
    title: MenuEnum.PROFILE,
    path: RouteEnum.PROFILE,
    icon: assets.icons.bookmark_rotate_svg,
    size: 24,
  },
  {
    id: 4,
    title: MenuEnum.MORE,
    icon: assets.icons.more_svg,
    size: 24,
  },
];

export const menuMoreMock: IMenuMoreItem[] = [
  {
    id: 1,
    icon: assets.icons.menu_homeprofit_svg,
    path: RouteEnum.DISCOUNT,
    title: 'Rabaty homeprofit',
    size: 28,
  },
  {
    id: 1,
    icon: assets.icons.menu_coupons_svg,
    path: RouteEnum.COUPON,
    title: 'Okazje',
    size: 24,
  },
  {
    id: 1,
    icon: assets.icons.menu_newspapers_svg,
    path: RouteEnum.NEWSPAPER,
    title: 'Gazetki',
    size: 28,
  },
  {
    id: 1,
    icon: assets.icons.menu_brands_svg,
    path: RouteEnum.BRAND,
    title: 'Strefa marek',
    size: 28,
  },
  {
    id: 1,
    icon: assets.icons.menu_articles_svg,
    path: RouteEnum.ARTICLE,
    title: 'artykuły',
    size: 24,
  },
  {
    id: 1,
    icon: assets.icons.menu_inspirations_svg,
    path: RouteEnum.INSPIRATION,
    title: 'inspiracje',
    size: 28,
  },
];
