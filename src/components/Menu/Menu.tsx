import React, { FC, ReactElement, useState } from 'react';

import * as Styled from './Menu.styled';

import { Button } from '~/components/Button/Button';
import { ButtonVariant } from '~/components/Button/enum';
import { Icon } from '~/components/Icon/Icon';
import { MenuItem } from '~/components/Menu/components/MenuItem/MenuItem';
import { MenuMoreItem } from '~/components/Menu/components/MenuMoreItem/MenuMoreItem';
import { IMenuItem, IMenuMoreItem, IMenuProps } from '~/components/Menu/types';
import { Overlay } from '~/components/Overlay/Overlay';
import * as Popup from '~/components/Popup';
import { Typography } from '~/components/Typography/Typography';
import { useNavigate } from '~/hooks/useNavigate';
import { RouteEnum } from '~/routes/routes';
import theme from '~/theme';
import { assets } from '~/theme/assets';

export const Menu: FC<IMenuProps> = ({ menuCollection, menuMoreCollection }): ReactElement => {
  const [showModal, setShowModal] = useState<boolean>(false);
  const { navigation } = useNavigate();

  return (
    <Styled.Container>
      {menuCollection.map((menuItem: IMenuItem) => (
        <MenuItem key={menuItem.id} menuItem={menuItem} onMorePress={() => setShowModal(true)} />
      ))}
      {showModal && (
        <Overlay
          onClose={() => setShowModal(false)}
          isVisible={showModal}
          animationType="fade"
          onBackdropPress={() => setShowModal(false)}>
          <Popup.StaticHeight heightPercentage={0.85} onClose={() => setShowModal(false)}>
            <Styled.MoreContainer>
              <Styled.MenuHeader>
                <Styled.MenuIcon>
                  <Icon
                    iconSVG={assets.icons.bookmark_rotate_svg}
                    width={32}
                    height={32}
                    fill={theme.colors.white[100]}
                    isFill={true}
                    isStroke={false}
                  />
                </Styled.MenuIcon>
                <Typography fontSize={24} fontWeight="600" color={theme.colors.black[100]}>
                  Więcej profitów
                </Typography>
              </Styled.MenuHeader>

              {menuMoreCollection?.map((menuMoreItem: IMenuMoreItem, index) => (
                <MenuMoreItem
                  key={index}
                  title={menuMoreItem.title}
                  icon={menuMoreItem.icon}
                  path={menuMoreItem.path}
                  size={menuMoreItem.size}
                  setShowModal={() => setShowModal(false)}
                />
              ))}

              <Styled.Contact>
                <Button
                  variant={ButtonVariant.FOURTH}
                  borderRadius={100}
                  typographyStyles={{
                    fontWeight: '600',
                    fontSize: 14,
                    color: theme.colors.purple['100'],
                  }}
                  onPress={() => {
                    setShowModal(false);
                    navigation.navigate(RouteEnum.CONTACT);
                  }}>
                  {`kontakt`.toUpperCase()}
                </Button>
              </Styled.Contact>
            </Styled.MoreContainer>
          </Popup.StaticHeight>
        </Overlay>
      )}
    </Styled.Container>
  );
};
