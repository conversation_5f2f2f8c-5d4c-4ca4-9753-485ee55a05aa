import React, { FC, ReactElement } from 'react';

import * as Styled from './MenuMoreItem.styled';

import { Icon } from '~/components/Icon/Icon';
import { IMenuMoreItemProps } from '~/components/Menu/types';
import { Typography } from '~/components/Typography/Typography';
import { useFilters } from '~/hooks/useFilters';
import { useNavigate } from '~/hooks/useNavigate';
import theme from '~/theme';

export const MenuMoreItem: FC<IMenuMoreItemProps> = ({
  title,
  icon,
  path,
  size,
  setShowModal,
}): ReactElement => {
  const { navigation } = useNavigate();
  const { removeAllFilters } = useFilters();
  return (
    <Styled.Container
      onPress={() => {
        setShowModal();
        removeAllFilters();
        navigation.navigate(path);
      }}>
      <Typography fontSize={14} fontWeight="500" color={theme.colors.gray[700]}>
        {title.toUpperCase()}
      </Typography>
      <Icon iconSVG={icon} width={size} height={size} />
    </Styled.Container>
  );
};
