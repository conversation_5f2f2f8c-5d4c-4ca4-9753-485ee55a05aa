import React, { FC, ReactElement, useContext, useEffect, useState } from 'react';

import * as Styled from './MenuItem.styled';

import { Icon } from '~/components/Icon/Icon';
import { MenuEnum } from '~/components/Menu/data';
import { routesSchema } from '~/components/Menu/data/routesSchema';
import { MenuItemProps } from '~/components/Menu/types';
import { Typography } from '~/components/Typography/Typography';
import { eventsContext } from '~/context/eventsContext';
import { homePageScrollContext } from '~/context/homePageScrollContext';
import { useNavigate } from '~/hooks/useNavigate';
import { useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import theme from '~/theme';

export const MenuItem: FC<MenuItemProps> = ({ menuItem, onMorePress }): ReactElement => {
  const { navigation } = useNavigate();
  const { activeRoute } = useContext(eventsContext);
  const { ref } = useContext(homePageScrollContext);
  const [isActive, setIsActive] = useState<boolean>(false);
  const { token } = useAppSelector((state) => state.user);

  const handlePress = () => {
    if (menuItem.path) {
      if (menuItem.path === activeRoute && menuItem.path === RouteEnum.HOME) {
        ref.current?.scrollTo({ x: 0, y: 0, animated: true });
      } else if (menuItem.title === MenuEnum.PROFILE && !token) {
        navigation.navigate(RouteEnum.AUTH_MODAL);
      } else {
        navigation.navigate(menuItem.path, menuItem.params);
      }
    } else if (onMorePress && menuItem.title === MenuEnum.MORE) {
      onMorePress();
    }
  };

  const handleIsActive = () => {
    if (menuItem.path) {
      setIsActive(routesSchema[menuItem.path].routes.includes(activeRoute));
    }
  };

  useEffect(() => {
    handleIsActive();
  }, [activeRoute]);

  return (
    <Styled.Container onPress={handlePress}>
      <Icon
        iconSVG={menuItem.icon}
        width={menuItem.size}
        height={menuItem.size}
        fill={isActive ? theme.colors.purple[100] : theme.colors.gray[900]}
        isStroke={false}
        isFill={true}
      />
      <Typography fontSize={10} color={theme.colors.gray['100']} fontWeight="300">
        {menuItem.title}
      </Typography>
    </Styled.Container>
  );
};
