import { MenuEnum } from '~/components/Menu/data';
import { RouteEnum } from '~/routes/routes';

export interface IMenuItem {
  id: number;
  title: MenuEnum;
  path?:
    | RouteEnum.DISCOUNT
    | RouteEnum.COUPON
    | RouteEnum.NEWSPAPER
    | RouteEnum.ARTICLE
    | RouteEnum.BRAND
    | RouteEnum.HOME
    | RouteEnum.PREMIUM
    | RouteEnum.AUTH_MODAL
    | RouteEnum.PROFILE
    | RouteEnum.INSPIRATION;
  params?: any;
  icon: any;
  size: number;
}

export interface IMenuMoreItem {
  id: number;
  icon: any;
  title: string;
  size: number;
  path:
    | RouteEnum.DISCOUNT
    | RouteEnum.COUPON
    | RouteEnum.NEWSPAPER
    | RouteEnum.ARTICLE
    | RouteEnum.BRAND
    | RouteEnum.AUTH_MODAL
    | RouteEnum.PROFILE
    | RouteEnum.INSPIRATION;
}

interface IMenuMoreItemProps {
  title: string;
  icon: any;
  size: number;
  path:
    | RouteEnum.DISCOUNT
    | RouteEnum.COUPON
    | RouteEnum.NEWSPAPER
    | RouteEnum.ARTICLE
    | RouteEnum.BRAND
    | RouteEnum.AUTH_MODAL
    | RouteEnum.PROFILE
    | RouteEnum.INSPIRATION;
  setShowModal: () => void;
}

interface MenuItemProps {
  menuItem: IMenuItem;
  onMorePress?: () => void;
}

interface IMenuProps {
  menuCollection: IMenuItem[];
  menuMoreCollection: IMenuMoreItem[];
}
