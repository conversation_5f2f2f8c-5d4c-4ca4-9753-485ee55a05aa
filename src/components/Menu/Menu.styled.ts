import styled from '@emotion/native';

import { flexColCenter } from '~/styles/flex.styled';
import theme from '~/theme';

export const Container = styled.View`
  display: flex;
  flex-direction: row;
  background-color: ${theme.colors.white[100]};
  justify-content: space-around;
  align-items: flex-end;
  padding-bottom: 4px;
`;

export const MoreContainer = styled.View`
  &:last-child {
    border-bottom: none;
  }
`;

export const MenuIcon = styled.View`
  padding: 4px 0 0 4px;
  width: 48px;
  height: 48px;
  ${flexColCenter};
  border-radius: 100px;
  background-color: ${theme.colors.purple[100]};
`;

export const MenuHeader = styled.View`
  ${flexColCenter};
  gap: 8px;
  margin-bottom: 30px;
`;

export const Contact = styled.View`
  margin: 30px 16px;
  align-items: center;
`;
