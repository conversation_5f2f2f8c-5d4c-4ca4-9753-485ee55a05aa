import { StyleSheet, View } from 'react-native';

import { Icon } from '../Icon/Icon';
import { Typography } from '../Typography/Typography';

import theme from '~/theme';
import { assets } from '~/theme/assets';

export const NetworkError = () => {
  return (
    <View style={styles.container}>
      <Typography fontSize={20} style={styles.text}>
        Wystąpił błąd podczas ładowania danych. Sprawdź połączenie z internetem i spróbuj ponownie.
      </Typography>
    </View>
  );
};

export const NetworkErrorModal = () => {
  return (
    <View style={[styles.modalContainer]}>
      <Icon iconSVG={assets.icons.wifi_off_svg} width={24} height={24} />
      <Typography fontSize={14} style={styles.modalText} color={theme.colors.white[100]}>
        Brak internetu
      </Typography>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    textAlign: 'center',
    color: theme.colors.gray['800'],
  },

  modalContainer: {
    position: 'absolute',
    top: 10,
    zIndex: 999,
    flexDirection: 'row',
    gap: 8,
    backgroundColor: theme.colors.purple[100],
    width: 200,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    padding: 4,
    borderRadius: 16,
  },
  modalText: {
    color: theme.colors.white[100],
  },
});
