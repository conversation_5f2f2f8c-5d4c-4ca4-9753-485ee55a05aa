import { TouchableOpacity } from 'react-native';

import * as Styled from './ListOptions.styled';

import { Icon } from '~/components/Icon/Icon';
import { IListOptions } from '~/components/ListOptions/types';
import { Typography } from '~/components/Typography/Typography';
import { useAppSelector } from '~/redux/store';
import theme from '~/theme';
import { assets } from '~/theme/assets';

export const ListOptions = ({
  setColumns,
  numColumns,
  isFilter,
  isSwitch,
  desc,
  filterLength,
  onFilterPress,
  singleSwitch = false,
}: IListOptions) => {
  const { isConnected } = useAppSelector((state) => state.network);
  return (
    <Styled.Wrapper>
      {desc !== undefined ? (
        <Styled.ItemWrapper>
          <Typography fontSize={16} fontWeight="600" color={theme.colors.black[100]}>
            {desc.toUpperCase()}
          </Typography>
        </Styled.ItemWrapper>
      ) : null}

      {isSwitch ? (
        <Styled.ItemWrapper>
          <TouchableOpacity
            onPress={() => {
              setColumns(2);
            }}>
            <Icon
              iconSVG={assets.icons.list_switch_row_svg}
              width={26}
              height={26}
              isFill={true}
              fill={numColumns >= 2 ? theme.colors.purple[100] : theme.colors.gray[900]}
            />
          </TouchableOpacity>
          {!singleSwitch ? (
            <TouchableOpacity
              onPress={() => {
                setColumns(1);
              }}>
              <Icon
                iconSVG={assets.icons.list_switch_col_svg}
                width={26}
                height={26}
                isFill={true}
                fill={numColumns === 1 ? theme.colors.purple[100] : theme.colors.gray[900]}
              />
            </TouchableOpacity>
          ) : null}
        </Styled.ItemWrapper>
      ) : (
        <Styled.ItemWrapper />
      )}

      {isFilter && isConnected ? (
        <Styled.ItemWrapper onPress={onFilterPress}>
          {filterLength > 0 ? (
            <Styled.Label>
              <Typography fontSize={12} fontWeight="600" color={theme.colors.white[100]}>
                {filterLength.toString()}
              </Typography>
            </Styled.Label>
          ) : null}

          <Typography fontSize={16} fontWeight="400" color={theme.colors.black[300]}>
            Filtruj
          </Typography>
          <Icon
            iconSVG={assets.icons.filters_svg}
            width={24}
            height={24}
            fill={'#000'}
            isStroke={false}
          />
        </Styled.ItemWrapper>
      ) : (
        <Styled.ItemWrapper />
      )}
    </Styled.Wrapper>
  );
};
