import styled from '@emotion/native';

import theme from '~/theme';

export const Wrapper = styled.View`
  height: 24px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
`;

export const ItemWrapper = styled.TouchableOpacity`
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  position: relative;
`;

export const Label = styled.View`
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  position: absolute;
  background-color: ${theme.colors.purple[100]};
  padding-left: 1px;
  top: -12px;
  right: -8px;
  border-radius: 50px;
  width: 18px;
  height: 18px;
`;
