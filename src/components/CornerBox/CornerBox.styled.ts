import styled from '@emotion/native';

import { ICornerBox } from '~/components/CornerBox/types';
import theme from '~/theme';

export const Wrapper = styled.View<Pick<ICornerBox, 'backgroundColor'>>`
  overflow: hidden;
  flex-direction: row;
  justify-content: space-between;
  background-color: ${({ backgroundColor }) =>
    backgroundColor ? `${backgroundColor}` : `${theme.colors.purple[200]}`};
  position: relative;
  border-radius: 4px;
  padding: 0 4px;
`;

export const Corner = styled.View<Pick<ICornerBox, 'cornerSize' | 'cornerColor'>>`
  position: absolute;
  z-index: 0;
  top: ${({ cornerSize }) => (cornerSize ? `${-(cornerSize / 2)}px` : `-25px`)};
  left: ${({ cornerSize }) => (cornerSize ? `${-(cornerSize / 2)}px` : `-25px`)};
  width: ${({ cornerSize }) => (cornerSize ? `${cornerSize}px` : `50px`)};
  height: ${({ cornerSize }) => (cornerSize ? `${cornerSize}px` : `50px`)};
  background-color: ${({ cornerColor }) =>
    cornerColor ? `${cornerColor}` : `${theme.colors.white[100]}`};
  transform: rotate(45deg);
`;
