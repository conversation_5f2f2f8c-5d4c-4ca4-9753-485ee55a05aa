import React, { FC, ReactElement } from 'react';
import { Image } from 'react-native';

import * as Styled from './ItemImage.styled';

import { IItemImageProps } from '~/components/Item/types';
import { Typography } from '~/components/Typography/Typography';

export const ItemImage: FC<IItemImageProps> = ({ img, imgLabel }): ReactElement => {
  return (
    <Styled.Container>
      <Image source={img} style={{ width: 64, height: 64 }} />
      <Styled.LabelWrapper>
        <Typography fontSize={14} fontWeight="400" style={{ maxWidth: 80 }}>
          {imgLabel.toUpperCase()}
        </Typography>
      </Styled.LabelWrapper>
    </Styled.Container>
  );
};
