import React, { FC, ReactElement } from 'react';

import * as Styled from './ItemContent.styled';

import { IItemContentProps } from '~/components/Item/types';
import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';

export const ItemContent: FC<IItemContentProps> = ({ text, durationText, price }): ReactElement => {
  return (
    <Styled.Container>
      <Typography fontSize={15} fontWeight="500">
        {text}
      </Typography>
      <Typography fontSize={12} color={theme.colors.gray['200']}>
        {durationText}
      </Typography>
      {price ? (
        <Typography fontSize={18} fontWeight="600" color={theme.colors.black['100']}>
          {price}
        </Typography>
      ) : null}
    </Styled.Container>
  );
};
