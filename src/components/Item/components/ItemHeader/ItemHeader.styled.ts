import styled from '@emotion/native';

import theme from '~/theme';

export const Container = styled.View`
  position: relative;
`;
export const Image = styled.Image``;

export const ImageWrapper = styled.View`
  overflow: hidden;
`;

export const LabelWrapper = styled.View`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100px;
  background-color: ${theme.colors.gray[100]};
`;
