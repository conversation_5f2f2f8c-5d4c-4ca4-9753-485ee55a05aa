import React, { FC, ReactElement } from 'react';

import * as Styled from './ItemHeader.styled';

import { ItemIcon } from '~/components/Item/components/ItemIcon/ItemIcon';
import { ItemImage } from '~/components/Item/components/ItemImage/ItemImage';
import { IItemHeaderProps } from '~/components/Item/types';
import { Typography } from '~/components/Typography/Typography';
import { assets } from '~/theme/assets';

export const ItemHeader: FC<IItemHeaderProps> = ({
  imgLabelText,
  bgImg,
  bgLabel,
  icon,
  imgLabel,
  aspectRatio = 1.8,
}): ReactElement => {
  return (
    <Styled.Container>
      {bgImg ? (
        <Styled.ImageWrapper>
          <Styled.Image
            source={bgImg}
            resizeMode={'contain'}
            style={{ height: undefined, width: '100%', aspectRatio: aspectRatio }}
          />
        </Styled.ImageWrapper>
      ) : (
        <Styled.LabelWrapper>
          {bgLabel ? (
            <Typography fontSize={42} fontWeight="700" color="black">
              {bgLabel?.toUpperCase()}
            </Typography>
          ) : null}
        </Styled.LabelWrapper>
      )}
      {icon ? <ItemIcon icon={assets.icons.heart_svg} /> : null}
      {imgLabelText ? <ItemImage img={imgLabel} imgLabel={imgLabelText} /> : null}
    </Styled.Container>
  );
};
