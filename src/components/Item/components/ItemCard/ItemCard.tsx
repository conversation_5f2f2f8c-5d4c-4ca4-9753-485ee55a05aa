import React, { FC, ReactElement } from 'react';

import * as Styled from './ItemCard.styled';

import { IItemCardProps } from '~/components/Item/types';

export const ItemCard: FC<IItemCardProps> = ({ onPress, children, icon, width }): ReactElement => {
  return (
    <Styled.Container
      onPress={onPress}
      open={icon}
      width={width}
      style={{
        shadowColor: 'rgba(0, 0, 0, 0.2)',
        shadowOffset: {
          width: 0,
          height: 0,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3.6,
        elevation: 3.6,
      }}>
      {children}
    </Styled.Container>
  );
};
