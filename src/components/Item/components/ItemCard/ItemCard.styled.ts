import styled from '@emotion/native';

import theme from '~/theme';

interface IContainer {
  open: boolean;
  width?: number;
}

export const Container = styled.TouchableOpacity<IContainer>`
  width: ${({ width }) => (width ? `${width}px` : '100%')};
  background-color: ${theme.colors.white['100']};
  display: flex;
  flex-direction: column;
  border: 1px solid ${theme.colors.gray['300']};
  gap: ${({ open }) => (open ? '36px' : '0px')};
`;
