import React from 'react';

export interface IItemContentProps {
  text: string;
  durationText: string;
  price?: string;
}

export interface IItemHeaderProps {
  bgImg?: any;
  bgLabel?: string;
  icon?: boolean;
  imgLabelText?: string;
  imgLabel?: any;
  aspectRatio?: number;
}

interface IItemCardProps {
  children: React.ReactNode;
  icon: boolean;
  onPress?: () => void;
  width?: number;
}

interface IItemIconProps {
  icon: any;
}

export interface IItemProps {
  id: number;
  itemContentProps?: IItemContentProps;
  itemHeaderProps: IItemHeaderProps;
  onPress?: () => void;
  width?: number;
}

interface IItemImageProps {
  img: any;
  imgLabel: string;
}
