import React, { FC, ReactElement } from 'react';

import { ItemCard } from '~/components/Item/components/ItemCard/ItemCard';
import { ItemContent } from '~/components/Item/components/ItemContent/ItemContent';
import { ItemHeader } from '~/components/Item/components/ItemHeader/ItemHeader';
import { IItemProps } from '~/components/Item/types';

export const Item: FC<IItemProps> = ({
  itemContentProps,
  itemHeaderProps,
  width,
  onPress,
}): ReactElement => {
  return (
    <ItemCard
      onPress={onPress}
      icon={!!(itemHeaderProps.imgLabel && itemHeaderProps.icon)}
      width={width}>
      <ItemHeader {...itemHeaderProps} />
      {itemContentProps ? <ItemContent {...itemContentProps} /> : null}
    </ItemCard>
  );
};
