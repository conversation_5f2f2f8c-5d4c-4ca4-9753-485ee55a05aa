import { View } from 'react-native';

import { CardLayout } from '../Cards/components/CardLayout';
import { Icon } from '../Icon/Icon';
import { Typography } from '../Typography/Typography';

import * as Styled from './More.styled';

import theme from '~/theme';
import { assets } from '~/theme/assets';

interface Props {
  onPress: () => void;
}

export const MoreDiscount = ({ onPress }: Props) => {
  return (
    <CardLayout isShadow={true} onPress={onPress}>
      <Styled.DiscountWrapper>
        <View style={{ flex: 2, justifyContent: 'flex-end' }}>
          <Styled.IconWrapper style={{ marginBottom: 4 }}>
            <Icon iconSVG={assets.icons.more_tile_svg} width={80} height={80} />
          </Styled.IconWrapper>
        </View>
        <View style={{ flex: 1 }}>
          <Typography
            fontSize={15}
            fontWeight="600"
            color={theme.colors.black[300]}
            style={{ textAlign: 'center', marginTop: 8 }}>
            {'Zobacz\nwięcej'}
          </Typography>
        </View>
      </Styled.DiscountWrapper>
    </CardLayout>
  );
};
