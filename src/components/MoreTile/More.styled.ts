import styled from '@emotion/native';

import { flexColCenter } from '~/styles/flex.styled';
import { shadow } from '~/theme/shadows';

export const IconWrapper = styled.View`
  border-radius: 100px;
  ${shadow.gray[100]};
`;

export const BrandWrapper = styled.TouchableOpacity`
  width: 100px;
  height: 100px;
  border-radius: 100px;
  margin: 0 5px 10px 5px;
  ${flexColCenter};
  ${shadow.gray[100]};
`;

export const DiscountWrapper = styled.View`
  height: 232px;
  width: 170px;
  align-items: center;
  justify-content: center;
`;

export const CouponWrapper = styled.View`
  height: 250px;
  width: 170px;
  align-items: center;
  justify-content: center;
`;

export const NewspaperWrapper = styled.View`
  height: 350px;
  width: 170px;
  align-items: center;
  justify-content: center;
`;

export const ArticleWrapper = styled.View`
  height: 250px;
  width: 170px;
  align-items: center;
  justify-content: center;
`;
