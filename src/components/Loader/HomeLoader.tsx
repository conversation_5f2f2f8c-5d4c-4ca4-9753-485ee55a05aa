import { ActivityIndicator, StyleSheet, View } from 'react-native';

import theme from '~/theme';

export const HomeLoader = () => {
  return (
    <View style={[styles.container, styles.horizontal]}>
      <ActivityIndicator size="large" color={theme.colors.purple[100]} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    backgroundColor: theme.colors.white[100],
    position: 'absolute',
    zIndex: 999,
  },
  horizontal: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 10,
  },
});
