import { ReactNode } from 'react';
import { Animated } from 'react-native';

import { useLofiAnimation } from '~/hooks/useLofiAnimation';

type Props = {
  children?: ReactNode;
  width?: string | number | Animated.Value | Animated.AnimatedInterpolation<string | number>;
  height?: string | number | Animated.Value | Animated.AnimatedInterpolation<string | number>;
  rx?: number;
  border?: boolean;
};

const Details = ({ children }: Props) => {
  return (
    <Animated.View
      style={{
        //backgroundColor: 'red',
        gap: 16,
        paddingVertical: 32,
        flex: 1,
        width: '100%',
        height: '100%',
        position: 'relative',
        padding: 8,
        opacity: 0.4,
      }}>
      {children}
    </Animated.View>
  );
};

const Container = ({ children, height = 150, rx = 8 }: Props) => {
  const { interpolation } = useLofiAnimation();
  return (
    <Animated.View
      style={[
        {
          width: '100%',
          height: height,
          borderRadius: rx,
          backgroundColor: interpolation,
          opacity: 0.6,
        },
      ]}>
      {children}
    </Animated.View>
  );
};

const Card = ({ width, height, children, rx = 8 }: Props) => {
  return (
    <Animated.View
      style={{
        flex: 1,
        width: width,
        height: height,
        position: 'relative',
        padding: 8,
        borderRadius: rx,
        opacity: 0.4,
      }}>
      {children}
    </Animated.View>
  );
};

const Circle = ({ width = 90, height = 90, rx = 100, border }: Props) => {
  const { interpolation } = useLofiAnimation();
  return (
    <Animated.View
      style={[
        {
          width,
          height,
          borderRadius: rx,
          backgroundColor: interpolation,
          opacity: 0.6,
          borderWidth: border ? 4 : 0,
          borderColor: 'white',
        },
      ]}
    />
  );
};

const Favorite = ({ width = '100%', height = '50%', rx = 8 }: Props) => {
  const { interpolation } = useLofiAnimation();
  return (
    <Animated.View
      style={[{ width, height, borderRadius: rx, backgroundColor: interpolation, opacity: 0.6 }]}
    />
  );
};

const Text = ({ width = '80%', height = 10, rx = 100 }: Props) => {
  const { interpolation } = useLofiAnimation();
  return (
    <Animated.View
      style={{
        width: width,
        height: height,
        borderRadius: rx,
        backgroundColor: interpolation,
      }}
    />
  );
};

export { Card, Circle, Favorite, Text, Details, Container };
