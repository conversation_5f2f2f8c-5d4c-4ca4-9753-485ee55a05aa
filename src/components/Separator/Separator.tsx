import * as Styled from './Separator.styled';

import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';

export const Separator = () => {
  return (
    <Styled.Separator>
      <Styled.SeparatorLine position={'left'} />
      <Typography fontSize={14} fontWeight="600" color={theme.colors.gray[900]}>
        lub
      </Typography>
      <Styled.SeparatorLine position={'right'} />
    </Styled.Separator>
  );
};
