import styled from '@emotion/native';

import { flexColCenter } from '~/styles/flex.styled';
import theme from '~/theme';

export interface ISeparatorLine {
  position?: 'left' | 'right';
}

export const Separator = styled.View`
  position: relative;
  height: 24px;
  ${flexColCenter};
`;

export const SeparatorLine = styled.View<ISeparatorLine>`
  position: absolute;
  right: ${({ position }) => (position === 'right' ? '32px' : 'undefined')};
  left: ${({ position }) => (position === 'left' ? '32px' : 'undefined')};
  width: 30%;
  height: 1px;
  background-color: ${theme.colors.gray[1000]};
`;
