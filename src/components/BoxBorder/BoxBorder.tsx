import { FC } from 'react';

import * as Styled from './BoxBorder.styled';

import { IBoxBorderProps } from '~/components/BoxBorder/types';

export const BoxBorder: FC<IBoxBorderProps> = ({ borderBottom, borderTop, children }) => {
  return (
    <Styled.Container
      testID={'box-border-container'}
      borderBottom={borderBottom}
      borderTop={borderTop}>
      {children}
    </Styled.Container>
  );
};
