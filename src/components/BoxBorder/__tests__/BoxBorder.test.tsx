import { render } from '@testing-library/react-native';
import React from 'react';
import { Text } from 'react-native';

import { BoxBorder } from '~/components/BoxBorder/BoxBorder';

describe('BoxBorder component', () => {
  it('renders correctly', () => {
    const { getByText } = render(
      <BoxBorder>
        <Text>Test content</Text>
      </BoxBorder>,
    );
    expect(getByText('Test content')).toBeTruthy();
  });
});
