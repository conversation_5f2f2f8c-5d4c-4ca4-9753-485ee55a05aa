import styled from '@emotion/native';

import { IBoxBorderProps } from '~/components/BoxBorder/types';
import theme from '~/theme';

export const Container = styled.View<IBoxBorderProps>`
  width: 100%;
  border-top-width: ${({ borderTop }) => (borderTop ? '1px' : '0px')};
  border-top-color: ${theme.colors.gray['1000']};
  border-bottom-width: ${({ borderBottom }) => (borderBottom ? '1px' : '0px')};
  border-bottom-color: ${theme.colors.gray['1000']};
`;
