import { FC } from 'react';

import * as Styled from './Filters.styled';

import { FilterCarousel } from '~/components/Filters/components/FilterCarousel/FilterCarousel';
import { IFilterProps } from '~/components/Filters/types';
import { Icon } from '~/components/Icon/Icon';
import { assets } from '~/theme/assets';

export const Filters: FC<IFilterProps> = ({ data, onRemoveAllFilters, onRemoveSingleFilter }) => {
  return (
    <Styled.Wrapper>
      <Styled.Touch onPress={onRemoveAllFilters}>
        <Icon iconSVG={assets.icons.cross_svg} isFill={false} width={28} height={28} />
      </Styled.Touch>
      <FilterCarousel onPress={onRemoveSingleFilter} data={data} />
    </Styled.Wrapper>
  );
};
