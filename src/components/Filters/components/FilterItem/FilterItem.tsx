import { FC } from 'react';
import { View } from 'react-native';

import * as Styled from './FilterItem.styled';

import { IFilterItemProps } from '~/components/Filters/components/FilterItem/types';
import { Icon } from '~/components/Icon/Icon';
import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';
import { assets } from '~/theme/assets';

export const FilterItem: FC<IFilterItemProps> = ({ title, onPress }) => {
  return (
    <Styled.Wrapper onPress={onPress}>
      <Typography fontSize={14} fontWeight="400" color={theme.colors.purple[100]}>
        {title.toUpperCase()}
      </Typography>
      <View>
        <Icon
          iconSVG={assets.icons.cross_filter_svg}
          isFill={false}
          isStroke={false}
          width={10}
          height={10}
        />
      </View>
    </Styled.Wrapper>
  );
};
