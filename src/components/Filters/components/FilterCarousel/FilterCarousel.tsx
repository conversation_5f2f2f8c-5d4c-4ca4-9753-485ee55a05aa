import { FC } from 'react';
import { View } from 'react-native';

import * as Styled from './FilterCarousel.styled';

import { IFilterCarouselProps } from '~/components/Filters/components/FilterCarousel/types';
import { FilterItem } from '~/components/Filters/components/FilterItem/FilterItem';
import { Slider } from '~/components/Slider/Slider';

export const FilterCarousel: FC<IFilterCarouselProps> = ({ data, onPress }) => {
  return (
    <Styled.Wrapper>
      <Slider
        itemWidth={1}
        ItemSeparatorComponent={() => <View style={{ width: 5 }} />}
        data={data}
        renderItem={({ item }) => {
          if (item) {
            return (
              <FilterItem
                onPress={() => onPress(item.id, item.categoryType)}
                title={item.name}
                key={item.id}
              />
            );
          } else {
            return null;
          }
        }}
      />
    </Styled.Wrapper>
  );
};
