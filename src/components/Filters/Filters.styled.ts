import styled from '@emotion/native';

import { flexColCenter } from '~/styles/flex.styled';
import theme from '~/theme';

export const Wrapper = styled.View`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 36px;
  gap: 8px;
`;

export const Touch = styled.TouchableOpacity`
  background-color: ${theme.colors.purple[100]};
  width: 26px;
  height: 26px;
  border-radius: 16px;
  ${flexColCenter};
`;
