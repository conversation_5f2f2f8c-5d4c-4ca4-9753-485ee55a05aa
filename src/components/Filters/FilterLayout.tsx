import React, { FC } from 'react';

import { Filters } from '~/components/Filters/Filters';
import { ListOptions } from '~/components/ListOptions/ListOptions';
import { ObjectTypeEnum } from '~/enums/objectsTypesEnum';
import { CategoryTypeEnum } from '~/enums/shared';
import { useNavigate } from '~/hooks/useNavigate';
import { RouteEnum } from '~/routes/routes';

export const FilterLayout: FC<{
  removeSingleFilter: (id: number, type: CategoryTypeEnum) => void;
  removeAllFilters: () => void;
  setColumnsSwitch: (cols: number) => void;
  columnsSwitch: number;
  screenType: ObjectTypeEnum;
  desc?: string;
  isFilter?: boolean;
  isSwitch?: boolean;
  tiles: ({ id: number; name: string; categoryType: CategoryTypeEnum } | null)[];
  singleSwitch?: boolean;
}> = ({
  removeSingleFilter,
  removeAllFilters,
  tiles,
  setColumnsSwitch,
  columnsSwitch,
  screenType,
  desc,
  isSwitch = true,
  isFilter = true,
  singleSwitch = false,
}) => {
  const { navigation } = useNavigate();

  return (
    <>
      {tiles.length > 0 ? (
        <Filters
          onRemoveSingleFilter={(id, type) => removeSingleFilter(id, type)}
          onRemoveAllFilters={() => removeAllFilters()}
          data={tiles}
        />
      ) : null}

      <ListOptions
        desc={desc}
        onFilterPress={() => {
          navigation.navigate(RouteEnum.FILTER_MODAL_SCREEN, {
            type: screenType,
          });
        }}
        filterLength={tiles ? tiles.length : 0}
        setColumns={setColumnsSwitch}
        numColumns={columnsSwitch}
        isFilter={isFilter}
        isSwitch={isSwitch}
        singleSwitch={singleSwitch}
      />
    </>
  );
};
