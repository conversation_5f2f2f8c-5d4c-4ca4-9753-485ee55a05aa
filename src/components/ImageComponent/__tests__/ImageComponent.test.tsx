import { fireEvent, render } from '@testing-library/react-native';
import React from 'react';

import { ImageComponent } from '~/components/ImageComponent/ImageComponent';
import { assets } from '~/theme/assets';

describe('<ImageComponent />', () => {
  it('renders correctly when uri is provided', () => {
    const uri =
      'https://images.pexels.com/photos/863963/pexels-photo-863963.jpeg?cs=srgb&dl=pexels-blaque-x-863963.jpg&fm=jpg';
    const { getByTestId } = render(<ImageComponent uri={uri} />);

    const image = getByTestId('dynamic-image');
    expect(image.props.source.uri).toBe(uri);
  });

  it('renders default image when uri is null or empty', () => {
    const { getByTestId } = render(<ImageComponent uri={null} />);

    const image = getByTestId('default-image');
    expect(image.props.source).toBe(assets.images.gallery_png);
  });

  it('renders a background color when bgColor is provided and uri is null', () => {
    const bgColor = 'red';
    const { getByTestId } = render(<ImageComponent bgColor={bgColor} uri={null} />);

    const view = getByTestId('bg-color-view');
    expect(view.props.style.backgroundColor).toBe(bgColor);
  });

  it('fires onPress event', () => {
    const onPress = jest.fn();
    const { getByTestId } = render(<ImageComponent onPress={onPress} />);

    const touchable = getByTestId('touchable-opacity');
    fireEvent.press(touchable);

    expect(onPress).toHaveBeenCalledTimes(1);
  });
});
