import React, { FC, useState } from 'react';
import { Image, ImageProps, StyleProp, TouchableOpacity, View, ViewStyle } from 'react-native';
import FastImage from 'react-native-fast-image';
import { SvgUri } from 'react-native-svg';

import { assets } from '~/theme/assets';

interface IExtendedImageProps extends Partial<ImageProps> {
  uri?: string | null;
  width?: string | number;
  height?: string | number;
  borderRadius?: number;
  onPress?: () => void;
  bgColor?: string | null;
  resizeMode?: 'contain' | 'cover' | 'stretch' | 'center';
  noCache?: boolean;
}

export const ImageComponent: FC<IExtendedImageProps> = ({
  uri,
  width = '100%',
  height = '100%',
  borderRadius = 0,
  onPress,
  bgColor,
  resizeMode = 'contain',
  noCache = false,
}) => {
  const [isLoaded, setIsLoaded] = useState(false);

  const isSvg = uri ? uri.includes('.svg') : false;

  const sharedStyle: StyleProp<ViewStyle> = {
    width,
    height,
    borderRadius,
    overflow: 'hidden',
  };

  const renderImage = () => {
    if (uri && uri !== '') {
      return isSvg ? (
        <SvgUri width="100%" height="100%" uri={uri} testID="svg-uri" />
      ) : (
        <>
          {!isLoaded && <View testID="default-image" style={{ width: '100%', height: '100%' }} />}
          {noCache ? (
            <Image
              resizeMode={resizeMode}
              style={{ width: '100%', height: '100%' }}
              source={{ uri }}
              onLoadEnd={() => setIsLoaded(true)}
            />
          ) : (
            <FastImage
              testID="dynamic-image"
              style={{ width: '100%', height: '100%' }}
              source={{
                uri: uri,
                priority: FastImage.priority.normal,
              }}
              onLoadEnd={() => setIsLoaded(true)}
              resizeMode={resizeMode}
            />
          )}
        </>
      );
    }
    if (bgColor) {
      return <View testID="bg-color-view" style={{ ...sharedStyle, backgroundColor: bgColor }} />;
    }

    return (
      <FastImage
        testID="default-image"
        style={{ width: '100%', height: '100%' }}
        source={assets.images.gallery_png}
        resizeMode={resizeMode}
      />
    );
  };

  return (
    <View style={sharedStyle} testID="image-component-wrapper">
      {onPress ? (
        <TouchableOpacity testID="touchable-opacity" onPress={onPress}>
          {renderImage()}
        </TouchableOpacity>
      ) : (
        renderImage()
      )}
    </View>
  );
};
