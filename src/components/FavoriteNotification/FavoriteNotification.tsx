import React, { <PERSON> } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import { Typography } from '../Typography/Typography';

export const FavoriteNotification: FC<{ message: string; onUndo: () => void }> = ({
  message,
  onUndo,
}) => (
  <View style={styles.notification}>
    <Typography fontSize={12}>{message}</Typography>
    <TouchableOpacity onPress={onUndo}>
      <Typography fontSize={12} style={styles.undoButton}>
        Cofnij
      </Typography>
    </TouchableOpacity>
  </View>
);
const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  notification: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f2f2f2',
    padding: 16,
    marginVertical: 8,
    width: '100%',
  },
  undoButton: {
    color: 'blue',
  },
});
