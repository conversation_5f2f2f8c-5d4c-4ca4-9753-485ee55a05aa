import { useEffect, useState } from 'react';

import * as Styled from './Checkbox.styled';

import { ICheckbox } from '~/components/Checkbox/types';
import { Icon } from '~/components/Icon/Icon';
import { assets } from '~/theme/assets';

export const Checkbox = ({ children, defaultChecked = false, onStateChange }: ICheckbox) => {
  const [checked, setChecked] = useState<boolean>(defaultChecked);

  useEffect(() => {
    onStateChange(checked);
  }, [checked]);

  return (
    <Styled.Wrapper>
      <Icon
        iconSVG={checked ? assets.icons.pick_checkbox_svg : assets.icons.checkbox_svg}
        width={28}
        height={28}
        onPress={() => {
          setChecked(!checked);
        }}
      />
      {children}
    </Styled.Wrapper>
  );
};

export const FilterCheckbox = ({
  children,
  defaultChecked = false,
  isChecked = false,
  onStateChange,
  onPress,
}: ICheckbox) => {
  const [checked, setChecked] = useState<boolean>(defaultChecked);

  useEffect(() => {
    onStateChange(checked);
  }, [checked]);

  const handlePress = () => {
    setChecked(!checked);
    if (onPress) onPress();
  };

  return (
    <Styled.Wrapper>
      <Icon
        iconSVG={isChecked ? assets.icons.pick_checkbox_svg : assets.icons.checkbox_svg}
        width={28}
        height={28}
        onPress={handlePress}
      />
      {children}
    </Styled.Wrapper>
  );
};
