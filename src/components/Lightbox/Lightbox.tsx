import {
  ReactNativeZoomableView,
  ZoomableViewEvent,
} from '@openspacelabs/react-native-zoomable-view';
import { ReactNode, useEffect, useMemo, useRef, useState } from 'react';
import {
  Animated,
  Dimensions,
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
  GestureResponderEvent,
  PanResponderGestureState,
} from 'react-native';
import Swiper from 'react-native-swiper';
import { Icon } from '../Icon/Icon';
import theme from '~/theme';
import { assets } from '~/theme/assets';

function Background({ children, onClose }: { children: ReactNode; onClose: () => void }) {
  return (
    <Animated.View style={styles.container}>
      <TouchableOpacity
        onPress={onClose}
        style={{
          paddingRight: 16,
          paddingTop: 16,
          alignSelf: 'flex-end',
          position: 'absolute',
          zIndex: 9999,
        }}>
        <Icon iconSVG={assets.icons.cross_svg} width={32} height={32} />
      </TouchableOpacity>
      <View style={{ flex: 1 }}>{children}</View>
    </Animated.View>
  );
}

type Props = {
  uri: string[];
  index?: number;
  onClose: () => void;
};

export const Lightbox = ({ uri, onClose, index = 0 }: Props) => {
  const swiperRef = useRef<Swiper>(null);
  const [currentZoom, setCurrentZoom] = useState(1);
  const [activeIndex, setActiveIndex] = useState(index);

  const [dimensions, setDimensions] = useState(Dimensions.get('window'));
  const [orientation, setOrientation] = useState(
    dimensions.width > dimensions.height ? 'landscape' : 'portrait',
  );

  // Handle orientation changes
  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setDimensions(window);
      setOrientation(window.width > window.height ? 'landscape' : 'portrait');
    });

    return () => subscription.remove();
  }, []);

  const handleZoomAfter = (
    _: GestureResponderEvent,
    __: PanResponderGestureState,
    zoomableViewEventObject: ZoomableViewEvent,
  ) => {
    const newZoom = zoomableViewEventObject.zoomLevel ?? 1;
    setCurrentZoom(newZoom);
  };

  const handleIndexChanged = (newIndex: number) => {
    setActiveIndex(newIndex);
  };

  const currentIndex = useMemo(() => activeIndex, [activeIndex]);

  useEffect(() => {
    if (swiperRef.current && uri[currentIndex]) {
      swiperRef.current.scrollTo(currentIndex);
    }
  }, [orientation]);

  const renderZoomableImage = (imageUri: string) => (
    <ReactNativeZoomableView
      maxZoom={3}
      minZoom={1}
      panEnabled={currentZoom > 1}
      bindToBorders={true}
      disablePanOnInitialZoom={true}
      onZoomAfter={handleZoomAfter}>
      <Image
        style={{ width: '100%', height: '100%' }}
        source={{ uri: imageUri }}
        resizeMode="contain"
      />
    </ReactNativeZoomableView>
  );

  const renderSingleImage = () => <View style={styles.slide1}>{renderZoomableImage(uri[0])}</View>;

  const renderSwiper = () => (
    <Swiper
      key={`${orientation}-${dimensions.width}x${dimensions.height}`}
      ref={swiperRef}
      index={currentIndex}
      onIndexChanged={handleIndexChanged}
      nextButton={
        <Icon
          iconSVG={assets.icons.arrow_right_svg}
          height={40}
          width={40}
          isFill={false}
          isStroke={true}
          fill={theme.colors.purple[100]}
        />
      }
      prevButton={
        <Icon
          iconSVG={assets.icons.arrow_left_svg}
          height={40}
          width={40}
          isFill={false}
          isStroke={true}
          fill={theme.colors.purple[100]}
        />
      }
      showsButtons={true}
      showsPagination={false}
      loop={false}
      scrollEnabled={true}>
      {uri.map((item, idx) => (
        <View key={idx} style={styles.slide1}>
          {renderZoomableImage(item)}
        </View>
      ))}
    </Swiper>
  );

  return (
    <Background onClose={onClose}>
      {uri.length === 1 ? renderSingleImage() : renderSwiper()}
    </Background>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'rgba(52, 52, 52, 0.95)',
    height: '100%',
    width: '100%',
    zIndex: 999,
    position: 'absolute',
  },
  slide1: {
    height: '100%',
  },
});
