import { Switch } from 'react-native';

import * as Styled from './RadioSwitch.styled';

import { IRadioSwitch } from '~/components/RadioSwitch/types';
import theme from '~/theme';

export const RadioSwitch = ({ checked, onChange }: IRadioSwitch) => {
  return (
    <Styled.Wrapper>
      <Switch
        trackColor={{ false: theme.colors.gray[300], true: theme.colors.purple[100] }}
        thumbColor={theme.colors.white[100]}
        ios_backgroundColor={theme.colors.gray[300]}
        onValueChange={onChange}
        style={{ transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }], padding: -8 }}
        value={checked}
      />
    </Styled.Wrapper>
  );
};
