import { ReactElement } from 'react';

import { ITypography } from '~/components/Typography/Typography';

export interface IHeaderProps {
  label?: string;
  labelStyle?: Omit<ITypography, 'children'>;
  leftIcon?: boolean | ReactElement;
  rightItem?: ReactElement;
  homeScreen?: boolean;
  onLeftIconPress?: () => void;
  onRightIconPress?: () => void;
  resultCounter?: number;
  isIOS?: boolean;
}

interface IContainer {
  ifLeftIcon?: boolean;
  homeScreen?: boolean;
  isScrolling?: boolean;
}
