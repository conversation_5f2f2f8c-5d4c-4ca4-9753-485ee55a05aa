import { ReactElement } from 'react';
import { Animated, Platform, StyleSheet, View } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { Icon } from '../Icon/Icon';

import theme from '~/theme';
import { assets } from '~/theme/assets';
import { shadow } from '~/theme/shadows';

// Tworzymy animowany komponent SafeAreaView
const AnimatedSafeArea = Animated.createAnimatedComponent(View);

type Props = {
  translateY: Animated.AnimatedInterpolation<string | number>;
  rightItem?: ReactElement;
};

const isNotch = DeviceInfo.hasNotch();

export const AnimatedHeader = ({ translateY, rightItem }: Props) => {
  // Pobieramy wartości bezpiecznych obszarów
  const insets = useSafeAreaInsets();

  return (
    <AnimatedSafeArea
      style={[
        styles.safeAreaContainer,
        {
          transform: [{ translateY: translateY }],
          paddingTop: insets.top, // Dynamicznie ustawiamy padding na podstawie rzeczywistej wartości
        },
        Platform.select({
          ios: {
            ...shadow.gray['400'],
          },
          android: {
            ...shadow.gray['100'],
          },
        }),
      ]}>
      <View style={styles.animatedContainer}>
        <Icon iconSVG={assets.icons.home_profit_nav_svg} height={38} width={184} />
        {rightItem ? rightItem : null}
      </View>
    </AnimatedSafeArea>
  );
};

const styles = StyleSheet.create({
  safeAreaContainer: {
    position: 'absolute',
    backgroundColor: theme.colors.white[100],
    top: 0,
    right: 0,
    left: 0,
    elevation: 4,
    zIndex: 1,
  },
  animatedContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 16,
    borderColor: theme.colors.gray[300],
    backgroundColor: theme.colors.white[100],
  },
});
