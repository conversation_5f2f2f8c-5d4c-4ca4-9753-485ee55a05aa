import styled from '@emotion/native';

import { IContainer } from '~/components/Header/types';
import theme from '~/theme';

export const Container = styled.View<IContainer>`
  ${({ homeScreen }) =>
    homeScreen &&
    `
      padding-bottom: 20px;
  `}
  justify-content: ${({ ifLeftIcon }) => (ifLeftIcon ? 'center' : 'flex-start')};
  flex-direction: row;
  align-items: ${({ homeScreen }) => (homeScreen ? 'flex-end' : 'center')};
  padding-left: 16px;
  padding-right: 16px;
  position: relative;
  flex: 2;
  border-color: ${theme.colors.gray[300]};
  border-bottom-width: ${({ homeScreen }) => (homeScreen ? 0 : '0')};
`;
export const RightIconWrapper = styled.TouchableOpacity<{ homeScreen?: boolean }>`
  position: absolute;
  top: 0;
  right: 16px;
  display: flex;
  flex-direction: row;
  align-items: ${({ homeScreen }) => (homeScreen ? 'flex-end' : 'center')};
  gap: 13px;
  height: 100%;
`;
export const LeftIconWrapper = styled.TouchableOpacity`
  display: flex;
  flex-direction: row;
  align-items: center;
  position: absolute;
  top: 0;
  left: 16px;
  height: 100%;
`;

export const LabelWrapper = styled.View<{ homeScreen?: boolean }>`
  flex-direction: row;
  align-items: ${({ homeScreen }) => (homeScreen ? 'flex-end' : 'center')};
  gap: 6px;
`;
