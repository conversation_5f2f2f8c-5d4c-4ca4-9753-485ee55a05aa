import { FC, ReactElement } from 'react';
import { View } from 'react-native';

import * as Styled from './Header.styled';

import { IHeaderProps } from '~/components/Header/types';
import { Icon } from '~/components/Icon/Icon';
import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';
import { assets } from '~/theme/assets';
import { shadow } from '~/theme/shadows';

export const Header: FC<IHeaderProps> = ({
  label,
  leftIcon,
  onLeftIconPress,
  rightItem,
  labelStyle,
  onRightIconPress,
  homeScreen,
  resultCounter,
  isIOS,
}): ReactElement => {
  return (
    <Styled.Container
      style={[homeScreen ? (isIOS ? shadow.gray['400'] : shadow.gray['100']) : null]}
      ifLeftIcon={typeof leftIcon === 'boolean'}
      homeScreen={homeScreen}>
      {leftIcon !== undefined ? (
        <>
          {typeof leftIcon === 'boolean' ? (
            <Styled.LeftIconWrapper onPress={onLeftIconPress}>
              <Icon
                iconSVG={assets.icons.arrow_left_svg}
                width={24}
                height={24}
                fill={theme.colors.gray[200]}
              />
            </Styled.LeftIconWrapper>
          ) : (
            <>{leftIcon}</>
          )}
        </>
      ) : null}
      {label && (
        <Styled.LabelWrapper homeScreen={homeScreen}>
          {homeScreen ? (
            <View style={{ flexDirection: 'row', alignItems: 'flex-end', gap: 5 }}>
              <Icon iconSVG={assets.icons.home_profit_nav_svg} height={38} width={184} />
            </View>
          ) : (
            <>
              <Typography fontSize={14} fontWeight="600" {...labelStyle}>
                {label.toUpperCase()}
              </Typography>
              {resultCounter ? (
                <Typography fontSize={14} fontWeight="400" color={theme.colors.gray['800']}>
                  {`[${resultCounter.toString()}]`}
                </Typography>
              ) : null}
            </>
          )}
        </Styled.LabelWrapper>
      )}

      {rightItem && (
        <Styled.RightIconWrapper homeScreen={homeScreen} onPress={onRightIconPress}>
          {rightItem}
        </Styled.RightIconWrapper>
      )}
    </Styled.Container>
  );
};
