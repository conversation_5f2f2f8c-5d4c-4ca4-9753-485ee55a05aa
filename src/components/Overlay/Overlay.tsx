import { FC, ReactElement } from 'react';
import { Modal, StyleSheet, TouchableWithoutFeedback, View } from 'react-native';

interface IOverlayProps {
  children: ReactElement;
  isVisible: boolean;
  onClose: () => void;
  animationType?: 'fade' | 'none' | 'slide';
  onBackdropPress?: () => void;
}

export const Overlay: FC<IOverlayProps> = ({
  children,
  animationType,
  onBackdropPress,
  onClose,
  isVisible,
}): ReactElement => {
  return (
    <Modal
      transparent
      visible={isVisible}
      animationType={animationType}
      onRequestClose={() => onClose()}>
      <TouchableWithoutFeedback onPress={onBackdropPress}>
        <View style={styles.container}>{children}</View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
