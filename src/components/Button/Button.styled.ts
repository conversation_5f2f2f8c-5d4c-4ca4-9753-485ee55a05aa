import styled from '@emotion/native';

import { ButtonSize } from '~/components/Button/enum';
import { IButtonStyledProps } from '~/components/Button/types.d';
import { flexRowCenter } from '~/styles/flex.styled';
import { buttonSizesStyles, buttonVariantsStyles } from '~/theme/themes';

export const Container = styled.TouchableOpacity<IButtonStyledProps>`
  ${flexRowCenter};
  opacity: ${({ opacity }) => opacity};
  position: relative;
  gap: 10px;
  ${({ alignSelf }) => (alignSelf ? `align-self: ${alignSelf}` : 'width: 100%')};
  max-width: 500px;
  min-height: ${({ size }) =>
    size ? buttonSizesStyles[size].height : buttonSizesStyles[ButtonSize.MEDIUM].height};
  padding: ${({ size }) =>
    size ? buttonSizesStyles[size].padding : buttonSizesStyles[ButtonSize.MEDIUM].padding};
  background-color: ${({ variant, backgroundColor }) =>
    backgroundColor ? backgroundColor : buttonVariantsStyles[variant].backgroundColor};
  border-radius: ${({ variant, borderRadius }) =>
    borderRadius ? borderRadius.toString() + `px` : buttonVariantsStyles[variant].borderRadius};
  border: ${({ variant, border }) => (border ? border : buttonVariantsStyles[variant].border)};
`;

export const IconWrapper = styled.View`
  position: absolute;
  right: 15px;
`;
