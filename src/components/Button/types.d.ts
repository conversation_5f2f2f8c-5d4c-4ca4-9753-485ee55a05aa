import { StyleProp, ViewStyle } from 'react-native';

import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { ITypography } from '~/components/Typography/Typography';

export interface IButtonStyles {
  backgroundColor?: string;
  height?: string;
  borderRadius?: string;
  border?: string;
}

// MAIN COMPONENT INTERFACE
export interface IButtonBase {
  disabled?: boolean;
  children: string;
  variant: ButtonVariant;
  size?: ButtonSize;
  alignSelf?: string;
  borderRadius?: number;
  border?: string;
  backgroundColor?: string;
  typographyStyles?: Partial<Pick<ITypography, 'fontWeight' | 'fontSize' | 'color'>>;
  onPress?: () => void;
  style?: StyleProp<ViewStyle>;
}

export interface IButtonWithIcon extends IButtonBase {
  icon: any;
  iconPosition?: 'left' | 'right';
}

export interface IButtonWithoutIcon extends IButtonBase {
  icon?: never;
  iconPosition?: never;
}

export type IButtonProps = IButtonWithIcon | IButtonWithoutIcon;

// MAIN STYLED COMPONENT INTERFACE FOR BUTTON STYLES
export interface IButtonStyledProps {
  opacity: number;
  variant: ButtonVariant;
  alignSelf?: string;
  size?: ButtonSize;
  borderRadius?: number;
  border?: string;
  backgroundColor?: string;
}

// INTERNAL COMPONENTS INTERFACE FOR TYPOGRAPHY STYLES
export interface ITypographyStyles {
  [ButtonVariant.PRIMARY]: Pick<ITypography, 'fontWeight' | 'fontSize' | 'color'>;
  [ButtonVariant.SECONDARY]: Pick<ITypography, 'fontWeight' | 'fontSize' | 'color'>;
  [ButtonVariant.TERTIARY]: Pick<ITypography, 'fontWeight' | 'fontSize' | 'color'>;
  [ButtonVariant.FOURTH]: Pick<ITypography, 'fontWeight' | 'fontSize' | 'color'>;
  [ButtonVariant.FIFTH]: Pick<ITypography, 'fontWeight' | 'fontSize' | 'color'>;
}

// INTERNAL STYLED COMPONENT INTERFACE FOR BUTTON STYLES
export interface IButtonVariantsStyles {
  [ButtonVariant.PRIMARY]: IButtonStyles;
  [ButtonVariant.SECONDARY]: IButtonStyles;
  [ButtonVariant.TERTIARY]: IButtonStyles;
  [ButtonVariant.FOURTH]: IButtonStyles;
  [ButtonVariant.FIFTH]: IButtonStyles;
}
