import { FC } from 'react';

import * as Styled from './Button.styled';

import { ButtonSize, ButtonVariant } from '~/components/Button/enum';
import { IButtonProps } from '~/components/Button/types.d';
import { Icon } from '~/components/Icon/Icon';
import { Typography } from '~/components/Typography/Typography';
import theme from '~/theme';
import { buttonStyle } from '~/theme/themes';

export const Button: FC<IButtonProps> = ({
  variant = ButtonVariant.PRIMARY,
  size = ButtonSize.MEDIUM,
  borderRadius,
  border,
  typographyStyles,
  children,
  icon,
  iconPosition,
  backgroundColor,
  onPress,
  alignSelf,
  disabled = false,
  style,
}) => {
  const { fontSize, fontWeight, color } = typographyStyles || buttonStyle[variant];
  return (
    <Styled.Container
      style={style}
      alignSelf={alignSelf}
      opacity={0.2}
      disabled={disabled}
      onPress={onPress}
      backgroundColor={backgroundColor}
      variant={variant}
      size={size}
      borderRadius={borderRadius}
      border={border}>
      {iconPosition === 'left' && icon ? (
        <Icon iconSVG={icon} width={24} height={24} isStroke={false} isFill={true} />
      ) : null}
      <Typography
        style={{ textAlign: 'center' }}
        fontSize={fontSize ? fontSize : 24}
        fontWeight={fontWeight}
        color={color}>
        {children}
      </Typography>
      {iconPosition === 'right' && icon ? (
        <Styled.IconWrapper>
          <Icon
            isFill={true}
            fill={theme.colors.white['100']}
            iconSVG={icon}
            width={24}
            height={24}
            isStroke={false}
          />
        </Styled.IconWrapper>
      ) : null}
    </Styled.Container>
  );
};
