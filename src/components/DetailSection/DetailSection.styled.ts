import styled from '@emotion/native';

import theme from '~/theme';

export const Container = styled.View`
  width: 100%;
`;

export const Section = styled.View<{ bottomBorder?: boolean }>`
  flex-direction: row;
  justify-content: space-between;
  height: 60px;
  width: 100%;
  border-top-width: 0.5px;
  border-top-color: ${theme.colors.gray['600']};
  border-bottom-width: ${({ bottomBorder }) => (bottomBorder ? '0.5px' : '0px')};
  border-bottom-color: ${theme.colors.gray['600']};
  align-items: center;
`;

export const ButtonWrapper = styled.View`
  width: 115px;
`;
