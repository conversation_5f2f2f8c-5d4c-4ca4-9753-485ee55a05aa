import { FC } from 'react';

import * as Styled from './DetailSection.styled';

import { IDetailSectionProps } from '~/components/DetailSection/types';
import { Typography } from '~/components/Typography/Typography';

export const DetailSection: FC<IDetailSectionProps> = ({
  leftText,
  rightText,
  children,
  bottomBorder,
  fontWeight = '400',
}) => {
  return (
    <Styled.Container>
      <Styled.Section bottomBorder={bottomBorder}>
        <Typography fontSize={12} fontWeight="400">
          {leftText}
        </Typography>
        {rightText ? (
          <Typography fontSize={14} fontWeight={fontWeight}>
            {rightText}
          </Typography>
        ) : (
          children
        )}
      </Styled.Section>
    </Styled.Container>
  );
};
