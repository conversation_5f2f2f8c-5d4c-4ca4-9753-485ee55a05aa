import * as Styled from './Label.styled';

import { Typography } from '~/components/Typography/Typography';
import { NotificationEnum } from '~/enums/notificationEnum';
import theme from '~/theme';
import { INotification } from '~/types/notification';

export const Label = ({ type }: Pick<INotification, 'type'>) => {
  if (type !== 'oth')
    return (
      <Styled.Wrapper>
        <Typography fontSize={12} fontWeight="500" color={theme.colors.purple[100]}>
          {NotificationEnum[type].toUpperCase()}
        </Typography>
      </Styled.Wrapper>
    );
  else return null;
};
