import React, { useEffect, useState } from 'react';

import * as Styled from './Icon.styled';

import { Icon as SVGIcon } from '~/components/Icon/Icon';
import { useNavigate } from '~/hooks/useNavigate';
import { useAppSelector } from '~/redux/store';
import { RouteEnum } from '~/routes/routes';
import { assets } from '~/theme/assets';

export const Icon = () => {
  const { navigation } = useNavigate();
  const [isAllRead, setIsAllRead] = useState<boolean>(false);
  const { notification } = useAppSelector((state) => state.user);
  const { collection } = useAppSelector((state) => state.notifications);

  const today = new Date();
  today.setMonth(today.getMonth() - 2);

  function handleCheckAll() {
    const datySet = new Set(notification);
    for (const obiekt of collection.results) {
      if (!datySet.has(obiekt.created_at)) {
        return false;
      }
    }
    return true;
  }

  useEffect(() => {
    setIsAllRead(handleCheckAll());
  }, []);

  useEffect(() => {
    setIsAllRead(handleCheckAll());
  }, [notification, collection]);

  return (
    <Styled.Wrapper onPress={() => navigation.navigate(RouteEnum.NOTIFICATIONS)} hitSlop={16}>
      {!isAllRead ? <Styled.Badge /> : null}
      <SVGIcon iconSVG={assets.icons.notification_svg} width={24} height={24} />
    </Styled.Wrapper>
  );
};
