import React, { FC, ReactElement, useState } from 'react';

import * as Styled from './CarouselGallery.styled';

import { Gallery } from '~/components/CarouselGallery/components/Gallery/Gallery';
import { Icon } from '~/components/Icon/Icon';
import { Slider } from '~/components/Slider/Slider';
import { getItemWidth } from '~/helpers/getItemWidth';
import { assets } from '~/theme/assets';
import { IBanner } from '~/types/banner';

interface ICarouselGalleryProps {
  data: IBanner[];
  onPress: (id?: number | null) => void;
}

export const CarouselGallery: FC<ICarouselGalleryProps> = ({ data, onPress }): ReactElement => {
  const [currentIndex, setCurrentIndex] = useState<number>(0);
  const ITEM_WIDTH = getItemWidth();
  const MAX_ITEMS = 3;

  return (
    <Styled.Container>
      <Slider
        handleIndex={(_) => setCurrentIndex(_)}
        currentIndex={currentIndex}
        scrollToIndex={currentIndex}
        snapToAlignment={'center'}
        data={data.slice(0, data.length > MAX_ITEMS ? MAX_ITEMS : data.length)}
        itemWidth={ITEM_WIDTH}
        renderItem={({ item, index }) => (
          <Gallery
            key={index}
            img={item.image}
            width={ITEM_WIDTH}
            onPress={() => onPress(item.card)}
          />
        )}
      />
      <Styled.LineContainer>
        {Array.from({ length: data.length > MAX_ITEMS ? MAX_ITEMS : data.length }).map(
          (_, index) => (
            <Icon
              key={index}
              iconSVG={
                index !== currentIndex ? assets.icons.line_svg : assets.icons.line_active_svg
              }
              width={25}
              height={10}
              onPress={() => setCurrentIndex(index)}
            />
          ),
        )}
      </Styled.LineContainer>
    </Styled.Container>
  );
};
