import { FC, ReactElement } from 'react';
import { Image } from 'react-native';

import * as Styled from './Gallery.styled';

import { assets } from '~/theme/assets';

export interface IGalleryProps {
  img: string;
  onPress: () => void;
  width?: number;
}

export const Gallery: FC<IGalleryProps> = ({ img, width, onPress }): ReactElement => {
  return (
    <Styled.Container onPress={onPress} width={width} style={{ marginHorizontal: 5 }}>
      <Image
        source={{ uri: img }}
        style={{ width: width, height: '100%' }}
        resizeMode={'cover'}
        defaultSource={assets.images.gallery_png}
      />
    </Styled.Container>
  );
};
