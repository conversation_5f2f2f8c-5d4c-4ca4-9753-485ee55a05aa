import styled from '@emotion/native';
import { Dimensions } from 'react-native';

interface IProps {
  width?: number;
}

export const Container = styled.TouchableOpacity<IProps>`
  box-sizing: border-box;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  overflow: hidden;
  height: ${() => (Dimensions.get('window').width > 600 ? `380px` : `190px`)};
  width: ${({ width }) => `${width}px`};
`;
