import React, { ReactNode } from 'react';
import { StyleProp, Text, TextStyle } from 'react-native';

import theme from '~/theme';
import { getFontSize } from '~/theme/size';

export interface ITypography extends TextStyle {
  fontSize: number;
  children: ReactNode[] | string[] | string;
  color?: string;
  onPress?: () => void;
  style?: StyleProp<TextStyle>;
  numberOfLines?: number;
}

export const Typography: React.FC<ITypography> = ({
  children,
  fontWeight,
  fontSize,
  onPress,
  style,
  color = theme.colors.black['100'],
  numberOfLines,
}) => {
  return (
    <Text
      style={[
        style,
        {
          fontSize: getFontSize(fontSize),
          fontWeight: fontWeight,
          fontFamily: theme.fonts.primary[fontWeight as string],
          color: color,
        },
      ]}
      //maxFontSizeMultiplier={1}
      numberOfLines={numberOfLines}
      onPress={onPress}>
      {children}
    </Text>
  );
};
