import styled from '@emotion/native';

import theme from '../../theme';

import { ITypography } from '~/components/Typography/Typography';

export const Text = styled.Text<ITypography>`
  // noinspection CssNoGenericFontName
  font-family: ${(props) =>
    props.fontWeight ? theme.fonts.primary[props.fontWeight] : theme.fonts.primary['400']};
  font-size: ${(props) => props.fontSize + 'px'};
  color: ${(props) => (props.color ? props.color : theme.colors.black['100'])};
  text-decoration-color: white;
`;
